# 语言学家的 AI 助手提示词库

本文档基于 [arthurlorenzi/copilot-for-linguists](https://github.com/arthurlorenzi/copilot-for-linguists) 仓库中的 `copilots-create-prompts.ipynb` 文件，提取并组织了用于语言学研究的提示词。

## 1. 建议新的词汇单元 (Suggest New Lexical Units)

这类提示词要求 AI 根据给定的语义框架（semantic frame）、框架元素（frame elements）和现有的词汇单元（lexical units），提出新的词汇单元。

**提示词模板:**

```
The semantic frame for "{frame_name}" is defined as follows: "{frame_description}".
Core frame elements in this frame are {core_fe_list}.
[Optional: Core unexpressed frame elements in this frame are {unexpressed_fe_list}.]
{fe_definitions_text}
Words evoking this frame are {existing_lus_text}.
Please propose 10 additional words/expression that evoke the "{frame_name}" semantic frame. Present them as a JSON array where each element is a word/expression.
```

**说明:**

*   `{frame_name}`: 语义框架的名称。
*   `{frame_description}`: 语义框架的定义。
*   `{core_fe_list}`: 核心框架元素的列表，例如 `"FE1", "FE2" and "FE3"`。
*   `{unexpressed_fe_list}`: (可选) 核心未表达框架元素的列表。
*   `{fe_definitions_text}`: 每个框架元素的定义，例如 `FE1: definition of FE1. FE2: definition of FE2.`
*   `{existing_lus_text}`: 已知的引发该框架的词汇单元及其词性，例如 `the noun "LU1", the verb "LU2" and the adjective "LU3"`。

**具体示例 (来自 `copilots-create-prompts.ipynb` 的 `prompt_suggest_lus` 函数逻辑):**

(假设我们有一个框架 "Activity" 的数据)
```
The semantic frame for "Activity" is defined as follows: "An event that occurs over a period of time and is characterized by a set of actions performed by an Agent.".
Core frame elements in this frame are "Agent", "Action" and "Duration".
Agent: The participant who performs the action. Action: The specific action being performed. Duration: The length of time over which the activity occurs.
Words evoking this frame are the noun "project", the verb "work" and the noun "task".
Please propose 10 additional words/expression that evoke the "Activity" semantic frame. Present them as a JSON array where each element is a word/expression.
```

## 2. 从词汇单元创建子框架 (Create Subframe from Lexical Units)

这类提示词要求 AI 基于一组给定的新词汇单元，为现有框架创建一个新的子框架。

**提示词模板:**

```
The semantic frame for "{parent_frame_name}" is defined as follows: "{parent_frame_description}".
The semantic frame for "{parent_frame_name}" has {num_core_fes} core elements: {core_fe_list}.
{fe_definitions_text}
Words evoking this frame are {existing_lus_text}.
First, propose a semantic frame evoked by words such as {new_lus_list}.
Second, please propose semantic frames for other kinds of "{parent_frame_name}".
Present them as table in which columns are "Frame Name", "Frame Definition", "Frame Elements", "Frame Element Definition" and "Words evoking the frame".
```

**说明:**

*   `{parent_frame_name}`: 父框架的名称。
*   `{parent_frame_description}`: 父框架的定义。
*   `{num_core_fes}`: 父框架核心元素的数量。
*   `{core_fe_list}`: 父框架核心元素的列表。
*   `{fe_definitions_text}`: 父框架每个核心元素的定义。
*   `{existing_lus_text}`: 已知的引发父框架的词汇单元。
*   `{new_lus_list}`: 用于定义新子框架的一组新词汇单元，例如 `"LU_A", "LU_B" and "LU_C"`。

**具体示例 (来自 `copilots-create-prompts.ipynb` 的 `prompt_create_from_lus` 函数输出):**

```
The semantic frame for "Entity" is defined as follows: "This frame is for words that denote highly schematic entities". The semantic frame for "Entity" has one core frame element: "Entity". The definition of the "Entity" frame element is as follows: "A thing (either abstract or physical) that exists with some degree of permanence". Words evoking this frame are the adverb "anything", the nouns "item", "entity", "object", "thing", "individual", "what", "material", "something", "article", "stuff", "paradox", "page", "plate", "rainbow", "trash", "waste", "label", "resource", "fuse", "grocery" and the pronoun "everything". First, propose a semantic frame evoked by words such as "god", "saint", "deity" and "goddess". Second, please propose semantic frames for other kinds of "Entity". Present them as table in which columns are "Frame Name", "Frame Definition", "Frame Elements", "Frame Element Definition" and "Words evoking the frame".
```

## 3. 从继承关系创建子框架 (Create Subframe from Inheritance)

这类提示词要求 AI 基于父框架和已知的子框架示例，提出其他可能的子框架。

**提示词模板:**

```
There is a semantic frame for "{parent_frame_name}", whose definition is as follows: "{parent_frame_description}".
Core frame elements in this frame are {parent_core_fe_list}.
[Optional: Core unexpressed frame elements in this frame are {parent_unexpressed_fe_list}.]
The "{child_frame_name}" frame inherits the "{parent_frame_name}" frame.
Core frame elements in this frame ({child_frame_name}) are {child_core_fe_list}.
Words evoking this frame ({child_frame_name}) are {child_lus_text}.
Please propose other semantic frames inheriting the "{parent_frame_name}" frame.
Present them as a table in which columns are "Frame Name", "Frame Definition", "Frame Elements", "Frame Element Definition" and "Words evoking the frame".
```

**说明:**

*   `{parent_frame_name}`: 父框架的名称。
*   `{parent_frame_description}`: 父框架的定义。
*   `{parent_core_fe_list}`: 父框架核心元素的列表。
*   `{parent_unexpressed_fe_list}`: (可选) 父框架核心未表达框架元素的列表。
*   `{child_frame_name}`: 已知的继承父框架的子框架名称。
*   `{child_core_fe_list}`: 子框架核心元素的列表 (通常与父框架相同或为其子集)。
*   `{child_lus_text}`: 已知的引发子框架的词汇单元。

**具体示例 (来自 `copilots-create-prompts.ipynb` 的 `prompt_create_from_inheritance` 函数输出):**

```
There is a semantic frame for "Intentionally act", whose definition is as follows: "This is an abstract frame for acts performed by sentient beings". The core frame element in this frame is "Agent". The core unexpressed frame element in this frame is "Act". The "Forming relationships" frame inherits the "Intentionally act" frame. The core frame element in this frame is "Agent". Words evoking this frame are the nouns "betrothal", "divorce", "marriage", "separation", "wedding", "marriage (into)" and "engagement" and the verbs "befriend", "woo", "betroth", "divorce", "leave", "marry", "marry (into)", "separate", "tie the knot" and "wed". Please propose other semantic frames inheriting the "Intentionally act" frame. Present them as a table in which columns are "Frame Name", "Frame Definition", "Frame Elements", "Frame Element Definition" and "Words evoking the frame".
```