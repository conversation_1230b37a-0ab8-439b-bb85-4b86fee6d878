<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言学家的 AI 助手提示词生成器</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: calc(100% - 22px);
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 150px;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 15px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .explanation {
            font-size: 0.9em;
            color: #555;
            margin-top: 5px;
            margin-bottom: 10px;
        }
        .code-block {
            background-color: #e9e9e9;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>语言学家的 AI 助手提示词生成器</h1>
    <p>本文档基于 <a href="https://github.com/arthurlorenzi/copilot-for-linguists" target="_blank">arthurlorenzi/copilot-for-linguists</a> 仓库中的 <code>copilots-create-prompts.ipynb</code> 文件，提取并组织了用于语言学研究的提示词。</p>

    <!-- 1. 建议新的词汇单元 -->
    <div class="container">
        <h2>1. 建议新的词汇单元 (Suggest New Lexical Units)</h2>
        <p class="explanation">这类提示词要求 AI 根据给定的语义框架（semantic frame）、框架元素（frame elements）和现有的词汇单元（lexical units），提出新的词汇单元。</p>
        
        <label for="s1_frame_name">语义框架的名称 (frame_name):</label>
        <input type="text" id="s1_frame_name" placeholder="例如: Activity">
        
        <label for="s1_frame_description">语义框架的定义 (frame_description):</label>
        <textarea id="s1_frame_description" placeholder="例如: An event that occurs over a period of time and is characterized by a set of actions performed by an Agent."></textarea>
        
        <label for="s1_core_fe_list">核心框架元素的列表 (core_fe_list):</label>
        <input type="text" id="s1_core_fe_list" placeholder='例如: "Agent", "Action" and "Duration"'>
        <p class="explanation">例如: "FE1", "FE2" and "FE3"</p>

        <label for="s1_unexpressed_fe_list">核心未表达框架元素的列表 (可选) (unexpressed_fe_list):</label>
        <input type="text" id="s1_unexpressed_fe_list" placeholder="例如: (留空则不包含此项)">
        
        <label for="s1_fe_definitions_text">每个框架元素的定义 (fe_definitions_text):</label>
        <textarea id="s1_fe_definitions_text" placeholder="例如: Agent: The participant who performs the action. Action: The specific action being performed. Duration: The length of time over which the activity occurs."></textarea>
        <p class="explanation">例如: FE1: definition of FE1. FE2: definition of FE2.</p>

        <label for="s1_existing_lus_text">已知的引发该框架的词汇单元及其词性 (existing_lus_text):</label>
        <textarea id="s1_existing_lus_text" placeholder='例如: the noun "project", the verb "work" and the noun "task"'></textarea>
        <p class="explanation">例如: the noun "LU1", the verb "LU2" and the adjective "LU3"</p>
        
        <button onclick="generatePrompt1()">生成提示词</button>
        <label for="s1_generated_prompt">生成的提示词:</label>
        <textarea id="s1_generated_prompt" readonly></textarea>
    </div>

    <!-- 2. 从词汇单元创建子框架 -->
    <div class="container">
        <h2>2. 从词汇单元创建子框架 (Create Subframe from Lexical Units)</h2>
        <p class="explanation">这类提示词要求 AI 基于一组给定的新词汇单元，为现有框架创建一个新的子框架。</p>

        <label for="s2_parent_frame_name">父框架的名称 (parent_frame_name):</label>
        <input type="text" id="s2_parent_frame_name" placeholder="例如: Entity">

        <label for="s2_parent_frame_description">父框架的定义 (parent_frame_description):</label>
        <textarea id="s2_parent_frame_description" placeholder="例如: This frame is for words that denote highly schematic entities"></textarea>

        <label for="s2_num_core_fes">父框架核心元素的数量 (num_core_fes):</label>
        <input type="text" id="s2_num_core_fes" placeholder="例如: one">

        <label for="s2_core_fe_list">父框架核心元素的列表 (core_fe_list):</label>
        <input type="text" id="s2_core_fe_list" placeholder='例如: "Entity"'>

        <label for="s2_fe_definitions_text">父框架每个核心元素的定义 (fe_definitions_text):</label>
        <textarea id="s2_fe_definitions_text" placeholder='例如: The definition of the "Entity" frame element is as follows: "A thing (either abstract or physical) that exists with some degree of permanence"'></textarea>

        <label for="s2_existing_lus_text">已知的引发父框架的词汇单元 (existing_lus_text):</label>
        <textarea id="s2_existing_lus_text" placeholder='例如: the adverb "anything", the nouns "item", "entity", ...'></textarea>

        <label for="s2_new_lus_list">用于定义新子框架的一组新词汇单元 (new_lus_list):</label>
        <input type="text" id="s2_new_lus_list" placeholder='例如: "god", "saint", "deity" and "goddess"'>
        <p class="explanation">例如: "LU_A", "LU_B" and "LU_C"</p>

        <button onclick="generatePrompt2()">生成提示词</button>
        <label for="s2_generated_prompt">生成的提示词:</label>
        <textarea id="s2_generated_prompt" readonly></textarea>
    </div>

    <!-- 3. 从继承关系创建子框架 -->
    <div class="container">
        <h2>3. 从继承关系创建子框架 (Create Subframe from Inheritance)</h2>
        <p class="explanation">这类提示词要求 AI 基于父框架和已知的子框架示例，提出其他可能的子框架。</p>

        <label for="s3_parent_frame_name">父框架的名称 (parent_frame_name):</label>
        <input type="text" id="s3_parent_frame_name" placeholder="例如: Intentionally act">

        <label for="s3_parent_frame_description">父框架的定义 (parent_frame_description):</label>
        <textarea id="s3_parent_frame_description" placeholder="例如: This is an abstract frame for acts performed by sentient beings"></textarea>

        <label for="s3_parent_core_fe_list">父框架核心元素的列表 (parent_core_fe_list):</label>
        <input type="text" id="s3_parent_core_fe_list" placeholder='例如: "Agent"'>

        <label for="s3_parent_unexpressed_fe_list">父框架核心未表达框架元素的列表 (可选) (parent_unexpressed_fe_list):</label>
        <input type="text" id="s3_parent_unexpressed_fe_list" placeholder='例如: "Act" (留空则不包含此项)'>

        <label for="s3_child_frame_name">已知的继承父框架的子框架名称 (child_frame_name):</label>
        <input type="text" id="s3_child_frame_name" placeholder="例如: Forming relationships">

        <label for="s3_child_core_fe_list">子框架核心元素的列表 (child_core_fe_list):</label>
        <input type="text" id="s3_child_core_fe_list" placeholder='例如: "Agent"'>
        <p class="explanation">通常与父框架相同或为其子集</p>

        <label for="s3_child_lus_text">已知的引发子框架的词汇单元 (child_lus_text):</label>
        <textarea id="s3_child_lus_text" placeholder='例如: the nouns "betrothal", "divorce", ... and the verbs "befriend", "woo", ...'></textarea>

        <button onclick="generatePrompt3()">生成提示词</button>
        <label for="s3_generated_prompt">生成的提示词:</label>
        <textarea id="s3_generated_prompt" readonly></textarea>
    </div>

    <script>
        function generatePrompt1() {
            const frame_name = document.getElementById('s1_frame_name').value;
            const frame_description = document.getElementById('s1_frame_description').value;
            const core_fe_list = document.getElementById('s1_core_fe_list').value;
            const unexpressed_fe_list = document.getElementById('s1_unexpressed_fe_list').value;
            const fe_definitions_text = document.getElementById('s1_fe_definitions_text').value;
            const existing_lus_text = document.getElementById('s1_existing_lus_text').value;

            let prompt = `The semantic frame for "${frame_name}" is defined as follows: "${frame_description}".\n`;
            prompt += `Core frame elements in this frame are ${core_fe_list}.\n`;
            if (unexpressed_fe_list) {
                prompt += `Core unexpressed frame elements in this frame are ${unexpressed_fe_list}.\n`;
            }
            prompt += `${fe_definitions_text}\n`;
            prompt += `Words evoking this frame are ${existing_lus_text}.\n`;
            prompt += `Please propose 10 additional words/expression that evoke the "${frame_name}" semantic frame. Present them as a JSON array where each element is a word/expression.`;

            document.getElementById('s1_generated_prompt').value = prompt;
        }

        function generatePrompt2() {
            const parent_frame_name = document.getElementById('s2_parent_frame_name').value;
            const parent_frame_description = document.getElementById('s2_parent_frame_description').value;
            const num_core_fes = document.getElementById('s2_num_core_fes').value;
            const core_fe_list = document.getElementById('s2_core_fe_list').value;
            const fe_definitions_text = document.getElementById('s2_fe_definitions_text').value;
            const existing_lus_text = document.getElementById('s2_existing_lus_text').value;
            const new_lus_list = document.getElementById('s2_new_lus_list').value;

            let prompt = `The semantic frame for "${parent_frame_name}" is defined as follows: "${parent_frame_description}".\n`;
            prompt += `The semantic frame for "${parent_frame_name}" has ${num_core_fes} core elements: ${core_fe_list}.\n`;
            prompt += `${fe_definitions_text}\n`;
            prompt += `Words evoking this frame are ${existing_lus_text}.\n`;
            prompt += `First, propose a semantic frame evoked by words such as ${new_lus_list}.\n`;
            prompt += `Second, please propose semantic frames for other kinds of "${parent_frame_name}".\n`;
            prompt += `Present them as table in which columns are "Frame Name", "Frame Definition", "Frame Elements", "Frame Element Definition" and "Words evoking the frame".`;

            document.getElementById('s2_generated_prompt').value = prompt;
        }

        function generatePrompt3() {
            const parent_frame_name = document.getElementById('s3_parent_frame_name').value;
            const parent_frame_description = document.getElementById('s3_parent_frame_description').value;
            const parent_core_fe_list = document.getElementById('s3_parent_core_fe_list').value;
            const parent_unexpressed_fe_list = document.getElementById('s3_parent_unexpressed_fe_list').value;
            const child_frame_name = document.getElementById('s3_child_frame_name').value;
            const child_core_fe_list = document.getElementById('s3_child_core_fe_list').value;
            const child_lus_text = document.getElementById('s3_child_lus_text').value;

            let prompt = `There is a semantic frame for "${parent_frame_name}", whose definition is as follows: "${parent_frame_description}".\n`;
            prompt += `Core frame elements in this frame are ${parent_core_fe_list}.\n`;
            if (parent_unexpressed_fe_list) {
                prompt += `Core unexpressed frame elements in this frame are ${parent_unexpressed_fe_list}.\n`;
            }
            prompt += `The "${child_frame_name}" frame inherits the "${parent_frame_name}" frame.\n`;
            prompt += `Core frame elements in this frame (${child_frame_name}) are ${child_core_fe_list}.\n`;
            prompt += `Words evoking this frame (${child_frame_name}) are ${child_lus_text}.\n`;
            prompt += `Please propose other semantic frames inheriting the "${parent_frame_name}" frame.\n`;
            prompt += `Present them as a table in which columns are "Frame Name", "Frame Definition", "Frame Elements", "Frame Element Definition" and "Words evoking the frame".`;

            document.getElementById('s3_generated_prompt').value = prompt;
        }
    </script>
</body>
</html>