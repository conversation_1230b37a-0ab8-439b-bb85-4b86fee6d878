import os

def generate_ffmpeg_command():
    print("FFmpeg 命令生成器")
    print(f"检测到FFmpeg安装在: D:\\Program Files (x86)\\ffmpeg-2024-08-04-git-eb3cc508d8-full_build")
    print("请按照提示输入信息，将为您生成FFmpeg命令\n")
    
    # 获取输入文件路径
    while True:
        input_file = input("请输入输入文件路径(支持mp4, mkv, avi, mov, flv, ts等格式): ").strip('"')
        if os.path.isfile(input_file):
            break
        print("错误: 文件不存在，请重新输入")
    
    # 获取输出文件路径
    while True:
        output_file = input("请输入输出文件路径(包括扩展名): ").strip('"')
        
        # 检查输出是否与输入相同
        if os.path.abspath(input_file) == os.path.abspath(output_file):
            print("错误: 输出文件不能与输入文件相同，请使用不同的文件名")
            continue
            
        output_dir = os.path.dirname(output_file)
        if not output_dir or os.path.isdir(output_dir):
            break
        print("错误: 输出目录不存在，请重新输入")
    
    # 其余代码保持不变...
    output_ext = os.path.splitext(output_file)[1].lower()
    
    print("\n请选择转码模式:")
    print("1. 保持原始质量(仅转封装)")
    print("2. 中等质量(平衡文件大小和质量)")
    print("3. 高压缩(最小文件大小)")
    print("4. 自定义参数")
    
    mode = input("请输入选项(1-4): ")
    
    ffmpeg_path = r'"D:\Program Files (x86)\ffmpeg-2024-08-04-git-eb3cc508d8-full_build\bin\ffmpeg.exe"'
    
    if mode == "1":
        cmd = f'{ffmpeg_path} -i "{input_file}" -c copy "{output_file}"'
    elif mode == "2":
        if output_ext == '.ts':
            cmd = f'{ffmpeg_path} -i "{input_file}" -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k -mpegts_copyts 1 "{output_file}"'
        else:
            cmd = f'{ffmpeg_path} -i "{input_file}" -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k "{output_file}"'
    elif mode == "3":
        if output_ext == '.ts':
            cmd = f'{ffmpeg_path} -i "{input_file}" -c:v libx265 -crf 28 -preset fast -c:a aac -b:a 96k -mpegts_copyts 1 "{output_file}"'
        else:
            cmd = f'{ffmpeg_path} -i "{input_file}" -c:v libx265 -crf 28 -preset fast -c:a aac -b:a 96k "{output_file}"'
    elif mode == "4":
        print("\n自定义参数选项:")
        video_codec = input("视频编码器(默认libx264，留空使用默认): ") or "libx264"
        crf = input("CRF值(0-51，默认23，数值越小质量越高): ") or "23"
        preset = input("预设(ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow，默认medium): ") or "medium"
        audio_codec = input("音频编码器(默认aac，留空使用默认): ") or "aac"
        audio_bitrate = input("音频比特率(如128k，默认128k): ") or "128k"
        
        extra_params = "-mpegts_copyts 1" if output_ext == '.ts' else ""
        cmd = f'{ffmpeg_path} -i "{input_file}" -c:v {video_codec} -crf {crf} -preset {preset} -c:a {audio_codec} -b:a {audio_bitrate} {extra_params} "{output_file}"'
    else:
        cmd = f'{ffmpeg_path} -i "{input_file}" -c copy "{output_file}"'
    
    print("\n生成的FFmpeg命令:")
    print(cmd)
    
    copy_to_clipboard = input("\n是否要将命令复制到剪贴板?(y/n): ").lower()
    if copy_to_clipboard == 'y':
        try:
            import pyperclip
            pyperclip.copy(cmd)
            print("命令已复制到剪贴板!")
        except:
            print("无法复制到剪贴板，请手动复制命令")
    
    run_now = input("\n是否要立即运行此命令?(y/n): ").lower()
    if run_now == 'y':
        os.system(cmd)

if __name__ == "__main__":
    generate_ffmpeg_command()
    
