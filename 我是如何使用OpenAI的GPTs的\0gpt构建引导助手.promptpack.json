{"version": "1.0", "name": "GPT构建引导助手", "description": "引导用户通过五轮对话创建自定义GPT，结构化明确目标、角色、知识、能力与风格", "prompts": [{"name": "任务目标识别", "prompt": "你希望这个GPT帮你完成什么任务？它主要产出什么样的结果？\n\n✅ 举例：\n- “我希望它每周自动分析用户反馈，生成可视化洞察报告。”\n- “我要一个像心理医生的GPT，能引导人倾诉并获得情绪纾解。”\n\n请具体描述：“我希望它能……”"}, {"name": "角色与人格设定", "prompt": "这个GPT像一个什么角色？你希望它说话的方式偏向专家、助理、老师，还是咨询师？\n\n✅ 举例：\n- “像一位经济学家，擅长逻辑推理和制度分析”\n- “像一位善于倾听的情感咨询师”\n\n请回答：“这个GPT像一位……”"}, {"name": "输入类型与知识需求", "prompt": "使用这个GPT时，你会输入哪些内容？它是否需要引用某些知识或文档来回答你？\n\n✅ 举例：\n- “我会给它一份产品用户反馈Excel，它需要从中提炼问题模式”\n- “我想让它能引用张五常的文章作为比喻”\n\n请描述：“它需要处理的输入是……”"}, {"name": "功能能力设定", "prompt": "它需要完成哪些能力？需要写文案、分类内容、分析数据、检索网页、或其他操作？\n\n✅ 举例：\n- “提取关键词 + 聚类用户问题 + 汇总成报告”\n- “检索外部信息 + 比较两个政策异同”\n\n请描述它的主要能力模块：“它需要……”"}, {"name": "输出风格与判断标准", "prompt": "你希望GPT输出的答案是什么风格？结构化？判断清晰？能自动总结？你怎么判断它算做得好？\n\n✅ 举例：\n- “它每段都要有小标题，最后要有一句判断或建议”\n- “结构要清晰，最好按Markdown段落返回”\n\n请描述：“我希望它输出时……”"}]}