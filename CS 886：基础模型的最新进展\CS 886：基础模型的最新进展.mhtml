From: <Saved by Blink>
Snapshot-Content-Location: https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/#
Subject: =?utf-8?Q?CS=20886=EF=BC=9A=E5=9F=BA=E7=A1=80=E6=A8=A1=E5=9E=8B=E7=9A=84?=
 =?utf-8?Q?=E6=9C=80=E6=96=B0=E8=BF=9B=E5=B1=95?=
Date: Wed, 12 Jun 2024 20:38:42 +0800
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--TVvrKUIs0YsrHrZDdevYX6VzSO2uNL6PjYbdkuk7RH----"


------MultipartBoundary--TVvrKUIs0YsrHrZDdevYX6VzSO2uNL6PjYbdkuk7RH----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/#

<!-- saved from url=3D(0046)https://cs.uwaterloo.ca/~jhoey/teaching/cs486/ =
--><html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; char=
set=3DUTF-8"><link rel=3D"stylesheet" type=3D"text/css" href=3D"cid:css-8bc=
<EMAIL>" />
<title _msttexthash=3D"64925445" _msthash=3D"0">CS 886=EF=BC=9A=E5=9F=BA=E7=
=A1=80=E6=A8=A1=E5=9E=8B=E7=9A=84=E6=9C=80=E6=96=B0=E8=BF=9B=E5=B1=95</titl=
e>
<meta name=3D"description" content=3D"Home page for CS 886">
<meta name=3D"author" content=3D"Wenhu Chen">
<meta http-equiv=3D"Content-Type" content=3D"text/html">
<link rel=3D"stylesheet" href=3D"https://cdn.jsdelivr.net/npm/bootstrap@4.6=
.1/dist/css/bootstrap.min.css">




<!--<link href=3D"css/teaching.css" rel=3D"stylesheet" type=3D"text/css">--=
>
</head>

<body text=3D"#000000" bgcolor=3D"#FFFFFF" link=3D"#000000" vlink=3D"#00000=
0" alink=3D"#000000" background=3D"https://cs.uwaterloo.ca/~wenhuche/teachi=
ng/cs886/css/yellow_rock.gif">
<div class=3D"container">
    <h1><large _msttexthash=3D"64925445" _msthash=3D"1">CS 886=EF=BC=9A=E5=
=9F=BA=E7=A1=80=E6=A8=A1=E5=9E=8B=E7=9A=84=E6=9C=80=E6=96=B0=E8=BF=9B=E5=B1=
=95</large></h1>
    <h2 _msttexthash=3D"4363060" _msthash=3D"2">=E5=86=AC=E5=AD=A3 2024</h2=
>
    <hr>

    <h2 _msttexthash=3D"1834014" _msthash=3D"3">=E4=BA=BA</h2>
    <ul>
      <li><font _mstmutation=3D"1" _msttexthash=3D"13376701" _msthash=3D"4"=
>=E6=95=99=E7=BB=83=EF=BC=9A</font><ul>
        <li><font _mstmutation=3D"1" _msttexthash=3D"38585352" _msthash=3D"=
5"><a href=3D"https://wenhuchen.github.io/" _mstmutation=3D"1" _istranslate=
d=3D"1">=E9=99=88=E6=96=87=E8=99=8E</a>=E5=8D=9A=E5=A3=AB=EF=BC=8C=EF=BC=88=
<tt _mstmutation=3D"1" _istranslated=3D"1">wenhuchen [at] uwaterloo [dot] c=
a</tt></font>)
        </li>
      </ul>
      </li>
      <li><font _mstmutation=3D"1" _msttexthash=3D"12264733" _msthash=3D"6"=
>=E5=8A=A9=E6=95=99=EF=BC=9A</font><ul>
          <li _msttexthash=3D"44705505" _msthash=3D"7">=E4=B8=9B=E4=BC=9F =
=EF=BC=88cong.wei [at] uwaterloo [dot] ca=EF=BC=89</li>
      </ul>
      </li>
    </ul>

    <h2 _msttexthash=3D"10453274" _msthash=3D"8">=E6=97=B6=E9=97=B4=E8=A1=
=A8</h2>
    <p><font _mstmutation=3D"1" _msttexthash=3D"60340189" _msthash=3D"9">=
=E8=AE=B2=E5=BA=A7=E6=AF=8F=E5=91=A8=E4=B8=BE=E8=A1=8C=E4=B8=80=E6=AC=A1=EF=
=BC=8C=E5=85=B7=E4=BD=93=E5=A6=82=E4=B8=8B</font><br>
    </p><ul>
    <li _msttexthash=3D"111666594" _msthash=3D"10">=E6=88=B4=E7=BB=B4=E6=96=
=AF=E4=B8=AD=E5=BF=83 2585 =E6=AF=8F=E5=91=A8=E4=B8=89=E4=B8=AD=E5=8D=88 12=
=EF=BC=9A00 =E8=87=B3=E4=B8=8B=E5=8D=88 2=EF=BC=9A50</li>
    <li _msttexthash=3D"85079033" _msthash=3D"11">=E7=AC=AC=E4=B8=80=E8=8A=
=82=EF=BC=9A=E6=98=9F=E6=9C=9F=E4=B8=89=E4=B8=AD=E5=8D=8812=EF=BC=9A00 - =
=E4=B8=8B=E5=8D=881=EF=BC=9A20</li>
    <li _msttexthash=3D"69586075" _msthash=3D"12">=E7=AC=AC=E4=BA=8C=E8=8A=
=82=EF=BC=9A=E6=98=9F=E6=9C=9F=E4=B8=89=E4=B8=8B=E5=8D=881=EF=BC=9A30 - 2=
=EF=BC=9A50</li>
    <li _msttexthash=3D"146171038" _msthash=3D"13">1 =E6=9C=88 10 =E6=97=A5=
=E5=B0=86=E7=94=B1=E6=88=91=E6=8C=87=E5=AF=BC=E3=80=82=E4=BB=A5=E4=B8=8B=E8=
=AE=B2=E5=BA=A7=E5=B0=86=E6=98=AF=E5=AD=A6=E7=94=9F=E6=BC=94=E8=AE=B2=E5=92=
=8C=E9=A1=B9=E7=9B=AE</li>
    </ul>

    <h2 _msttexthash=3D"3931148" _msthash=3D"14">=E4=BA=A4=E4=BB=98</h2>
    <ul>
    <li _msttexthash=3D"698785893" _msthash=3D"15"><strong _istranslated=3D=
"1">[1] =E8=AF=BE=E5=A0=82=E6=BC=94=E7=A4=BA</strong>=E3=80=82=E4=B8=A4=E5=
=90=8D=E5=AD=A6=E7=94=9F=E5=B0=86=E9=85=8D=E5=AF=B9=EF=BC=881=EF=BC=89=E5=
=88=9B=E5=BB=BA=E5=B9=BB=E7=81=AF=E7=89=87=EF=BC=8C=EF=BC=882=EF=BC=89=E5=
=85=B1=E5=90=8C=E5=90=91=E5=85=A8=E7=8F=AD=E5=B1=95=E7=A4=BA=EF=BC=8C=EF=BC=
=883=EF=BC=89=E5=BC=95=E5=AF=BC=E8=AE=A8=E8=AE=BA=E9=98=B6=E6=AE=B5=E3=80=
=82=E6=BC=94=E8=AE=B2=E6=97=B6=E9=97=B4=E4=B8=BA 80 =E5=88=86=E9=92=9F=E3=
=80=82</li>
    <li _msttexthash=3D"700807276" _msthash=3D"16"><strong _istranslated=3D=
"1">[2] =E9=98=85=E8=AF=BB=E7=AC=94=E8=AE=B0</strong>=E3=80=82=E5=AD=A6=E7=
=94=9F=E9=9C=80=E8=A6=81=E6=8F=90=E4=BA=A4=E4=B8=80=E4=BB=BD=E5=85=B3=E4=BA=
=8E=E4=BB=96=E4=BB=AC=E6=84=9F=E5=85=B4=E8=B6=A3=E7=9A=84=E8=BF=91=E6=9C=9F=
=E8=AE=BA=E6=96=87=E7=9A=84=E9=98=85=E8=AF=BB=E7=AC=94=E8=AE=B0=E3=80=82=E8=
=BF=99=E7=AF=87=E8=AE=BA=E6=96=87=E5=8F=AF=E4=BB=A5=E6=98=AF=E6=B7=B1=E5=BA=
=A6=E5=AD=A6=E4=B9=A0=E5=92=8C=E5=9F=BA=E7=A1=80=E6=A8=A1=E5=9E=8B=E9=A2=86=
=E5=9F=9F=E7=9A=84=E4=BB=BB=E4=BD=95=E4=B8=9C=E8=A5=BF=E3=80=82</li>
    <li _msttexthash=3D"409283108" _msthash=3D"17"><strong _istranslated=3D=
"1">[3] =E8=AF=BE=E7=A8=8B=E9=A1=B9=E7=9B=AE</strong>=E3=80=82=E5=9B=A2=E4=
=BD=93=E4=BA=BA=E6=95=B0=E6=9C=80=E5=A4=9A=E4=B8=BA2=E4=BA=BA=E3=80=82=E5=
=AD=A6=E7=94=9F=E9=9C=80=E8=A6=81=E6=8F=90=E4=BA=A4=E4=B8=80=E4=BB=BD=E9=AB=
=98=E8=B4=A8=E9=87=8F=E7=9A=84 8 =E9=A1=B5=E6=8A=A5=E5=91=8A=E4=BB=A5=E5=8F=
=8A=E6=9C=80=E7=BB=88=E7=9A=84=E8=AF=BE=E5=A0=82=E6=BC=94=E7=A4=BA=E3=80=82=
</li>
    <li _msttexthash=3D"110470607" _msthash=3D"18">=E5=90=91<a href=3D"http=
s://learn.uwaterloo.ca/d2l/home/<USER>" _istranslated=3D"1">LEARN</a>=E6=8F=
=90=E4=BA=A4=E6=BC=94=E7=A4=BA=E6=9D=90=E6=96=99=E3=80=81=E9=98=85=E8=AF=BB=
=E7=AC=94=E8=AE=B0=E5=92=8C=E9=A1=B9=E7=9B=AE=E3=80=82</li>
    </ul>

    <h2 _msttexthash=3D"5364268" _msthash=3D"19">=E8=AF=84=E4=BC=B0</h2>
    <ul>
        <li> <font _mstmutation=3D"1" _msttexthash=3D"265987371" _msthash=
=3D"20"><strong _mstmutation=3D"1" _istranslated=3D"1">[1] =E8=AF=BE=E5=A0=
=82=E6=BC=94=E8=AE=B2</strong>=EF=BC=9A40%=E3=80=82[=E9=A6=96=E6=AC=A1=E6=
=8F=90=E4=BA=A4=E6=88=AA=E6=AD=A2=E6=97=A5=E6=9C=9F=E4=B8=BA2=E6=9C=887=E6=
=97=A5=E3=80=82=E7=AC=AC=E4=BA=8C=E4=B8=AA=E6=88=AA=E6=AD=A2=E6=97=A5=E6=9C=
=9F=E4=B8=BA4=E6=9C=881=E6=97=A5=E3=80=82</font></li>
        <li> <font _mstmutation=3D"1" _msttexthash=3D"94629600" _msthash=3D=
"21"><strong _mstmutation=3D"1" _istranslated=3D"1">[2] =E9=98=85=E8=AF=BB=
=E7=AC=94=E8=AE=B0</strong>=EF=BC=9A10%=E3=80=82[=E6=88=AA=E6=AD=A2=E6=97=
=A5=E6=9C=9F=E4=B8=BA4=E6=9C=881=E6=97=A5=E3=80=82</font></li>
        <li> <font _mstmutation=3D"1" _msttexthash=3D"393436303" _msthash=
=3D"22"><strong _mstmutation=3D"1" _istranslated=3D"1">[3] =E8=AF=BE=E7=A8=
=8B=E9=A1=B9=E7=9B=AE</strong>=EF=BC=9A50%=E3=80=82[2 =E6=9C=88 28 =E6=97=
=A5=E4=B8=BA 1 =E9=A1=B5=E7=9A=84=E6=8F=90=E6=A1=88=E6=88=AA=E6=AD=A2=E6=97=
=A5=E6=9C=9F=EF=BC=8C4 =E6=9C=88 10 =E6=97=A5=E4=B8=BA 8 =E9=A1=B5=E7=9A=84=
=E6=9C=80=E7=BB=88=E6=8A=A5=E5=91=8A=E6=88=AA=E6=AD=A2=E6=97=A5=E6=9C=9F=E3=
=80=82</font></li>
    </ul>

    <h2 _msttexthash=3D"5483686" _msthash=3D"23">=E9=80=9A=E4=BF=A1</h2>
    <ul>
      <li _msttexthash=3D"86103069" _msthash=3D"24">=E6=89=80=E6=9C=89=E6=
=B2=9F=E9=80=9A=E9=83=BD=E5=BA=94=E4=BD=BF=E7=94=A8<a href=3D"https://piazz=
a.com/uwaterloo.ca/winter2024/cs886" _istranslated=3D"1">Piazza=E8=AE=A8=E8=
=AE=BA=E6=9D=BF=E8=BF=9B=E8=A1=8C=E3=80=82</a></li>
      <li _msttexthash=3D"214200064" _msthash=3D"25">=E6=88=91=E4=BB=AC=E4=
=B8=8D=E4=BC=9A=E5=B0=86=E6=9D=90=E6=96=99=E6=88=96=E4=BD=9C=E4=B8=9A=E4=B8=
=8A=E4=BC=A0=E5=88=B0 Piazza=EF=BC=8C=E7=9B=B8=E5=8F=8D=EF=BC=8C=E8=BF=99=
=E4=BA=9B=E6=9D=90=E6=96=99=E5=B0=86=E5=87=BA=E7=8E=B0=E5=9C=A8 LEARN =E4=
=B8=8A</li>
      <li _msttexthash=3D"122127018" _msthash=3D"26"><a href=3D"https://pia=
zza.com/uwaterloo.ca/winter2024/cs886" _istranslated=3D"1">=E5=9C=A8=E8=BF=
=99=E9=87=8C</a>=E6=B3=A8=E5=86=8C Piazza=EF=BC=88=E5=A6=82=E6=9E=9C=E6=82=
=A8=E8=BF=98=E6=B2=A1=E6=9C=89=E6=B3=A8=E5=86=8C=EF=BC=89=E3=80=82</li>
      <li _msttexthash=3D"611610883" _msthash=3D"27">=E5=85=AC=E5=85=B1=E5=
=B9=BF=E5=9C=BA=E5=B8=96=E5=AD=90=EF=BC=88=E5=8F=AF=E4=BB=A5=E6=98=AF=E5=8C=
=BF=E5=90=8D=E7=9A=84=EF=BC=89=E6=98=AF=E6=9C=89=E5=85=B3=E8=AF=BE=E7=A8=8B=
=E6=9D=90=E6=96=99=E7=AD=89=E9=97=AE=E9=A2=98=E7=9A=84=E9=A6=96=E9=80=89=E6=
=96=B9=E6=B3=95=E3=80=82=E7=84=B6=E5=90=8E=EF=BC=8C=E5=AD=A6=E7=94=9F=E5=8F=
=AF=E4=BB=A5=E4=BA=92=E7=9B=B8=E5=B8=AE=E5=8A=A9=EF=BC=8C=E6=95=99=E5=B8=88=
=E5=8F=AF=E4=BB=A5=E9=98=85=E8=AF=BB/=E5=9B=9E=E5=A4=8D=E3=80=82</li><li _m=
sttexthash=3D"284584430" _msthash=3D"28">=E7=A7=81=E4=BA=BA=E5=B9=BF=E5=9C=
=BA=E5=B8=96=E5=AD=90=EF=BC=88=E4=BB=85=E9=99=90=E6=95=99=E5=B8=88=EF=BC=89=
=E5=8F=AF=E7=94=A8=E4=BA=8E=E4=BB=BB=E4=BD=95=E5=8C=85=E5=90=AB=E8=A7=A3=E5=
=86=B3=E6=96=B9=E6=A1=88=E7=89=87=E6=AE=B5=E6=88=96=E7=A7=81=E4=BA=BA=E9=97=
=AE=E9=A2=98=E7=9A=84=E5=B8=96=E5=AD=90=E3=80=82</li>
      <li _msttexthash=3D"291598710" _msthash=3D"29">=E5=8F=AA=E6=9C=89=E5=
=9C=A8=E7=89=B9=E6=AE=8A=E6=83=85=E5=86=B5=E4=B8=8B=EF=BC=8C=E6=82=A8<b _is=
translated=3D"1">=E5=8F=AA=E9=9C=80=E8=A6=81</b>=E8=81=94=E7=B3=BB=E6=95=99=
=E5=B8=88=EF=BC=8C=E6=82=A8=E6=89=8D=E5=BA=94=E8=AF=A5=E4=BD=BF=E7=94=A8=E4=
=B8=8A=E9=9D=A2=E7=9A=84=E4=B8=AA=E4=BA=BA=E7=94=B5=E5=AD=90=E9=82=AE=E4=BB=
=B6=E3=80=82</li>
      </ul>

   =20
    <h2 _msttexthash=3D"6718387" _msthash=3D"30">=E9=A1=B9=E7=9B=AE</h2>
    <ul>
    <li _msttexthash=3D"162870981" _msthash=3D"31">=E8=AF=A5=E9=A1=B9=E7=9B=
=AE=E9=9C=80=E8=A6=81=E4=B8=8E=E6=96=87=E6=9C=AC=E6=88=96=E8=A7=86=E8=A7=89=
=E6=88=96=E5=A4=9A=E6=A8=A1=E6=80=81=E6=A8=A1=E5=9E=8B=E7=AD=89=E5=9F=BA=E7=
=A1=80=E6=A8=A1=E5=9E=8B=E7=9B=B8=E5=85=B3=E3=80=82</li>
    <li _msttexthash=3D"265080868" _msthash=3D"32">=E9=A1=B9=E7=9B=AE=E9=9C=
=80=E8=A6=81=E5=8C=85=E5=90=AB=E4=BB=A5=E4=B8=8B=E9=83=A8=E5=88=86=EF=BC=9A=
=E5=BC=95=E8=A8=80=E3=80=81=E9=97=AE=E9=A2=98=E5=AE=9A=E4=B9=89=E3=80=81=E7=
=AE=97=E6=B3=95=E8=AE=BE=E8=AE=A1=E3=80=81=E5=AE=9E=E9=AA=8C=E3=80=81=E8=AF=
=84=E4=BC=B0=E3=80=81=E7=BB=93=E8=AE=BA=E3=80=82</li>
    <li _msttexthash=3D"96792800" _msthash=3D"33">=E8=AF=B7=E4=BD=BF=E7=94=
=A8<a href=3D"https://neurips.cc/Conferences/2023/PaperInformation/StyleFil=
es" _istranslated=3D"1">=E4=B9=B3=E8=83=B6=E6=A8=A1=E6=9D=BF</a>=E4=BD=9C=
=E4=B8=BA=E6=A8=A1=E6=9D=BF=E6=9D=A5=E6=92=B0=E5=86=99=E6=9C=80=E7=BB=88=E6=
=8A=A5=E5=91=8A=E3=80=82</li><li><strong _msttexthash=3D"364379197" _msthas=
h=3D"34">=E8=AF=B7=E7=A1=AE=E4=BF=9D=E4=BB=A3=E7=A0=81=E4=B8=8D=E6=98=AF=E4=
=BB=8E=E5=85=AC=E5=85=B1=E5=AD=98=E5=82=A8=E5=BA=93=E5=A4=8D=E5=88=B6=E7=9A=
=84=E3=80=82=E4=BB=BB=E4=BD=95=E8=BF=9D=E8=A7=84=E8=A1=8C=E4=B8=BA=E9=83=BD=
=E5=B0=86=E8=A2=AB=E8=A7=86=E4=B8=BA=E6=8A=84=E8=A2=AD=EF=BC=8C=E5=B9=B6=E9=
=80=A0=E6=88=90=E4=B8=A5=E9=87=8D=E5=90=8E=E6=9E=9C=E3=80=82</strong></li>
    </ul>

    <hr>
</div>


<div class=3D"container">

<h2 _msttexthash=3D"5774366" _msthash=3D"35">=E8=AE=B2=E5=BA=A7</h2>
<table style=3D"width:100%">
  <tbody>
  <tr>
    <th _msttexthash=3D"12375194" _msthash=3D"36">=E8=AE=B2=E5=BA=A7=E7=BC=
=96=E5=8F=B7</th>
    <th _msttexthash=3D"5885113" _msthash=3D"37">=E4=B8=BB=E9=A2=98</th>
    <th class=3D"table-cell-no-wrap table-width-fixed" _msttexthash=3D"5119=
231" _msthash=3D"38">=E6=97=A5=E6=9C=9F</th>
    <th class=3D"table-cell-no-wrap table-width-fixed" _msttexthash=3D"8617=
284" _msthash=3D"39">=E5=B9=BB=E7=81=AF=E7=89=87</th>
    <th _msttexthash=3D"5334199" _msthash=3D"40">=E5=BC=95=E7=94=A8</th>
  </tr>

  <tr>
    <td colspan=3D"5" style=3D"text-align: center">
    <h4 _msttexthash=3D"19011005" _msthash=3D"41">=E5=9F=BA=E7=A1=80=E6=A8=
=A1=E5=9E=8B=E7=AE=80=E4=BB=8B</h4>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"4459" _msthash=3D"42">1</td>
    <td _msttexthash=3D"27214187" _msthash=3D"43">=E5=9F=BA=E7=A1=80=E6=A8=
=A1=E5=9E=8B=E5=8F=8A=E5=85=B6=E5=BA=94=E7=94=A8</td>
    <td _msttexthash=3D"6489691" _msthash=3D"44">1=E6=9C=8810=E6=97=A5</td>
    <td><a href=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L1.pptx=
" _msttexthash=3D"8617284" _msthash=3D"45">=E5=B9=BB=E7=81=AF=E7=89=87</a><=
/td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/abs/2108.07258" _msttexthash=3D"52=
399672" _msthash=3D"46">=E8=AE=BA=E5=9F=BA=E7=A1=80=E6=A8=A1=E5=9E=8B=E7=9A=
=84=E6=9C=BA=E9=81=87=E4=B8=8E=E9=A3=8E=E9=99=A9</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2309.10020.pdf" _msttexthash=
=3D"83076513" _msthash=3D"47">=E5=A4=9A=E6=A8=A1=E5=BC=8F=E5=9F=BA=E7=A1=80=
=E6=A8=A1=E5=9E=8B=EF=BC=9A=E4=BB=8E=E4=B8=93=E5=AE=B6=E5=88=B0=E9=80=9A=E7=
=94=A8=E5=8A=A9=E6=89=8B</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2306.14895.pdf" _msttexthash=
=3D"90350910" _msthash=3D"48">=E5=A4=A7=E5=9E=8B=E5=A4=9A=E6=A8=A1=E5=BC=8F=
=E6=A8=A1=E5=9E=8B=EF=BC=9ACVPR 2023 =E6=95=99=E7=A8=8B=E6=B3=A8=E6=84=8F=
=E4=BA=8B=E9=A1=B9</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2307.14334.pdf" _msttexthash=
=3D"29919123" _msthash=3D"49">=E8=BF=88=E5=90=91=E9=80=9A=E6=89=8D=E7=94=9F=
=E7=89=A9=E5=8C=BB=E5=AD=A6 AI</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2302.09419.pdf" _msttexthash=
=3D"108300413" _msthash=3D"50">=E9=A2=84=E8=AE=AD=E7=BB=83=E5=9F=BA=E7=A1=
=80=E6=A8=A1=E5=9E=8B=E7=BB=BC=E5=90=88=E8=B0=83=E6=9F=A5=EF=BC=9A=E4=BB=8E=
 BERT =E5=88=B0 ChatGPT =E7=9A=84=E5=8E=86=E5=8F=B2</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.13246.pdf" _msttexthash=
=3D"36736843" _msthash=3D"51">=E4=BA=A4=E4=BA=92=E5=BC=8F=E8=87=AA=E7=84=B6=
=E8=AF=AD=E8=A8=80=E5=A4=84=E7=90=86</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2212.10403.pdf" _msttexthash=
=3D"103458914" _msthash=3D"52">=E8=BF=88=E5=90=91=E5=A4=A7=E5=9E=8B=E8=AF=
=AD=E8=A8=80=E6=A8=A1=E5=9E=8B=E4=B8=AD=E7=9A=84=E6=8E=A8=E7=90=86=EF=BC=9A=
=E4=B8=80=E9=A1=B9=E8=B0=83=E6=9F=A5</a></li>
      </ul>
    </td>
  </tr>
  <tr>
    <td _msttexthash=3D"4550" _msthash=3D"53">2</td>
    <td _msttexthash=3D"13573313" _msthash=3D"54">=E8=AF=BE=E7=A8=8B=E7=89=
=A9=E6=B5=81</td>
    <td _msttexthash=3D"6489691" _msthash=3D"55">1=E6=9C=8810=E6=97=A5</td>
    <td><a href=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/#"></a>=
</td>
    <td>
      <ul>
        <li _msttexthash=3D"13080158" _msthash=3D"56">=E5=BB=BA=E7=AB=8B=E5=
=9B=A2=E9=98=9F</li>
        <li _msttexthash=3D"17156516" _msthash=3D"57">=E5=B9=BB=E7=81=AF=E7=
=89=87=E8=A6=81=E6=B1=82</li>
        <li _msttexthash=3D"14439724" _msthash=3D"58">=E9=A1=B9=E7=9B=AE=E8=
=A6=81=E6=B1=82</li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"4641" _msthash=3D"59">3</td>
    <td _msttexthash=3D"8049899" _msthash=3D"60">RNN &amp; CNN=E5=85=AC=E5=
=8F=B8</td>
    <td _msttexthash=3D"6490601" _msthash=3D"61">1=E6=9C=8817=E6=97=A5</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"62"><a hr=
ef=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L3.pptx" _mstmutatio=
n=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/mUdj_4jRaDU" _mstmutation=3D"1">Video</a><=
/font><br>
   </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1912.05911.pdf" _msttexthash=
=3D"128603462" _msthash=3D"63">=E9=80=92=E5=BD=92=E7=A5=9E=E7=BB=8F=E7=BD=
=91=E7=BB=9C =EF=BC=88RNN=EF=BC=89=EF=BC=9A=E6=B8=A9=E5=92=8C=E7=9A=84=E4=
=BB=8B=E7=BB=8D=E5=92=8C=E6=A6=82=E8=BF=B0</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1505.00387.pdf" _msttexthash=
=3D"9489025" _msthash=3D"64">=E5=85=AC=E8=B7=AF=E7=BD=91</a></li>
        <li><a href=3D"https://www.bioinf.jku.at/publications/older/2604.pd=
f" _msttexthash=3D"17919122" _msthash=3D"65">=E9=95=BF=E7=9F=AD=E6=9C=9F=E8=
=AE=B0=E5=BF=86</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1412.3555.pdf" _msttexthash=3D=
"108851184" _msthash=3D"66">=E9=97=A8=E6=8E=A7=E5=BE=AA=E7=8E=AF=E7=A5=9E=
=E7=BB=8F=E7=BD=91=E7=BB=9C=E5=9C=A8=E5=BA=8F=E5=88=97=E5=BB=BA=E6=A8=A1=E4=
=B8=8A=E7=9A=84=E5=AE=9E=E8=AF=81=E8=AF=84=E4=BC=B0</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1508.04025.pdf" _msttexthash=
=3D"2933957" _msthash=3D"67">Effective Approaches to Attention-based Neural=
 Machine Translation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1511.08458.pdf" _msttexthash=
=3D"1669941" _msthash=3D"68">An Introduction to Convolutional Neural Networ=
ks</a></li>
        <li><a href=3D"https://proceedings.neurips.cc/paper_files/paper/201=
2/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf" _msttexthash=3D"2731833"=
 _msthash=3D"69">ImageNet Classification with Deep Convolutional Neural Net=
works</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1505.04597.pdf" _msttexthash=
=3D"2682394" _msthash=3D"70">U-Net: Convolutional Networks for Biomedical I=
mage Segmentation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1512.03385.pdf" _msttexthash=
=3D"1363024" _msthash=3D"71">Deep Residual Learning for Image Recognition</=
a></li>
        <li><a href=3D"https://arxiv.org/pdf/1608.06993.pdf" _msttexthash=
=3D"1281228" _msthash=3D"72">Densely Connected Convolutional Networks</a></=
li>
        <li><a href=3D"https://arxiv.org/pdf/1611.05431.pdf" _msttexthash=
=3D"2472847" _msthash=3D"73">Aggregated Residual Transformations for Deep N=
eural Networks</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2201.03545.pdf" _msttexthash=
=3D"349336" _msthash=3D"74">A ConvNet for the 2020s</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"4732" _msthash=3D"75">4</td>
    <td _msttexthash=3D"52299" _msthash=3D"76">NLP &amp; CV</td>
    <td _msttexthash=3D"79599" _msthash=3D"77">Jan 17th</td>
    <td><a href=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L4.pptx=
" _msttexthash=3D"76453" _msthash=3D"78">Slides</a></td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1409.3215.pdf" _msttexthash=3D=
"1721200" _msthash=3D"79">Sequence to Sequence Learning with Neural Network=
s</a></li>
        <li><a href=3D"https://aclanthology.org/W02-1011.pdf" _msttexthash=
=3D"3143764" _msthash=3D"80">Thumbs up? Sentiment Classification using Mach=
ine Learning Techniques</a></li>
        <li><a href=3D"https://nlp.cs.nyu.edu/sekine/papers/li07.pdf" _mstt=
exthash=3D"2032043" _msthash=3D"81">A survey of named entity recognition an=
d classification</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1506.03340.pdf" _msttexthash=
=3D"1117324" _msthash=3D"82">Teaching Machines to Read and Comprehend</a></=
li>
        <li><a href=3D"https://static.googleusercontent.com/media/research.=
google.com/en//pubs/archive/38131.pdf" _msttexthash=3D"2693366" _msthash=3D=
"83">Deep neural networks for acoustic modeling in speech recognition</a></=
li>
        <li><a href=3D"https://arxiv.org/pdf/1509.00685.pdf" _msttexthash=
=3D"1787565" _msthash=3D"84">A Neural Attention Model for Sentence Summariz=
ation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1405.0312.pdf" _msttexthash=3D=
"1164059" _msthash=3D"85">Microsoft COCO: Common Objects in Context</a></li=
>
        <li><a href=3D"https://arxiv.org/pdf/1311.2524.pdf" _msttexthash=3D=
"4123730" _msthash=3D"86">Rich feature hierarchies for accurate object dete=
ction and semantic segmentation</a>
        </li><li><a href=3D"https://arxiv.org/pdf/1411.4038.pdf" _msttextha=
sh=3D"2092025" _msthash=3D"87">Fully Convolutional Networks for Semantic Se=
gmentation</a></li>
        <li><a href=3D"https://www.cs.toronto.edu/~ranzato/publications/tai=
gman_cvpr14.pdf" _msttexthash=3D"3241277" _msthash=3D"88">DeepFace: Closing=
 the Gap to Human-Level Performance in Face Verification</a></li>
        <li><a href=3D"https://ieeexplore.ieee.org/document/6909610" _mstte=
xthash=3D"2046538" _msthash=3D"89">DeepPose: Human Pose Estimation via Deep=
 Neural Networks</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1511.06434v2.pdf" _msttexthash=
=3D"5572060" _msthash=3D"90">Unsupervised Representation Learning with Deep=
 Convolutional Generative Adversarial Networks</a></li>=20
      </ul>
    </td>
  </tr>

  <tr>
    <td colspan=3D"5" style=3D"text-align: center">
    <h4 _msttexthash=3D"570219" _msthash=3D"91">Transformer Architecture</h=
4>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"4823" _msthash=3D"92">5</td>
    <td _msttexthash=3D"686569" _msthash=3D"93">Self Attention &amp; Transf=
ormers</td>
    <td _msttexthash=3D"79300" _msthash=3D"94">Jan 24th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"95"><a hr=
ef=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L5.pptx" _mstmutatio=
n=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/TZa8ozmm6xo" _mstmutation=3D"1">Video</a><=
/font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1409.0473.pdf" _msttexthash=3D=
"2966691" _msthash=3D"96">Neural Machine Translation by Jointly Learning to=
 Align and Translate</a></li>
        <li><a href=3D"https://proceedings.mlr.press/v37/xuc15.html" _mstte=
xthash=3D"3511781" _msthash=3D"97">Show, Attend and Tell: Neural Image Capt=
ion Generation with Visual Attention</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1706.03762.pdf" _msttexthash=
=3D"465894" _msthash=3D"98">Attention Is All You Need</a></li>
        <li><a href=3D"https://nlp.seas.harvard.edu/annotated-transformer/"=
 _msttexthash=3D"455338" _msthash=3D"99">Annotated Transformer</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1802.05751.pdf" _msttexthash=
=3D"317746" _msthash=3D"100">Image Transformer</a></li>
        <li><a href=3D"https://openreview.net/forum?id=3DYicbFdNTTy" _mstte=
xthash=3D"3154723" _msthash=3D"101">An Image is Worth 16x16 Words: Transfor=
mers for Image Recognition at Scale</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"4914" _msthash=3D"102">6</td>
    <td _msttexthash=3D"494559" _msthash=3D"103">Efficient Transformers</td=
>
    <td _msttexthash=3D"79300" _msthash=3D"104">Jan 24th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"105"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L6.pptx" _mstmutati=
on=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/VHgzfqbicog" _mstmutation=3D"1">Video1</a>=
<br _mstmutation=3D"1">
	    <a href=3D"https://drive.google.com/file/d/1-26T80XI68DJlCadCOWFwwL6uO=
5o4GPu/view" _mstmutation=3D"1">Video2</a></font>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2006.16236.pdf" _msttexthash=
=3D"3862976" _msthash=3D"106">Transformers are RNNs: Fast Autoregressive Tr=
ansformers with Linear Attention</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2103.03206.pdf" _msttexthash=
=3D"2074358" _msthash=3D"107">Perceiver: General Perception with Iterative =
Attention</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2103.02143.pdf" _msttexthash=
=3D"525486" _msthash=3D"108">Random Feature Attention</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2004.05150.pdf" _msttexthash=
=3D"1292018" _msthash=3D"109">Longformer: The Long-Document Transformer</a>=
</li>
        <li><a href=3D"https://arxiv.org/pdf/1904.10509.pdf" _msttexthash=
=3D"1799980" _msthash=3D"110">Generating Long Sequences with Sparse Transfo=
rmers</a>
        </li><li><a href=3D"https://arxiv.org/pdf/2006.04768.pdf" _msttexth=
ash=3D"1697956" _msthash=3D"111">Linformer: Self-Attention with Linear Comp=
lexity</a></li>
        <!--<li><a href=3D"https://arxiv.org/pdf/2105.03824.pdf">FNet: Mixi=
ng Tokens with Fourier Transforms</a></li>-->
        <!--<li><a href=3D"https://arxiv.org/pdf/2004.08483.pdf">ETC: Encod=
ing Long and Structured Inputs in Transformers</a></li>-->
        <!--<li><a href=3D"https://arxiv.org/pdf/2312.00752.pdf">Mamba: Lin=
ear-Time Sequence Modeling with Selective State Spaces</a>-->
        <li><a href=3D"https://arxiv.org/pdf/2111.00396.pdf" _msttexthash=
=3D"2715739" _msthash=3D"112">Efficiently Modeling Long Sequences with Stru=
ctured State Spaces</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"5005" _msthash=3D"113">7</td>
    <td _msttexthash=3D"635687" _msthash=3D"114">Parameter-efficient Tuning=
</td>
    <td _msttexthash=3D"80873" _msthash=3D"115">Jan 31st</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"116"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L7.pptx" _mstmutati=
on=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/mUdj_4jRaDU" _mstmutation=3D"1">Video</a><=
/font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1902.00751.pdf" _msttexthash=
=3D"1439542" _msthash=3D"117">Parameter-Efficient Transfer Learning for NLP=
</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2106.09685.pdf" _msttexthash=
=3D"1633307" _msthash=3D"118">LoRA: Low-Rank Adaptation of Large Language M=
odels</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2104.08691.pdf" _msttexthash=
=3D"2029534" _msthash=3D"119">The Power of Scale for Parameter-Efficient Pr=
ompt Tuning</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2009.07118.pdf" _msttexthash=
=3D"3670082" _msthash=3D"120">It's Not Just Size That Matters: Small Langua=
ge Models Are Also Few-Shot Learners</a></li>
        <li><a href=3D"https://aclanthology.org/2021.acl-long.295.pdf" _mst=
texthash=3D"2304367" _msthash=3D"121">Making Pre-trained Language Models Be=
tter Few-shot Learners</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2205.05638.pdf" _msttexthash=
=3D"4634409" _msthash=3D"122">Few-Shot Parameter-Efficient Fine-Tuning is B=
etter and Cheaper than In-Context Learning</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2110.04366.pdf" _msttexthash=
=3D"2562196" _msthash=3D"123">Towards a Unified View of Parameter-Efficient=
 Transfer Learning</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"5096" _msthash=3D"124">8</td>
    <td _msttexthash=3D"597974" _msthash=3D"125">Language Model Pretraining=
</td>
    <td _msttexthash=3D"79001" _msthash=3D"126">Jan 31th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"127"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L8.pptx" _mstmutati=
on=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DlGF03oNwglo" _mstmutation=
=3D"1">Video1</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/o7CZc6zozAo" _mstmutation=3D"1">Video2</a>=
</font><br>
    </td><td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1802.05365.pdf" _msttexthash=
=3D"1308671" _msthash=3D"128">Deep contextualized word representations</a><=
/li>
        <li><a href=3D"https://arxiv.org/pdf/1810.04805.pdf" _msttexthash=
=3D"4128033" _msthash=3D"129">BERT: Pre-training of Deep Bidirectional Tran=
sformers for Language Understanding</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1905.03197.pdf" _msttexthash=
=3D"4539093" _msthash=3D"130">Unified Language Model Pre-training for Natur=
al Language Understanding and Generation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1909.11942.pdf" _msttexthash=
=3D"3572829" _msthash=3D"131">ALBERT: A Lite BERT for Self-supervised Learn=
ing of Language Representations</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1907.11692.pdf" _msttexthash=
=3D"1981707" _msthash=3D"132">RoBERTa: A Robustly Optimized BERT Pretrainin=
g Approach</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1905.07129.pdf" _msttexthash=
=3D"2882139" _msthash=3D"133">ERNIE: Enhanced Language Representation with =
Informative Entities</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2003.10555.pdf" _msttexthash=
=3D"3689595" _msthash=3D"134">ELECTRA: Pre-training Text Encoders as Discri=
minators Rather Than Generators</a></li>
      </ul>
    </td>
  </tr>
 =20
  <tr>
    <td colspan=3D"5" style=3D"text-align: center">
    <h4 _msttexthash=3D"401544" _msthash=3D"135">Large Language Models</h4>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"5187" _msthash=3D"136">9</td>
    <td _msttexthash=3D"401544" _msthash=3D"137">Large Language Models</td>
    <td _msttexthash=3D"68302" _msthash=3D"138">Feb 7th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"139"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L9.pptx" _mstmutati=
on=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/WuEhDi4GLnI" _mstmutation=3D"1">Video</a><=
/font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1910.10683.pdf" _msttexthash=
=3D"3994653" _msthash=3D"140">Exploring the Limits of Transfer Learning wit=
h a Unified Text-to-Text Transformer</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2005.14165.pdf" _msttexthash=
=3D"995124" _msthash=3D"141">Language Models are Few-Shot Learners</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2201.08239.pdf" _msttexthash=
=3D"1466192" _msthash=3D"142">LaMDA: Language Models for Dialog Application=
s</a></li>
        <li><a href=3D"https://d4mucfpksywv.cloudfront.net/better-language-=
models/language_models_are_unsupervised_multitask_learners.pdf" _msttexthas=
h=3D"1860495" _msthash=3D"143">Language Models are Unsupervised Multitask L=
earners</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2107.03374.pdf" _msttexthash=
=3D"1522443" _msthash=3D"144">Evaluating Large Language Models Trained on C=
ode</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2204.02311.pdf" _msttexthash=
=3D"1425619" _msthash=3D"145">PaLM: Scaling Language Modeling with Pathways=
</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2307.09288.pdf" _msttexthash=
=3D"1610245" _msthash=3D"146">Llama 2: Open Foundation and Fine-Tuned Chat =
Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2401.04088.pdf" _msttexthash=
=3D"323050" _msthash=3D"147">Mixtral of Experts</a></li>
      </ul>
    </td>
  </tr>


  <tr>
    <td _msttexthash=3D"9451" _msthash=3D"148">10</td>
    <td _msttexthash=3D"150319" _msthash=3D"149">Scaling Law </td>
    <td _msttexthash=3D"68302" _msthash=3D"150">Feb 7th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"151"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L10.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/_09CeX44tqg?si=3DX2fac1pNn3EKjEDO" _mstmut=
ation=3D"1">Video</a></font><br>
    </td>
      <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2001.08361.pdf" _msttexthash=
=3D"1064908" _msthash=3D"152">Scaling Laws for Neural Language Models</a></=
li>
        <li><a href=3D"https://arxiv.org/pdf/2102.01293.pdf" _msttexthash=
=3D"524316" _msthash=3D"153">Scaling Laws for Transfer</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2206.07682.pdf" _msttexthash=
=3D"1284361" _msthash=3D"154">Emergent Abilities of Large Language Models</=
a></li>
        <li><a href=3D"https://arxiv.org/pdf/2203.15556.pdf" _msttexthash=
=3D"1517672" _msthash=3D"155">Training Compute-Optimal Large Language Model=
s</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2210.11399.pdf" _msttexthash=
=3D"1526421" _msthash=3D"156">Transcending Scaling Laws with 0.1% Extra Com=
pute</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2211.02011.pdf" _msttexthash=
=3D"893308" _msthash=3D"157">Inverse scaling can become U-shaped</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2304.15004.pdf" _msttexthash=
=3D"1970423" _msthash=3D"158">Are Emergent Abilities of Large Language Mode=
ls a Mirage?</a></li>
        </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9555" _msthash=3D"159">11</td>
    <td _msttexthash=3D"469911" _msthash=3D"160">Instruction Tuning &amp; R=
LHF</td>
    <td _msttexthash=3D"77818" _msthash=3D"161">Feb 14th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"537160" _msthash=3D"162"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L11.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/ks2X_mem_DE" _mstmutation=3D"1">Video1</a>=
<br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/q42aCoDD0lo" _mstmutation=3D"1">Video2</a>=
<br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/jU59t-foUxU" _mstmutation=3D"1">Video3</a>=
</font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2203.02155.pdf" _msttexthash=
=3D"2958280" _msthash=3D"163">Training language models to follow instructio=
ns with human feedback</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2109.01652.pdf" _msttexthash=
=3D"1578915" _msthash=3D"164">Finetuned Language Models Are Zero-Shot Learn=
ers</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2110.08207.pdf" _msttexthash=
=3D"2839590" _msthash=3D"165">Multitask Prompted Training Enables Zero-Shot=
 Task Generalization</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.11206.pdf" _msttexthash=
=3D"705900" _msthash=3D"166">LIMA: Less Is More for Alignment</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.18290.pdf" _msttexthash=
=3D"3677102" _msthash=3D"167">Direct Preference Optimization: Your Language=
 Model is Secretly a Reward Model</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2310.16944.pdf" _msttexthash=
=3D"1285973" _msthash=3D"168">Zephyr: Direct Distillation of LM Alignment</=
a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9659" _msthash=3D"169">12</td>
    <td _msttexthash=3D"426452" _msthash=3D"170">Efficient LLM Training</td=
>
    <td _msttexthash=3D"79690" _msthash=3D"171">Feb 14st</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"172"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L12.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3Dv1ekwGFksrY" _mstmutation=
=3D"1">Video1</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/tutGtRkNHnw" _mstmutation=3D"1">Video2</a>=
</font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/1909.08053.pdf" _msttexthash=
=3D"4575324" _msthash=3D"173">Megatron-LM: Training Multi-Billion Parameter=
 Language Models Using Model Parallelism</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1910.02054.pdf" _msttexthash=
=3D"3055897" _msthash=3D"174">ZeRO: Memory Optimizations Toward Training Tr=
illion Parameter Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2108.12409.pdf" _msttexthash=
=3D"4550767" _msthash=3D"175">Train Short, Test Long: Attention with Linear=
 Biases Enables Input Length Extrapolation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2104.09864.pdf" _msttexthash=
=3D"2530398" _msthash=3D"176">RoFormer: Enhanced Transformer with Rotary Po=
sition Embedding</a></li>
        <li><a href=3D"https://arxiv.org/pdf/1911.02150.pdf" _msttexthash=
=3D"1938859" _msthash=3D"177">Fast Transformer Decoding: One Write-Head is =
All You Need</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.13245v2.pdf" _msttexthash=
=3D"4482036" _msthash=3D"178">GQA: Training Generalized Multi-Query Transfo=
rmer Models from Multi-Head Checkpoints</a>
        </li><li><a href=3D"https://arxiv.org/pdf/2205.14135.pdf" _msttexth=
ash=3D"3605160" _msthash=3D"179">FlashAttention: Fast and Memory-Efficient =
Exact Attention with IO-Awareness</a>
      </li></ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9763" _msthash=3D"180">13</td>
    <td _msttexthash=3D"456963" _msthash=3D"181">Efficient LLM Inference</t=
d>
    <td _msttexthash=3D"78520" _msthash=3D"182">Feb 28th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"183"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L13.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DRfD5tPoMnZY" _mstmutation=
=3D"1">Video1</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DyVXtLTcdO1Q" _mstmutation=
=3D"1">Video2</a></font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2006.04152.pdf" _msttexthash=
=3D"2329821" _msthash=3D"184">BERT Loses Patience: Fast and Robust Inferenc=
e with Early Exit</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2207.07061.pdf" _msttexthash=
=3D"1008865" _msthash=3D"185">Confident Adaptive Language Modeling</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2211.17192.pdf" _msttexthash=
=3D"2214953" _msthash=3D"186">Fast Inference from Transformers via Speculat=
ive Decoding</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2309.06180.pdf" _msttexthash=
=3D"4004962" _msthash=3D"187">Efficient Memory Management for Large Languag=
e Model Serving with PagedAttention</a></li>
        <li><a href=3D"https://pytorch.org/blog/flash-decoding/" _msttextha=
sh=3D"1304121" _msthash=3D"188">Flash-Decoding for long-context inference</=
a></li>
        <li><a href=3D"https://lmsys.org/blog/2023-11-21-lookahead-decoding=
/" _msttexthash=3D"3511261" _msthash=3D"189">Breaking the Sequential Depend=
ency of LLM Inference Using Lookahead Decoding</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9867" _msthash=3D"190">14</td>
    <td _msttexthash=3D"500526" _msthash=3D"191">Compress and Sparsify LLM<=
/td>
    <td _msttexthash=3D"78520" _msthash=3D"192">Feb 28th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"193"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L14.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DhoBIQIvPcI0" _mstmutation=
=3D"1">Video</a></font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2112.10684.pdf" _msttexthash=
=3D"2637440" _msthash=3D"194">Efficient Large Scale Language Modeling with =
Mixtures of Experts</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2006.16668.pdf" _msttexthash=
=3D"4021537" _msthash=3D"195">GShard: Scaling Giant Models with Conditional=
 Computation and Automatic Sharding</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2303.09752.pdf" _msttexthash=
=3D"2965469" _msthash=3D"196">CoLT5: Faster Long-Range Transformers with Co=
nditional Computation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2208.07339.pdf" _msttexthash=
=3D"2736565" _msthash=3D"197">LLM.int8(): 8-bit Matrix Multiplication for T=
ransformers at Scale</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2110.02861.pdf" _msttexthash=
=3D"1436214" _msthash=3D"198">8-bit Optimizers via Block-wise Quantization<=
/a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.14314.pdf" _msttexthash=
=3D"1383213" _msthash=3D"199">QLoRA: Efficient Finetuning of Quantized LLMs=
</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2310.11453.pdf" _msttexthash=
=3D"2305368" _msthash=3D"200">BitNet: Scaling 1-bit Transformers for Large =
Language Models</a>
      </li></ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9971" _msthash=3D"201">15</td>
    <td _msttexthash=3D"199524" _msthash=3D"202">LLM Prompting</td>
    <td _msttexthash=3D"70265" _msthash=3D"203">Mar 6th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"204"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L15.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/8kFbPP8fCUk" _mstmutation=3D"1">Video</a><=
/font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2201.11903.pdf" _msttexthash=
=3D"3087448" _msthash=3D"205">Chain-of-Thought Prompting Elicits Reasoning =
in Large Language Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2203.11171.pdf" _msttexthash=
=3D"3213834" _msthash=3D"206">Self-Consistency Improves Chain of Thought Re=
asoning in Language Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.10601.pdf" _msttexthash=
=3D"3100851" _msthash=3D"207">Tree of Thoughts: Deliberate Problem Solving =
with Large Language Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2211.12588.pdf" _msttexthash=
=3D"6307951" _msthash=3D"208">Program of Thoughts Prompting: Disentangling =
Computation from Reasoning for Numerical Reasoning Tasks</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2205.10625.pdf" _msttexthash=
=3D"3461224" _msthash=3D"209">Least-to-Most Prompting Enables Complex Reaso=
ning in Large Language Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2210.03350.pdf" _msttexthash=
=3D"2871765" _msthash=3D"210">Measuring and Narrowing the Compositionality =
Gap in Language Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2210.03629.pdf" _msttexthash=
=3D"2169674" _msthash=3D"211">ReAct: Synergizing Reasoning and Acting in La=
nguage Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2303.17651.pdf" _msttexthash=
=3D"1888510" _msthash=3D"212">Self-Refine: Iterative Refinement with Self-F=
eedback</a></li>     =20
      </ul>
    </td>
  </tr>


  <tr>
    <td colspan=3D"5" style=3D"text-align: center">
    <h4 _msttexthash=3D"539760" _msthash=3D"213">(Large) Multimodal Models<=
/h4>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"10075" _msthash=3D"214">16</td>
    <td _msttexthash=3D"390572" _msthash=3D"215">Vision Transformers</td>
    <td _msttexthash=3D"70265" _msthash=3D"216">Mar 6th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"217"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L16.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DYklCcqu0Xm0&amp;ab_channe=
l=3DNioushaSadjadi" _mstmutation=3D"1">Video1</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3D9qe_mw7sm80" _mstmutation=
=3D"1">Video2</a></font><br>
    </td>
    <td>
      <ul>
        <!--<li><a href=3D"https://proceedings.mlr.press/v119/chen20s/chen2=
0s.pdf">Generative Pretraining from Pixels</a></li>-->
        <li><a href=3D"https://openreview.net/forum?id=3DYicbFdNTTy" _mstte=
xthash=3D"3154723" _msthash=3D"218">An Image is Worth 16x16 Words: Transfor=
mers for Image Recognition at Scale</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2103.14030.pdf" _msttexthash=
=3D"3364452" _msthash=3D"219">Swin Transformer: Hierarchical Vision Transfo=
rmer using Shifted Windows</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2012.12877.pdf" _msttexthash=
=3D"3813069" _msthash=3D"220">Training data-efficient image transformers &a=
mp; distillation through attention</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2104.14294.pdf" _msttexthash=
=3D"2390492" _msthash=3D"221">Emerging Properties in Self-Supervised Vision=
 Transformers</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2303.15446.pdf" _msttexthash=
=3D"6298058" _msthash=3D"222">SwiftFormer: Efficient Additive Attention for=
 Transformer-based Real-time Mobile Vision Applications</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2111.11418.pdf" _msttexthash=
=3D"1424605" _msthash=3D"223">MetaFormer Is Actually What You Need for Visi=
on</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2111.06377.pdf" _msttexthash=
=3D"1609257" _msthash=3D"224">Masked Autoencoders Are Scalable Vision Learn=
ers</a></li>
     </ul>
    </td>
  </tr>


  <tr>
    <td _msttexthash=3D"10179" _msthash=3D"225">17</td>
    <td _msttexthash=3D"284739" _msthash=3D"226">Diffusion Models</td>
    <td _msttexthash=3D"79768" _msthash=3D"227">Mar 13th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"228"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L17.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3Ds-pmBG1DSRA" _mstmutation=
=3D"1">Video</a></font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2101.09258.pdf" _msttexthash=
=3D"2305641" _msthash=3D"229">Maximum Likelihood Training of Score-Based Di=
ffusion Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2011.13456.pdf" _msttexthash=
=3D"3631862" _msthash=3D"230">Score-Based Generative Modeling through Stoch=
astic Differential Equations</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2010.02502.pdf" _msttexthash=
=3D"972140" _msthash=3D"231">Denoising Diffusion Implicit Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2006.11239.pdf" _msttexthash=
=3D"1250574" _msthash=3D"232">Denoising Diffusion Probabilistic Models</a><=
/li>
        <li><a href=3D"https://arxiv.org/pdf/2206.00927.pdf" _msttexthash=
=3D"4622241" _msthash=3D"233">DPM-Solver: A Fast ODE Solver for Diffusion P=
robabilistic Model Sampling in Around 10 Steps</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2303.01469.pdf" _msttexthash=
=3D"349466" _msthash=3D"234">Consistency Models</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"10283" _msthash=3D"235">18</td>
    <td _msttexthash=3D"282139" _msthash=3D"236">Image Generation</td>
    <td _msttexthash=3D"79768" _msthash=3D"237">Mar 13th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"238"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L18.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/EpIXtVuP_nA" _mstmutation=3D"1">Video</a><=
/font>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2112.10752.pdf" _msttexthash=
=3D"2452528" _msthash=3D"239">High-Resolution Image Synthesis with Latent D=
iffusion Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2205.11487.pdf" _msttexthash=
=3D"3976167" _msthash=3D"240">Photorealistic Text-to-Image Diffusion Models=
 with Deep Language Understanding</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2206.10789.pdf" _msttexthash=
=3D"3302000" _msthash=3D"241">Scaling Autoregressive Models for Content-Ric=
h Text-to-Image Generation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2204.06125.pdf" _msttexthash=
=3D"2685111" _msthash=3D"242">Hierarchical Text-Conditional Image Generatio=
n with CLIP Latents</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2310.00426.pdf" _msttexthash=
=3D"5344794" _msthash=3D"243">PIXART-=CE=B1: Fast Training of Diffusion Tra=
nsformer for Photorealistic Text-to-Image Synthesis</a></li>
        <li><a href=3D"https://static1.squarespace.com/static/6213c340453c3=
f502425776e/t/65663480a92fba51d0e1023f/1701197769659/adversarial_diffusion_=
distillation.pdf" _msttexthash=3D"986011" _msthash=3D"244">Adversarial Diff=
usion Distillation</a></li>
      </ul>
    </td>
  </tr>


  <tr>
    <td _msttexthash=3D"10387" _msthash=3D"245">19</td>
    <td _msttexthash=3D"713063" _msthash=3D"246">Multimodal Model Pre-train=
ing</td>
    <td _msttexthash=3D"79469" _msthash=3D"247">Mar 20th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"175136" _msthash=3D"248"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L19.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3Dg_RvHaIoZ2w" _mstmutation=
=3D"1">Video</a></font>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2103.00020.pdf" _msttexthash=
=3D"3155789" _msthash=3D"249">Learning Transferable Visual Models From Natu=
ral Language Supervision</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2201.12086.pdf" _msttexthash=
=3D"6786104" _msthash=3D"250">BLIP: Bootstrapping Language-Image Pre-traini=
ng for Unified Vision-Language Understanding and Generation</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2205.01917.pdf" _msttexthash=
=3D"2481180" _msthash=3D"251">CoCa: Contrastive Captioners are Image-Text F=
oundation Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2004.06165.pdf" _msttexthash=
=3D"3220048" _msthash=3D"252">Oscar: Object-Semantics Aligned Pre-training =
for Vision-Language Tasks</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2101.00529.pdf" _msttexthash=
=3D"2921308" _msthash=3D"253">VinVL: Revisiting Visual Representations in V=
ision-Language Models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2102.05918.pdf" _msttexthash=
=3D"4954118" _msthash=3D"254">Scaling Up Visual and Vision-Language Represe=
ntation Learning With Noisy Text Supervision</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2303.15343.pdf" _msttexthash=
=3D"1315899" _msthash=3D"255">Sigmoid Loss for Language Image Pre-Training<=
/a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9542" _msthash=3D"256">20</td>
    <td _msttexthash=3D"479648" _msthash=3D"257">Large Multimodal Models</t=
d>
    <td _msttexthash=3D"79469" _msthash=3D"258">Mar 20th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"259"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L20.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/e1-8iizC2Kw" _mstmutation=3D"1">Video1</a>=
<br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/5wC5h6wDLys" _mstmutation=3D"1">Video2</a>=
</font>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2204.14198.pdf" _msttexthash=
=3D"1907191" _msthash=3D"260">Flamingo: a Visual Language Model for Few-Sho=
t Learning</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2304.08485.pdf" _msttexthash=
=3D"574665" _msthash=3D"261">Visual Instruction Tuning</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.06500.pdf" _msttexthash=
=3D"4661566" _msthash=3D"262">InstructBLIP: Towards General-purpose Vision-=
Language Models with Instruction Tuning</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2209.06794.pdf" _msttexthash=
=3D"2084654" _msthash=3D"263">PaLI: A Jointly-Scaled Multilingual Language-=
Image Model</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2310.09199.pdf" _msttexthash=
=3D"2012127" _msthash=3D"264">PaLI-3 Vision Language Models: Smaller, Faste=
r, Stronger</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2312.13286.pdf" _msttexthash=
=3D"1875445" _msthash=3D"265">Generative Multimodal Models are In-Context L=
earners</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2312.14238.pdf" _msttexthash=
=3D"5308108" _msthash=3D"266">InternVL: Scaling up Vision Foundation Models=
 and Aligning for Generic Visual-Linguistic Tasks</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2312.11805v1.pdf" _msttexthash=
=3D"1725776" _msthash=3D"267">Gemini: A Family of Highly Capable Multimodal=
 Models</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td colspan=3D"5" style=3D"text-align: center">
    <h4 _msttexthash=3D"688064" _msthash=3D"268">Augmenting Foundation Mode=
ls</h4>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9646" _msthash=3D"269">21</td>
    <td _msttexthash=3D"319631" _msthash=3D"270">Tool Augmentation</td>
    <td _msttexthash=3D"80470" _msthash=3D"271">Mar 27th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"340119" _msthash=3D"272"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L21.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DAX6ns2fTZtA&amp;t=3D188s"=
 _mstmutation=3D"1">Video1</a><br _mstmutation=3D"1">
	    <a href=3D"https://www.youtube.com/watch?v=3DSZ_c48BOwM0" _mstmutation=
=3D"1">Video2</a></font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2302.04761.pdf" _msttexthash=
=3D"2341508" _msthash=3D"273">Toolformer: Language Models Can Teach Themsel=
ves to Use Tools</a></li>
	<li><a href=3D"https://arxiv.org/pdf/2303.09014.pdf" _msttexthash=3D"34527=
09" _msthash=3D"274">ART: Automatic multi-step reasoning and tool-use for l=
arge language models</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2307.16789.pdf" _msttexthash=
=3D"3218579" _msthash=3D"275">ToolLLM: Facilitating Large Language Models t=
o Master 16000+ Real-world APIs</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2305.11554.pdf" _msttexthash=
=3D"4320303" _msthash=3D"276">ToolkenGPT: Augmenting Frozen Language Models=
 with Massive Tools via Tool Embeddings</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2308.03688.pdf" _msttexthash=
=3D"980343" _msthash=3D"277">AgentBench: Evaluating LLMs as Agents</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2312.08914.pdf" _msttexthash=
=3D"1416337" _msthash=3D"278">CogAgent: A Visual Language Model for GUI Age=
nts</a></li>
	<li><a href=3D"https://arxiv.org/pdf/2307.13854.pdf" _msttexthash=3D"29646=
76" _msthash=3D"279">WebArena: A Realistic Web Environment for Building Aut=
onomous Agents</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9750" _msthash=3D"280">22</td>
    <td _msttexthash=3D"491101" _msthash=3D"281">Retrieval Augmentation</td=
>
    <td _msttexthash=3D"80470" _msthash=3D"282">Mar 27th</td>
    <td>
	    <font _mstmutation=3D"1" _msttexthash=3D"186602" _msthash=3D"283"><a h=
ref=3D"https://cs.uwaterloo.ca/~wenhuche/teaching/cs886/L22.pptx" _mstmutat=
ion=3D"1">Slides</a><br _mstmutation=3D"1">
	    <a href=3D"https://youtu.be/Gm4p8E87DdU" _mstmutation=3D"1">Video1</a>=
</font><br>
    </td>
    <td>
      <ul>
        <li><a href=3D"https://arxiv.org/pdf/2002.08909.pdf" _msttexthash=
=3D"2014532" _msthash=3D"284">REALM: Retrieval-Augmented Language Model Pre=
-Training</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2005.11401.pdf" _msttexthash=
=3D"2776969" _msthash=3D"285">Retrieval-Augmented Generation for Knowledge-=
Intensive NLP Tasks</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2112.04426.pdf" _msttexthash=
=3D"2729324" _msthash=3D"286">Improving language models by retrieving from =
trillions of tokens</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2310.11511.pdf" _msttexthash=
=3D"3792763" _msthash=3D"287">Self-RAG: Learning to Retrieve, Generate, and=
 Critique through Self-Reflection</a></li>
        <li><a href=3D"https://arxiv.org/pdf/2301.12652.pdf" _msttexthash=
=3D"1929993" _msthash=3D"288">REPLUG: Retrieval-Augmented Black-Box Languag=
e Models</a></li>
      </ul>
    </td>
  </tr>

  <tr>
    <td colspan=3D"5" style=3D"text-align: center">
    <h4 _msttexthash=3D"421005" _msthash=3D"289">Project Presentation</h4>
    </td>
  </tr>


  <tr>
    <td _msttexthash=3D"9854" _msthash=3D"290">23</td>
    <td _msttexthash=3D"350649" _msthash=3D"291">Final Presentation</td>
    <td _msttexthash=3D"69433" _msthash=3D"292">Apr 3rd</td>
    <td></td>
    <td>
      <ul>
        <li _msttexthash=3D"2463318" _msthash=3D"293">Group 1 - 10: Present=
ing their final project. Each with 12 minutes.</li>
      </ul>
    </td>
  </tr>

  <tr>
    <td _msttexthash=3D"9958" _msthash=3D"294">24</td>
    <td _msttexthash=3D"350649" _msthash=3D"295">Final Presentation</td>
    <td _msttexthash=3D"69433" _msthash=3D"296">Apr 3rd</td>
    <td></td>
    <td>
      <ul>
        <li _msttexthash=3D"2534883" _msthash=3D"297">Group 11 - 20: Presen=
ting their final project. Each with 12 minutes.</li>
      </ul>
    </td>
  </tr>

</tbody>
</table>
</div>

<div class=3D"container">
    <hr>
    <h2 _msttexthash=3D"1638780" _msthash=3D"298">University of Waterloo Ac=
ademic Integrity Policy</h2>
    <p _msttexthash=3D"18836194" _msthash=3D"299">The University of Waterlo=
o Senate Undergraduate Council has also approved the following message outl=
ining University of Waterloo policy on academic integrity and associated po=
licies.
    </p>

    <div class=3D"vspace"></div><h3 _msttexthash=3D"347256" _msthash=3D"300=
">Academic Integrity</h3>
    <p _msttexthash=3D"203348327" _msthash=3D"301">In order to maintain a c=
ulture of academic integrity, members of the University of Waterloo communi=
ty are expected to promote honesty, trust, fairness, respect and responsibi=
lity. Check the <a href=3D"http://www.uwaterloo.ca/academicintegrity/">Offi=
ce of Academic Integrity's website</a> for more information.
    All members of the UW community are expected to hold to the highest sta=
ndard of academic integrity in their studies, teaching, and research. This =
site explains why academic integrity is important and how students can avoi=
d academic misconduct. It also identifies resources available on campus for=
 students and faculty to help achieve academic integrity in, and our, of th=
e classroom.
    </p>
    <div class=3D"vspace"></div><h3 _msttexthash=3D"132041" _msthash=3D"302=
">Grievance</h3>
    <p _msttexthash=3D"62131108" _msthash=3D"303">A student who believes th=
at a decision affecting some aspect of his/her university life has been unf=
air or unreasonable may have grounds for initiating a grievance. Read  <a h=
ref=3D"http://www.adm.uwaterloo.ca/infosec/Policies/policy70.htm">Policy 70=
 - Student Petitions and Grievances, Section 4.</a> When in doubt please be=
 certain to contact the department's administrative assistant who will prov=
ide further assistance.
    </p>
    <div class=3D"vspace"></div><h3 _msttexthash=3D"155740" _msthash=3D"304=
">Discipline</h3>
    <p _msttexthash=3D"266756854" _msthash=3D"305">A student is expected to=
 know what constitutes academic integrity, to avoid committing academic off=
enses, and to take responsibility for his/her actions. A student who is uns=
ure whether an action constitutes an offense, or who needs help in learning=
 how to avoid offenses (e.g., plagiarism, cheating) or about =E2=80=9Crules=
=E2=80=9D for group work/collaboration should seek guidance from the course=
 professor, academic advisor, or the Undergraduate Associate Dean. For info=
rmation on categories of offenses and types of penalties, students should r=
efer to <a href=3D"http://www.adm.uwaterloo.ca/infosec/Policies/policy71.ht=
m">Policy 71-Student Discipline</a>. For
    typical penalties check <a href=3D"http://www.adm.uwaterloo.ca/infosec/=
guidelines/penaltyguidelines.htm">Guidelines for the Assessment of Penaltie=
s</a>.
    </p>
    <div class=3D"vspace"></div><h3 _msttexthash=3D"587340" _msthash=3D"306=
">Avoiding Academic Offenses</h3>
    <p _msttexthash=3D"72284160" _msthash=3D"307">Most students are unaware=
 of the line between acceptable and unacceptable academic behaviour, especi=
ally when discussing assignments with classmates and using the work of othe=
r students. For information on commonly misunderstood academic offenses and=
 how to avoid them, students should refer to the Faculty of Mathematics Che=
ating and Student Academic Discipline Policy.
    </p>
    <div class=3D"vspace"></div><h3 _msttexthash=3D"93951" _msthash=3D"308"=
>Appeals</h3>
    <p _msttexthash=3D"38342980" _msthash=3D"309">A decision made or a pena=
lty imposed under Policy 70, Student Petitions and Grievances (other than a=
 petition) or Policy 71, Student Discipline may be appealed if there is a g=
round. A student who believes he/she has a ground for an appeal should refe=
r to <a href=3D"http://www.adm.uwaterloo.ca/infosec/Policies/policy72.htm">=
Policy 72 - Student Appeals</a>.
    </p>
    <div class=3D"vspace"></div><h3 _msttexthash=3D"955500" _msthash=3D"310=
">Note for students with disabilities</h3>
    <p _msttexthash=3D"84109480" _msthash=3D"311">
      The AccessAbility Services Office (AAS), located in Needles Hall, Roo=
m 1401, collaborates with all academic departments to arrange appropriate a=
ccommodations for students with disabilities without compromising the acade=
mic integrity of the curriculum. If you require academic accommodations to =
lessen the impact of your disability, please register with the AAS at the b=
eginning of each academic term.
    </p>
</div>





</body></html>
------MultipartBoundary--TVvrKUIs0YsrHrZDdevYX6VzSO2uNL6PjYbdkuk7RH----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

tr { border-bottom: 1px solid black; border-top: 1px solid black; border-co=
llapse: collapse; }

.table-cell-no-wrap { white-space: nowrap; }

.table-width-fixed { width: 100px; }
------MultipartBoundary--TVvrKUIs0YsrHrZDdevYX6VzSO2uNL6PjYbdkuk7RH----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css

@charset "utf-8";

:root { --blue: #007bff; --indigo: #6610f2; --purple: #6f42c1; --pink: #e83=
e8c; --red: #dc3545; --orange: #fd7e14; --yellow: #ffc107; --green: #28a745=
; --teal: #20c997; --cyan: #17a2b8; --white: #fff; --gray: #6c757d; --gray-=
dark: #343a40; --primary: #007bff; --secondary: #6c757d; --success: #28a745=
; --info: #17a2b8; --warning: #ffc107; --danger: #dc3545; --light: #f8f9fa;=
 --dark: #343a40; --breakpoint-xs: 0; --breakpoint-sm: 576px; --breakpoint-=
md: 768px; --breakpoint-lg: 992px; --breakpoint-xl: 1200px; --font-family-s=
ans-serif: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Ne=
ue",Arial,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Seg=
oe UI Emoji","Segoe UI Symbol","Noto Color Emoji"; --font-family-monospace:=
 SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monos=
pace; }

*, ::after, ::before { box-sizing: border-box; }

html { font-family: sans-serif; line-height: 1.15; text-size-adjust: 100%; =
-webkit-tap-highlight-color: transparent; }

article, aside, figcaption, figure, footer, header, hgroup, main, nav, sect=
ion { display: block; }

body { margin: 0px; font-family: -apple-system, BlinkMacSystemFont, "Segoe =
UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-=
serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Colo=
r Emoji"; font-size: 1rem; font-weight: 400; line-height: 1.5; color: rgb(3=
3, 37, 41); text-align: left; background-color: rgb(255, 255, 255); }

[tabindex=3D"-1"]:focus:not(:focus-visible) { outline: 0px !important; }

hr { box-sizing: content-box; height: 0px; overflow: visible; }

h1, h2, h3, h4, h5, h6 { margin-top: 0px; margin-bottom: 0.5rem; }

p { margin-top: 0px; margin-bottom: 1rem; }

abbr[data-original-title], abbr[title] { text-decoration: underline dotted;=
 cursor: help; border-bottom: 0px; text-decoration-skip-ink: none; }

address { margin-bottom: 1rem; font-style: normal; line-height: inherit; }

dl, ol, ul { margin-top: 0px; margin-bottom: 1rem; }

ol ol, ol ul, ul ol, ul ul { margin-bottom: 0px; }

dt { font-weight: 700; }

dd { margin-bottom: 0.5rem; margin-left: 0px; }

blockquote { margin: 0px 0px 1rem; }

b, strong { font-weight: bolder; }

small { font-size: 80%; }

sub, sup { position: relative; font-size: 75%; line-height: 0; vertical-ali=
gn: baseline; }

sub { bottom: -0.25em; }

sup { top: -0.5em; }

a { color: rgb(0, 123, 255); text-decoration: none; background-color: trans=
parent; }

a:hover { color: rgb(0, 86, 179); text-decoration: underline; }

a:not([href]):not([class]) { color: inherit; text-decoration: none; }

a:not([href]):not([class]):hover { color: inherit; text-decoration: none; }

code, kbd, pre, samp { font-family: SFMono-Regular, Menlo, Monaco, Consolas=
, "Liberation Mono", "Courier New", monospace; font-size: 1em; }

pre { margin-top: 0px; margin-bottom: 1rem; overflow: auto; }

figure { margin: 0px 0px 1rem; }

img { vertical-align: middle; border-style: none; }

svg { overflow: hidden; vertical-align: middle; }

table { border-collapse: collapse; }

caption { padding-top: 0.75rem; padding-bottom: 0.75rem; color: rgb(108, 11=
7, 125); text-align: left; caption-side: bottom; }

th { text-align: -webkit-match-parent; }

label { display: inline-block; margin-bottom: 0.5rem; }

button { border-radius: 0px; }

button:focus:not(:focus-visible) { outline: 0px; }

button, input, optgroup, select, textarea { margin: 0px; font-family: inher=
it; font-size: inherit; line-height: inherit; }

button, input { overflow: visible; }

button, select { text-transform: none; }

[role=3D"button"] { cursor: pointer; }

select { overflow-wrap: normal; }

[type=3D"button"], [type=3D"reset"], [type=3D"submit"], button { appearance=
: button; }

[type=3D"button"]:not(:disabled), [type=3D"reset"]:not(:disabled), [type=3D=
"submit"]:not(:disabled), button:not(:disabled) { cursor: pointer; }

input[type=3D"checkbox"], input[type=3D"radio"] { box-sizing: border-box; p=
adding: 0px; }

textarea { overflow: auto; resize: vertical; }

fieldset { min-width: 0px; padding: 0px; margin: 0px; border: 0px; }

legend { display: block; width: 100%; max-width: 100%; padding: 0px; margin=
-bottom: 0.5rem; font-size: 1.5rem; line-height: inherit; color: inherit; w=
hite-space: normal; }

progress { vertical-align: baseline; }

[type=3D"number"]::-webkit-inner-spin-button, [type=3D"number"]::-webkit-ou=
ter-spin-button { height: auto; }

[type=3D"search"] { outline-offset: -2px; appearance: none; }

[type=3D"search"]::-webkit-search-decoration { appearance: none; }

::-webkit-file-upload-button { font: inherit; appearance: button; }

output { display: inline-block; }

summary { display: list-item; cursor: pointer; }

template { display: none; }

[hidden] { display: none !important; }

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 { margin-bottom: 0.5re=
m; font-weight: 500; line-height: 1.2; }

.h1, h1 { font-size: 2.5rem; }

.h2, h2 { font-size: 2rem; }

.h3, h3 { font-size: 1.75rem; }

.h4, h4 { font-size: 1.5rem; }

.h5, h5 { font-size: 1.25rem; }

.h6, h6 { font-size: 1rem; }

.lead { font-size: 1.25rem; font-weight: 300; }

.display-1 { font-size: 6rem; font-weight: 300; line-height: 1.2; }

.display-2 { font-size: 5.5rem; font-weight: 300; line-height: 1.2; }

.display-3 { font-size: 4.5rem; font-weight: 300; line-height: 1.2; }

.display-4 { font-size: 3.5rem; font-weight: 300; line-height: 1.2; }

hr { margin-top: 1rem; margin-bottom: 1rem; border-width: 1px 0px 0px; bord=
er-right-style: initial; border-bottom-style: initial; border-left-style: i=
nitial; border-right-color: initial; border-bottom-color: initial; border-l=
eft-color: initial; border-image: initial; border-top-style: solid; border-=
top-color: rgba(0, 0, 0, 0.1); }

.small, small { font-size: 80%; font-weight: 400; }

.mark, mark { padding: 0.2em; background-color: rgb(252, 248, 227); }

.list-unstyled { padding-left: 0px; list-style: none; }

.list-inline { padding-left: 0px; list-style: none; }

.list-inline-item { display: inline-block; }

.list-inline-item:not(:last-child) { margin-right: 0.5rem; }

.initialism { font-size: 90%; text-transform: uppercase; }

.blockquote { margin-bottom: 1rem; font-size: 1.25rem; }

.blockquote-footer { display: block; font-size: 80%; color: rgb(108, 117, 1=
25); }

.blockquote-footer::before { content: "=E2=80=94=C2=A0"; }

.img-fluid { max-width: 100%; height: auto; }

.img-thumbnail { padding: 0.25rem; background-color: rgb(255, 255, 255); bo=
rder: 1px solid rgb(222, 226, 230); border-radius: 0.25rem; max-width: 100%=
; height: auto; }

.figure { display: inline-block; }

.figure-img { margin-bottom: 0.5rem; line-height: 1; }

.figure-caption { font-size: 90%; color: rgb(108, 117, 125); }

code { font-size: 87.5%; color: rgb(232, 62, 140); overflow-wrap: break-wor=
d; }

a > code { color: inherit; }

kbd { padding: 0.2rem 0.4rem; font-size: 87.5%; color: rgb(255, 255, 255); =
background-color: rgb(33, 37, 41); border-radius: 0.2rem; }

kbd kbd { padding: 0px; font-size: 100%; font-weight: 700; }

pre { display: block; font-size: 87.5%; color: rgb(33, 37, 41); }

pre code { font-size: inherit; color: inherit; word-break: normal; }

.pre-scrollable { max-height: 340px; overflow-y: scroll; }

.container, .container-fluid, .container-lg, .container-md, .container-sm, =
.container-xl { width: 100%; padding-right: 15px; padding-left: 15px; margi=
n-right: auto; margin-left: auto; }

@media (min-width: 576px) {
  .container, .container-sm { max-width: 540px; }
}

@media (min-width: 768px) {
  .container, .container-md, .container-sm { max-width: 720px; }
}

@media (min-width: 992px) {
  .container, .container-lg, .container-md, .container-sm { max-width: 960p=
x; }
}

@media (min-width: 1200px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl { =
max-width: 1140px; }
}

.row { display: flex; flex-wrap: wrap; margin-right: -15px; margin-left: -1=
5px; }

.no-gutters { margin-right: 0px; margin-left: 0px; }

.no-gutters > .col, .no-gutters > [class*=3D"col-"] { padding-right: 0px; p=
adding-left: 0px; }

.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .c=
ol-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .c=
ol-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6=
, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-m=
d-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .=
col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1=
, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col=
-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .=
col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl=
-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto { p=
osition: relative; width: 100%; padding-right: 15px; padding-left: 15px; }

.col { flex-basis: 0px; flex-grow: 1; max-width: 100%; }

.row-cols-1 > * { flex: 0 0 100%; max-width: 100%; }

.row-cols-2 > * { flex: 0 0 50%; max-width: 50%; }

.row-cols-3 > * { flex: 0 0 33.3333%; max-width: 33.3333%; }

.row-cols-4 > * { flex: 0 0 25%; max-width: 25%; }

.row-cols-5 > * { flex: 0 0 20%; max-width: 20%; }

.row-cols-6 > * { flex: 0 0 16.6667%; max-width: 16.6667%; }

.col-auto { flex: 0 0 auto; width: auto; max-width: 100%; }

.col-1 { flex: 0 0 8.33333%; max-width: 8.33333%; }

.col-2 { flex: 0 0 16.6667%; max-width: 16.6667%; }

.col-3 { flex: 0 0 25%; max-width: 25%; }

.col-4 { flex: 0 0 33.3333%; max-width: 33.3333%; }

.col-5 { flex: 0 0 41.6667%; max-width: 41.6667%; }

.col-6 { flex: 0 0 50%; max-width: 50%; }

.col-7 { flex: 0 0 58.3333%; max-width: 58.3333%; }

.col-8 { flex: 0 0 66.6667%; max-width: 66.6667%; }

.col-9 { flex: 0 0 75%; max-width: 75%; }

.col-10 { flex: 0 0 83.3333%; max-width: 83.3333%; }

.col-11 { flex: 0 0 91.6667%; max-width: 91.6667%; }

.col-12 { flex: 0 0 100%; max-width: 100%; }

.order-first { order: -1; }

.order-last { order: 13; }

.order-0 { order: 0; }

.order-1 { order: 1; }

.order-2 { order: 2; }

.order-3 { order: 3; }

.order-4 { order: 4; }

.order-5 { order: 5; }

.order-6 { order: 6; }

.order-7 { order: 7; }

.order-8 { order: 8; }

.order-9 { order: 9; }

.order-10 { order: 10; }

.order-11 { order: 11; }

.order-12 { order: 12; }

.offset-1 { margin-left: 8.33333%; }

.offset-2 { margin-left: 16.6667%; }

.offset-3 { margin-left: 25%; }

.offset-4 { margin-left: 33.3333%; }

.offset-5 { margin-left: 41.6667%; }

.offset-6 { margin-left: 50%; }

.offset-7 { margin-left: 58.3333%; }

.offset-8 { margin-left: 66.6667%; }

.offset-9 { margin-left: 75%; }

.offset-10 { margin-left: 83.3333%; }

.offset-11 { margin-left: 91.6667%; }

@media (min-width: 576px) {
  .col-sm { flex-basis: 0px; flex-grow: 1; max-width: 100%; }
  .row-cols-sm-1 > * { flex: 0 0 100%; max-width: 100%; }
  .row-cols-sm-2 > * { flex: 0 0 50%; max-width: 50%; }
  .row-cols-sm-3 > * { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .row-cols-sm-4 > * { flex: 0 0 25%; max-width: 25%; }
  .row-cols-sm-5 > * { flex: 0 0 20%; max-width: 20%; }
  .row-cols-sm-6 > * { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-sm-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-sm-1 { flex: 0 0 8.33333%; max-width: 8.33333%; }
  .col-sm-2 { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .col-sm-4 { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .col-sm-5 { flex: 0 0 41.6667%; max-width: 41.6667%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-7 { flex: 0 0 58.3333%; max-width: 58.3333%; }
  .col-sm-8 { flex: 0 0 66.6667%; max-width: 66.6667%; }
  .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
  .col-sm-10 { flex: 0 0 83.3333%; max-width: 83.3333%; }
  .col-sm-11 { flex: 0 0 91.6667%; max-width: 91.6667%; }
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
  .order-sm-first { order: -1; }
  .order-sm-last { order: 13; }
  .order-sm-0 { order: 0; }
  .order-sm-1 { order: 1; }
  .order-sm-2 { order: 2; }
  .order-sm-3 { order: 3; }
  .order-sm-4 { order: 4; }
  .order-sm-5 { order: 5; }
  .order-sm-6 { order: 6; }
  .order-sm-7 { order: 7; }
  .order-sm-8 { order: 8; }
  .order-sm-9 { order: 9; }
  .order-sm-10 { order: 10; }
  .order-sm-11 { order: 11; }
  .order-sm-12 { order: 12; }
  .offset-sm-0 { margin-left: 0px; }
  .offset-sm-1 { margin-left: 8.33333%; }
  .offset-sm-2 { margin-left: 16.6667%; }
  .offset-sm-3 { margin-left: 25%; }
  .offset-sm-4 { margin-left: 33.3333%; }
  .offset-sm-5 { margin-left: 41.6667%; }
  .offset-sm-6 { margin-left: 50%; }
  .offset-sm-7 { margin-left: 58.3333%; }
  .offset-sm-8 { margin-left: 66.6667%; }
  .offset-sm-9 { margin-left: 75%; }
  .offset-sm-10 { margin-left: 83.3333%; }
  .offset-sm-11 { margin-left: 91.6667%; }
}

@media (min-width: 768px) {
  .col-md { flex-basis: 0px; flex-grow: 1; max-width: 100%; }
  .row-cols-md-1 > * { flex: 0 0 100%; max-width: 100%; }
  .row-cols-md-2 > * { flex: 0 0 50%; max-width: 50%; }
  .row-cols-md-3 > * { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .row-cols-md-4 > * { flex: 0 0 25%; max-width: 25%; }
  .row-cols-md-5 > * { flex: 0 0 20%; max-width: 20%; }
  .row-cols-md-6 > * { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-md-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-md-1 { flex: 0 0 8.33333%; max-width: 8.33333%; }
  .col-md-2 { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .col-md-5 { flex: 0 0 41.6667%; max-width: 41.6667%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-7 { flex: 0 0 58.3333%; max-width: 58.3333%; }
  .col-md-8 { flex: 0 0 66.6667%; max-width: 66.6667%; }
  .col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .col-md-10 { flex: 0 0 83.3333%; max-width: 83.3333%; }
  .col-md-11 { flex: 0 0 91.6667%; max-width: 91.6667%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
  .order-md-first { order: -1; }
  .order-md-last { order: 13; }
  .order-md-0 { order: 0; }
  .order-md-1 { order: 1; }
  .order-md-2 { order: 2; }
  .order-md-3 { order: 3; }
  .order-md-4 { order: 4; }
  .order-md-5 { order: 5; }
  .order-md-6 { order: 6; }
  .order-md-7 { order: 7; }
  .order-md-8 { order: 8; }
  .order-md-9 { order: 9; }
  .order-md-10 { order: 10; }
  .order-md-11 { order: 11; }
  .order-md-12 { order: 12; }
  .offset-md-0 { margin-left: 0px; }
  .offset-md-1 { margin-left: 8.33333%; }
  .offset-md-2 { margin-left: 16.6667%; }
  .offset-md-3 { margin-left: 25%; }
  .offset-md-4 { margin-left: 33.3333%; }
  .offset-md-5 { margin-left: 41.6667%; }
  .offset-md-6 { margin-left: 50%; }
  .offset-md-7 { margin-left: 58.3333%; }
  .offset-md-8 { margin-left: 66.6667%; }
  .offset-md-9 { margin-left: 75%; }
  .offset-md-10 { margin-left: 83.3333%; }
  .offset-md-11 { margin-left: 91.6667%; }
}

@media (min-width: 992px) {
  .col-lg { flex-basis: 0px; flex-grow: 1; max-width: 100%; }
  .row-cols-lg-1 > * { flex: 0 0 100%; max-width: 100%; }
  .row-cols-lg-2 > * { flex: 0 0 50%; max-width: 50%; }
  .row-cols-lg-3 > * { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .row-cols-lg-4 > * { flex: 0 0 25%; max-width: 25%; }
  .row-cols-lg-5 > * { flex: 0 0 20%; max-width: 20%; }
  .row-cols-lg-6 > * { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-lg-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-lg-1 { flex: 0 0 8.33333%; max-width: 8.33333%; }
  .col-lg-2 { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-4 { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .col-lg-5 { flex: 0 0 41.6667%; max-width: 41.6667%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-7 { flex: 0 0 58.3333%; max-width: 58.3333%; }
  .col-lg-8 { flex: 0 0 66.6667%; max-width: 66.6667%; }
  .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .col-lg-10 { flex: 0 0 83.3333%; max-width: 83.3333%; }
  .col-lg-11 { flex: 0 0 91.6667%; max-width: 91.6667%; }
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
  .order-lg-first { order: -1; }
  .order-lg-last { order: 13; }
  .order-lg-0 { order: 0; }
  .order-lg-1 { order: 1; }
  .order-lg-2 { order: 2; }
  .order-lg-3 { order: 3; }
  .order-lg-4 { order: 4; }
  .order-lg-5 { order: 5; }
  .order-lg-6 { order: 6; }
  .order-lg-7 { order: 7; }
  .order-lg-8 { order: 8; }
  .order-lg-9 { order: 9; }
  .order-lg-10 { order: 10; }
  .order-lg-11 { order: 11; }
  .order-lg-12 { order: 12; }
  .offset-lg-0 { margin-left: 0px; }
  .offset-lg-1 { margin-left: 8.33333%; }
  .offset-lg-2 { margin-left: 16.6667%; }
  .offset-lg-3 { margin-left: 25%; }
  .offset-lg-4 { margin-left: 33.3333%; }
  .offset-lg-5 { margin-left: 41.6667%; }
  .offset-lg-6 { margin-left: 50%; }
  .offset-lg-7 { margin-left: 58.3333%; }
  .offset-lg-8 { margin-left: 66.6667%; }
  .offset-lg-9 { margin-left: 75%; }
  .offset-lg-10 { margin-left: 83.3333%; }
  .offset-lg-11 { margin-left: 91.6667%; }
}

@media (min-width: 1200px) {
  .col-xl { flex-basis: 0px; flex-grow: 1; max-width: 100%; }
  .row-cols-xl-1 > * { flex: 0 0 100%; max-width: 100%; }
  .row-cols-xl-2 > * { flex: 0 0 50%; max-width: 50%; }
  .row-cols-xl-3 > * { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .row-cols-xl-4 > * { flex: 0 0 25%; max-width: 25%; }
  .row-cols-xl-5 > * { flex: 0 0 20%; max-width: 20%; }
  .row-cols-xl-6 > * { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-xl-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-xl-1 { flex: 0 0 8.33333%; max-width: 8.33333%; }
  .col-xl-2 { flex: 0 0 16.6667%; max-width: 16.6667%; }
  .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
  .col-xl-4 { flex: 0 0 33.3333%; max-width: 33.3333%; }
  .col-xl-5 { flex: 0 0 41.6667%; max-width: 41.6667%; }
  .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
  .col-xl-7 { flex: 0 0 58.3333%; max-width: 58.3333%; }
  .col-xl-8 { flex: 0 0 66.6667%; max-width: 66.6667%; }
  .col-xl-9 { flex: 0 0 75%; max-width: 75%; }
  .col-xl-10 { flex: 0 0 83.3333%; max-width: 83.3333%; }
  .col-xl-11 { flex: 0 0 91.6667%; max-width: 91.6667%; }
  .col-xl-12 { flex: 0 0 100%; max-width: 100%; }
  .order-xl-first { order: -1; }
  .order-xl-last { order: 13; }
  .order-xl-0 { order: 0; }
  .order-xl-1 { order: 1; }
  .order-xl-2 { order: 2; }
  .order-xl-3 { order: 3; }
  .order-xl-4 { order: 4; }
  .order-xl-5 { order: 5; }
  .order-xl-6 { order: 6; }
  .order-xl-7 { order: 7; }
  .order-xl-8 { order: 8; }
  .order-xl-9 { order: 9; }
  .order-xl-10 { order: 10; }
  .order-xl-11 { order: 11; }
  .order-xl-12 { order: 12; }
  .offset-xl-0 { margin-left: 0px; }
  .offset-xl-1 { margin-left: 8.33333%; }
  .offset-xl-2 { margin-left: 16.6667%; }
  .offset-xl-3 { margin-left: 25%; }
  .offset-xl-4 { margin-left: 33.3333%; }
  .offset-xl-5 { margin-left: 41.6667%; }
  .offset-xl-6 { margin-left: 50%; }
  .offset-xl-7 { margin-left: 58.3333%; }
  .offset-xl-8 { margin-left: 66.6667%; }
  .offset-xl-9 { margin-left: 75%; }
  .offset-xl-10 { margin-left: 83.3333%; }
  .offset-xl-11 { margin-left: 91.6667%; }
}

.table { width: 100%; margin-bottom: 1rem; color: rgb(33, 37, 41); }

.table td, .table th { padding: 0.75rem; vertical-align: top; border-top: 1=
px solid rgb(222, 226, 230); }

.table thead th { vertical-align: bottom; border-bottom: 2px solid rgb(222,=
 226, 230); }

.table tbody + tbody { border-top: 2px solid rgb(222, 226, 230); }

.table-sm td, .table-sm th { padding: 0.3rem; }

.table-bordered { border: 1px solid rgb(222, 226, 230); }

.table-bordered td, .table-bordered th { border: 1px solid rgb(222, 226, 23=
0); }

.table-bordered thead td, .table-bordered thead th { border-bottom-width: 2=
px; }

.table-borderless tbody + tbody, .table-borderless td, .table-borderless th=
, .table-borderless thead th { border: 0px; }

.table-striped tbody tr:nth-of-type(2n+1) { background-color: rgba(0, 0, 0,=
 0.05); }

.table-hover tbody tr:hover { color: rgb(33, 37, 41); background-color: rgb=
a(0, 0, 0, 0.075); }

.table-primary, .table-primary > td, .table-primary > th { background-color=
: rgb(184, 218, 255); }

.table-primary tbody + tbody, .table-primary td, .table-primary th, .table-=
primary thead th { border-color: rgb(122, 186, 255); }

.table-hover .table-primary:hover { background-color: rgb(159, 205, 255); }

.table-hover .table-primary:hover > td, .table-hover .table-primary:hover >=
 th { background-color: rgb(159, 205, 255); }

.table-secondary, .table-secondary > td, .table-secondary > th { background=
-color: rgb(214, 216, 219); }

.table-secondary tbody + tbody, .table-secondary td, .table-secondary th, .=
table-secondary thead th { border-color: rgb(179, 183, 187); }

.table-hover .table-secondary:hover { background-color: rgb(200, 203, 207);=
 }

.table-hover .table-secondary:hover > td, .table-hover .table-secondary:hov=
er > th { background-color: rgb(200, 203, 207); }

.table-success, .table-success > td, .table-success > th { background-color=
: rgb(195, 230, 203); }

.table-success tbody + tbody, .table-success td, .table-success th, .table-=
success thead th { border-color: rgb(143, 209, 158); }

.table-hover .table-success:hover { background-color: rgb(177, 223, 187); }

.table-hover .table-success:hover > td, .table-hover .table-success:hover >=
 th { background-color: rgb(177, 223, 187); }

.table-info, .table-info > td, .table-info > th { background-color: rgb(190=
, 229, 235); }

.table-info tbody + tbody, .table-info td, .table-info th, .table-info thea=
d th { border-color: rgb(134, 207, 218); }

.table-hover .table-info:hover { background-color: rgb(171, 221, 229); }

.table-hover .table-info:hover > td, .table-hover .table-info:hover > th { =
background-color: rgb(171, 221, 229); }

.table-warning, .table-warning > td, .table-warning > th { background-color=
: rgb(255, 238, 186); }

.table-warning tbody + tbody, .table-warning td, .table-warning th, .table-=
warning thead th { border-color: rgb(255, 223, 126); }

.table-hover .table-warning:hover { background-color: rgb(255, 232, 161); }

.table-hover .table-warning:hover > td, .table-hover .table-warning:hover >=
 th { background-color: rgb(255, 232, 161); }

.table-danger, .table-danger > td, .table-danger > th { background-color: r=
gb(245, 198, 203); }

.table-danger tbody + tbody, .table-danger td, .table-danger th, .table-dan=
ger thead th { border-color: rgb(237, 150, 158); }

.table-hover .table-danger:hover { background-color: rgb(241, 176, 183); }

.table-hover .table-danger:hover > td, .table-hover .table-danger:hover > t=
h { background-color: rgb(241, 176, 183); }

.table-light, .table-light > td, .table-light > th { background-color: rgb(=
253, 253, 254); }

.table-light tbody + tbody, .table-light td, .table-light th, .table-light =
thead th { border-color: rgb(251, 252, 252); }

.table-hover .table-light:hover { background-color: rgb(236, 236, 246); }

.table-hover .table-light:hover > td, .table-hover .table-light:hover > th =
{ background-color: rgb(236, 236, 246); }

.table-dark, .table-dark > td, .table-dark > th { background-color: rgb(198=
, 200, 202); }

.table-dark tbody + tbody, .table-dark td, .table-dark th, .table-dark thea=
d th { border-color: rgb(149, 153, 156); }

.table-hover .table-dark:hover { background-color: rgb(185, 187, 190); }

.table-hover .table-dark:hover > td, .table-hover .table-dark:hover > th { =
background-color: rgb(185, 187, 190); }

.table-active, .table-active > td, .table-active > th { background-color: r=
gba(0, 0, 0, 0.075); }

.table-hover .table-active:hover { background-color: rgba(0, 0, 0, 0.075); =
}

.table-hover .table-active:hover > td, .table-hover .table-active:hover > t=
h { background-color: rgba(0, 0, 0, 0.075); }

.table .thead-dark th { color: rgb(255, 255, 255); background-color: rgb(52=
, 58, 64); border-color: rgb(69, 77, 85); }

.table .thead-light th { color: rgb(73, 80, 87); background-color: rgb(233,=
 236, 239); border-color: rgb(222, 226, 230); }

.table-dark { color: rgb(255, 255, 255); background-color: rgb(52, 58, 64);=
 }

.table-dark td, .table-dark th, .table-dark thead th { border-color: rgb(69=
, 77, 85); }

.table-dark.table-bordered { border: 0px; }

.table-dark.table-striped tbody tr:nth-of-type(2n+1) { background-color: rg=
ba(255, 255, 255, 0.05); }

.table-dark.table-hover tbody tr:hover { color: rgb(255, 255, 255); backgro=
und-color: rgba(255, 255, 255, 0.075); }

@media (max-width: 575.98px) {
  .table-responsive-sm { display: block; width: 100%; overflow-x: auto; }
  .table-responsive-sm > .table-bordered { border: 0px; }
}

@media (max-width: 767.98px) {
  .table-responsive-md { display: block; width: 100%; overflow-x: auto; }
  .table-responsive-md > .table-bordered { border: 0px; }
}

@media (max-width: 991.98px) {
  .table-responsive-lg { display: block; width: 100%; overflow-x: auto; }
  .table-responsive-lg > .table-bordered { border: 0px; }
}

@media (max-width: 1199.98px) {
  .table-responsive-xl { display: block; width: 100%; overflow-x: auto; }
  .table-responsive-xl > .table-bordered { border: 0px; }
}

.table-responsive { display: block; width: 100%; overflow-x: auto; }

.table-responsive > .table-bordered { border: 0px; }

.form-control { display: block; width: 100%; height: calc(1.5em + 2px + 0.7=
5rem); padding: 0.375rem 0.75rem; font-size: 1rem; font-weight: 400; line-h=
eight: 1.5; color: rgb(73, 80, 87); background-color: rgb(255, 255, 255); b=
ackground-clip: padding-box; border: 1px solid rgb(206, 212, 218); border-r=
adius: 0.25rem; transition: border-color 0.15s ease-in-out 0s, box-shadow 0=
.15s ease-in-out 0s; }

@media (prefers-reduced-motion: reduce) {
  .form-control { transition: none 0s ease 0s; }
}

.form-control:focus { color: rgb(73, 80, 87); background-color: rgb(255, 25=
5, 255); border-color: rgb(128, 189, 255); outline: 0px; box-shadow: rgba(0=
, 123, 255, 0.25) 0px 0px 0px 0.2rem; }

.form-control::-webkit-input-placeholder { color: rgb(108, 117, 125); opaci=
ty: 1; }

.form-control::placeholder { color: rgb(108, 117, 125); opacity: 1; }

.form-control:disabled, .form-control[readonly] { background-color: rgb(233=
, 236, 239); opacity: 1; }

input[type=3D"date"].form-control, input[type=3D"datetime-local"].form-cont=
rol, input[type=3D"month"].form-control, input[type=3D"time"].form-control =
{ appearance: none; }

.form-control-file, .form-control-range { display: block; width: 100%; }

.col-form-label { padding-top: calc(1px + 0.375rem); padding-bottom: calc(1=
px + 0.375rem); margin-bottom: 0px; font-size: inherit; line-height: 1.5; }

.col-form-label-lg { padding-top: calc(1px + 0.5rem); padding-bottom: calc(=
1px + 0.5rem); font-size: 1.25rem; line-height: 1.5; }

.col-form-label-sm { padding-top: calc(1px + 0.25rem); padding-bottom: calc=
(1px + 0.25rem); font-size: 0.875rem; line-height: 1.5; }

.form-control-plaintext { display: block; width: 100%; padding: 0.375rem 0p=
x; margin-bottom: 0px; font-size: 1rem; line-height: 1.5; color: rgb(33, 37=
, 41); background-color: transparent; border-style: solid; border-color: tr=
ansparent; border-image: initial; border-width: 1px 0px; }

.form-control-plaintext.form-control-lg, .form-control-plaintext.form-contr=
ol-sm { padding-right: 0px; padding-left: 0px; }

.form-control-sm { height: calc(1.5em + 2px + 0.5rem); padding: 0.25rem 0.5=
rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; }

.form-control-lg { height: calc(1.5em + 2px + 1rem); padding: 0.5rem 1rem; =
font-size: 1.25rem; line-height: 1.5; border-radius: 0.3rem; }

select.form-control[multiple], select.form-control[size] { height: auto; }

textarea.form-control { height: auto; }

.form-group { margin-bottom: 1rem; }

.form-text { display: block; margin-top: 0.25rem; }

.form-row { display: flex; flex-wrap: wrap; margin-right: -5px; margin-left=
: -5px; }

.form-row > .col, .form-row > [class*=3D"col-"] { padding-right: 5px; paddi=
ng-left: 5px; }

.form-check { position: relative; display: block; padding-left: 1.25rem; }

.form-check-input { position: absolute; margin-top: 0.3rem; margin-left: -1=
.25rem; }

.form-check-input:disabled ~ .form-check-label, .form-check-input[disabled]=
 ~ .form-check-label { color: rgb(108, 117, 125); }

.form-check-label { margin-bottom: 0px; }

.form-check-inline { display: inline-flex; align-items: center; padding-lef=
t: 0px; margin-right: 0.75rem; }

.form-check-inline .form-check-input { position: static; margin-top: 0px; m=
argin-right: 0.3125rem; margin-left: 0px; }

.valid-feedback { display: none; width: 100%; margin-top: 0.25rem; font-siz=
e: 80%; color: rgb(40, 167, 69); }

.valid-tooltip { position: absolute; top: 100%; left: 0px; z-index: 5; disp=
lay: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: 0.1rem; fo=
nt-size: 0.875rem; line-height: 1.5; color: rgb(255, 255, 255); background-=
color: rgba(40, 167, 69, 0.9); border-radius: 0.25rem; }

.form-row > .col > .valid-tooltip, .form-row > [class*=3D"col-"] > .valid-t=
ooltip { left: 5px; }

.is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip, .was-validated :va=
lid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip { display: bl=
ock; }

.form-control.is-valid, .was-validated .form-control:valid { border-color: =
rgb(40, 167, 69); background-image: url("data:image/svg+xml,%3csvg xmlns=3D=
'http://www.w3.org/2000/svg' width=3D'8' height=3D'8' viewBox=3D'0 0 8 8'%3=
e%3cpath fill=3D'%2328a745' d=3D'M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1=
.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3=
e"); background-repeat: no-repeat; background-position: right calc(0.375em =
+ 0.1875rem) center; background-size: calc(0.75em + 0.375rem) calc(0.75em +=
 0.375rem); padding-right: calc(1.5em + 0.75rem) !important; }

.form-control.is-valid:focus, .was-validated .form-control:valid:focus { bo=
rder-color: rgb(40, 167, 69); box-shadow: rgba(40, 167, 69, 0.25) 0px 0px 0=
px 0.2rem; }

.was-validated select.form-control:valid, select.form-control.is-valid { ba=
ckground-position: right 1.5rem center; padding-right: 3rem !important; }

.was-validated textarea.form-control:valid, textarea.form-control.is-valid =
{ padding-right: calc(1.5em + 0.75rem); background-position: right calc(0.3=
75em + 0.1875rem) top calc(0.375em + 0.1875rem); }

.custom-select.is-valid, .was-validated .custom-select:valid { border-color=
: rgb(40, 167, 69); background: url("data:image/svg+xml,%3csvg xmlns=3D'htt=
p://www.w3.org/2000/svg' width=3D'4' height=3D'5' viewBox=3D'0 0 4 5'%3e%3c=
path fill=3D'%23343a40' d=3D'M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right =
0.75rem center / 8px 10px no-repeat, url("data:image/svg+xml,%3csvg xmlns=
=3D'http://www.w3.org/2000/svg' width=3D'8' height=3D'8' viewBox=3D'0 0 8 8=
'%3e%3cpath fill=3D'%2328a745' d=3D'M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.=
8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/sv=
g%3e") right 1.75rem center / calc(0.75em + 0.375rem) calc(0.75em + 0.375re=
m) no-repeat rgb(255, 255, 255); padding-right: calc(0.75em + 2.3125rem) !i=
mportant; }

.custom-select.is-valid:focus, .was-validated .custom-select:valid:focus { =
border-color: rgb(40, 167, 69); box-shadow: rgba(40, 167, 69, 0.25) 0px 0px=
 0px 0.2rem; }

.form-check-input.is-valid ~ .form-check-label, .was-validated .form-check-=
input:valid ~ .form-check-label { color: rgb(40, 167, 69); }

.form-check-input.is-valid ~ .valid-feedback, .form-check-input.is-valid ~ =
.valid-tooltip, .was-validated .form-check-input:valid ~ .valid-feedback, .=
was-validated .form-check-input:valid ~ .valid-tooltip { display: block; }

.custom-control-input.is-valid ~ .custom-control-label, .was-validated .cus=
tom-control-input:valid ~ .custom-control-label { color: rgb(40, 167, 69); =
}

.custom-control-input.is-valid ~ .custom-control-label::before, .was-valida=
ted .custom-control-input:valid ~ .custom-control-label::before { border-co=
lor: rgb(40, 167, 69); }

.custom-control-input.is-valid:checked ~ .custom-control-label::before, .wa=
s-validated .custom-control-input:valid:checked ~ .custom-control-label::be=
fore { border-color: rgb(52, 206, 87); background-color: rgb(52, 206, 87); =
}

.custom-control-input.is-valid:focus ~ .custom-control-label::before, .was-=
validated .custom-control-input:valid:focus ~ .custom-control-label::before=
 { box-shadow: rgba(40, 167, 69, 0.25) 0px 0px 0px 0.2rem; }

.custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label:=
:before, .was-validated .custom-control-input:valid:focus:not(:checked) ~ .=
custom-control-label::before { border-color: rgb(40, 167, 69); }

.custom-file-input.is-valid ~ .custom-file-label, .was-validated .custom-fi=
le-input:valid ~ .custom-file-label { border-color: rgb(40, 167, 69); }

.custom-file-input.is-valid:focus ~ .custom-file-label, .was-validated .cus=
tom-file-input:valid:focus ~ .custom-file-label { border-color: rgb(40, 167=
, 69); box-shadow: rgba(40, 167, 69, 0.25) 0px 0px 0px 0.2rem; }

.invalid-feedback { display: none; width: 100%; margin-top: 0.25rem; font-s=
ize: 80%; color: rgb(220, 53, 69); }

.invalid-tooltip { position: absolute; top: 100%; left: 0px; z-index: 5; di=
splay: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: 0.1rem; =
font-size: 0.875rem; line-height: 1.5; color: rgb(255, 255, 255); backgroun=
d-color: rgba(220, 53, 69, 0.9); border-radius: 0.25rem; }

.form-row > .col > .invalid-tooltip, .form-row > [class*=3D"col-"] > .inval=
id-tooltip { left: 5px; }

.is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip, .was-valid=
ated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-toolt=
ip { display: block; }

.form-control.is-invalid, .was-validated .form-control:invalid { border-col=
or: rgb(220, 53, 69); background-image: url("data:image/svg+xml,%3csvg xmln=
s=3D'http://www.w3.org/2000/svg' width=3D'12' height=3D'12' fill=3D'none' s=
troke=3D'%23dc3545' viewBox=3D'0 0 12 12'%3e%3ccircle cx=3D'6' cy=3D'6' r=
=3D'4.5'/%3e%3cpath stroke-linejoin=3D'round' d=3D'M5.8 3.6h.4L6 6.5z'/%3e%=
3ccircle cx=3D'6' cy=3D'8.2' r=3D'.6' fill=3D'%23dc3545' stroke=3D'none'/%3=
e%3c/svg%3e"); background-repeat: no-repeat; background-position: right cal=
c(0.375em + 0.1875rem) center; background-size: calc(0.75em + 0.375rem) cal=
c(0.75em + 0.375rem); padding-right: calc(1.5em + 0.75rem) !important; }

.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus =
{ border-color: rgb(220, 53, 69); box-shadow: rgba(220, 53, 69, 0.25) 0px 0=
px 0px 0.2rem; }

.was-validated select.form-control:invalid, select.form-control.is-invalid =
{ background-position: right 1.5rem center; padding-right: 3rem !important;=
 }

.was-validated textarea.form-control:invalid, textarea.form-control.is-inva=
lid { padding-right: calc(1.5em + 0.75rem); background-position: right calc=
(0.375em + 0.1875rem) top calc(0.375em + 0.1875rem); }

.custom-select.is-invalid, .was-validated .custom-select:invalid { border-c=
olor: rgb(220, 53, 69); background: url("data:image/svg+xml,%3csvg xmlns=3D=
'http://www.w3.org/2000/svg' width=3D'4' height=3D'5' viewBox=3D'0 0 4 5'%3=
e%3cpath fill=3D'%23343a40' d=3D'M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") ri=
ght 0.75rem center / 8px 10px no-repeat, url("data:image/svg+xml,%3csvg xml=
ns=3D'http://www.w3.org/2000/svg' width=3D'12' height=3D'12' fill=3D'none' =
stroke=3D'%23dc3545' viewBox=3D'0 0 12 12'%3e%3ccircle cx=3D'6' cy=3D'6' r=
=3D'4.5'/%3e%3cpath stroke-linejoin=3D'round' d=3D'M5.8 3.6h.4L6 6.5z'/%3e%=
3ccircle cx=3D'6' cy=3D'8.2' r=3D'.6' fill=3D'%23dc3545' stroke=3D'none'/%3=
e%3c/svg%3e") right 1.75rem center / calc(0.75em + 0.375rem) calc(0.75em + =
0.375rem) no-repeat rgb(255, 255, 255); padding-right: calc(0.75em + 2.3125=
rem) !important; }

.custom-select.is-invalid:focus, .was-validated .custom-select:invalid:focu=
s { border-color: rgb(220, 53, 69); box-shadow: rgba(220, 53, 69, 0.25) 0px=
 0px 0px 0.2rem; }

.form-check-input.is-invalid ~ .form-check-label, .was-validated .form-chec=
k-input:invalid ~ .form-check-label { color: rgb(220, 53, 69); }

.form-check-input.is-invalid ~ .invalid-feedback, .form-check-input.is-inva=
lid ~ .invalid-tooltip, .was-validated .form-check-input:invalid ~ .invalid=
-feedback, .was-validated .form-check-input:invalid ~ .invalid-tooltip { di=
splay: block; }

.custom-control-input.is-invalid ~ .custom-control-label, .was-validated .c=
ustom-control-input:invalid ~ .custom-control-label { color: rgb(220, 53, 6=
9); }

.custom-control-input.is-invalid ~ .custom-control-label::before, .was-vali=
dated .custom-control-input:invalid ~ .custom-control-label::before { borde=
r-color: rgb(220, 53, 69); }

.custom-control-input.is-invalid:checked ~ .custom-control-label::before, .=
was-validated .custom-control-input:invalid:checked ~ .custom-control-label=
::before { border-color: rgb(228, 96, 109); background-color: rgb(228, 96, =
109); }

.custom-control-input.is-invalid:focus ~ .custom-control-label::before, .wa=
s-validated .custom-control-input:invalid:focus ~ .custom-control-label::be=
fore { box-shadow: rgba(220, 53, 69, 0.25) 0px 0px 0px 0.2rem; }

.custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-labe=
l::before, .was-validated .custom-control-input:invalid:focus:not(:checked)=
 ~ .custom-control-label::before { border-color: rgb(220, 53, 69); }

.custom-file-input.is-invalid ~ .custom-file-label, .was-validated .custom-=
file-input:invalid ~ .custom-file-label { border-color: rgb(220, 53, 69); }

.custom-file-input.is-invalid:focus ~ .custom-file-label, .was-validated .c=
ustom-file-input:invalid:focus ~ .custom-file-label { border-color: rgb(220=
, 53, 69); box-shadow: rgba(220, 53, 69, 0.25) 0px 0px 0px 0.2rem; }

.form-inline { display: flex; flex-flow: wrap; align-items: center; }

.form-inline .form-check { width: 100%; }

@media (min-width: 576px) {
  .form-inline label { display: flex; align-items: center; justify-content:=
 center; margin-bottom: 0px; }
  .form-inline .form-group { display: flex; flex: 0 0 auto; flex-flow: wrap=
; align-items: center; margin-bottom: 0px; }
  .form-inline .form-control { display: inline-block; width: auto; vertical=
-align: middle; }
  .form-inline .form-control-plaintext { display: inline-block; }
  .form-inline .custom-select, .form-inline .input-group { width: auto; }
  .form-inline .form-check { display: flex; align-items: center; justify-co=
ntent: center; width: auto; padding-left: 0px; }
  .form-inline .form-check-input { position: relative; flex-shrink: 0; marg=
in-top: 0px; margin-right: 0.25rem; margin-left: 0px; }
  .form-inline .custom-control { align-items: center; justify-content: cent=
er; }
  .form-inline .custom-control-label { margin-bottom: 0px; }
}

.btn { display: inline-block; font-weight: 400; color: rgb(33, 37, 41); tex=
t-align: center; vertical-align: middle; user-select: none; background-colo=
r: transparent; border: 1px solid transparent; padding: 0.375rem 0.75rem; f=
ont-size: 1rem; line-height: 1.5; border-radius: 0.25rem; transition: color=
 0.15s ease-in-out 0s, background-color 0.15s ease-in-out 0s, border-color =
0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s; }

@media (prefers-reduced-motion: reduce) {
  .btn { transition: none 0s ease 0s; }
}

.btn:hover { color: rgb(33, 37, 41); text-decoration: none; }

.btn.focus, .btn:focus { outline: 0px; box-shadow: rgba(0, 123, 255, 0.25) =
0px 0px 0px 0.2rem; }

.btn.disabled, .btn:disabled { opacity: 0.65; }

.btn:not(:disabled):not(.disabled) { cursor: pointer; }

a.btn.disabled, fieldset:disabled a.btn { pointer-events: none; }

.btn-primary { color: rgb(255, 255, 255); background-color: rgb(0, 123, 255=
); border-color: rgb(0, 123, 255); }

.btn-primary:hover { color: rgb(255, 255, 255); background-color: rgb(0, 10=
5, 217); border-color: rgb(0, 98, 204); }

.btn-primary.focus, .btn-primary:focus { color: rgb(255, 255, 255); backgro=
und-color: rgb(0, 105, 217); border-color: rgb(0, 98, 204); box-shadow: rgb=
a(38, 143, 255, 0.5) 0px 0px 0px 0.2rem; }

.btn-primary.disabled, .btn-primary:disabled { color: rgb(255, 255, 255); b=
ackground-color: rgb(0, 123, 255); border-color: rgb(0, 123, 255); }

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabl=
ed):not(.disabled):active, .show > .btn-primary.dropdown-toggle { color: rg=
b(255, 255, 255); background-color: rgb(0, 98, 204); border-color: rgb(0, 9=
2, 191); }

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:=
disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle=
:focus { box-shadow: rgba(38, 143, 255, 0.5) 0px 0px 0px 0.2rem; }

.btn-secondary { color: rgb(255, 255, 255); background-color: rgb(108, 117,=
 125); border-color: rgb(108, 117, 125); }

.btn-secondary:hover { color: rgb(255, 255, 255); background-color: rgb(90,=
 98, 104); border-color: rgb(84, 91, 98); }

.btn-secondary.focus, .btn-secondary:focus { color: rgb(255, 255, 255); bac=
kground-color: rgb(90, 98, 104); border-color: rgb(84, 91, 98); box-shadow:=
 rgba(130, 138, 145, 0.5) 0px 0px 0px 0.2rem; }

.btn-secondary.disabled, .btn-secondary:disabled { color: rgb(255, 255, 255=
); background-color: rgb(108, 117, 125); border-color: rgb(108, 117, 125); =
}

.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:di=
sabled):not(.disabled):active, .show > .btn-secondary.dropdown-toggle { col=
or: rgb(255, 255, 255); background-color: rgb(84, 91, 98); border-color: rg=
b(78, 85, 91); }

.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:n=
ot(:disabled):not(.disabled):active:focus, .show > .btn-secondary.dropdown-=
toggle:focus { box-shadow: rgba(130, 138, 145, 0.5) 0px 0px 0px 0.2rem; }

.btn-success { color: rgb(255, 255, 255); background-color: rgb(40, 167, 69=
); border-color: rgb(40, 167, 69); }

.btn-success:hover { color: rgb(255, 255, 255); background-color: rgb(33, 1=
36, 56); border-color: rgb(30, 126, 52); }

.btn-success.focus, .btn-success:focus { color: rgb(255, 255, 255); backgro=
und-color: rgb(33, 136, 56); border-color: rgb(30, 126, 52); box-shadow: rg=
ba(72, 180, 97, 0.5) 0px 0px 0px 0.2rem; }

.btn-success.disabled, .btn-success:disabled { color: rgb(255, 255, 255); b=
ackground-color: rgb(40, 167, 69); border-color: rgb(40, 167, 69); }

.btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabl=
ed):not(.disabled):active, .show > .btn-success.dropdown-toggle { color: rg=
b(255, 255, 255); background-color: rgb(30, 126, 52); border-color: rgb(28,=
 116, 48); }

.btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:=
disabled):not(.disabled):active:focus, .show > .btn-success.dropdown-toggle=
:focus { box-shadow: rgba(72, 180, 97, 0.5) 0px 0px 0px 0.2rem; }

.btn-info { color: rgb(255, 255, 255); background-color: rgb(23, 162, 184);=
 border-color: rgb(23, 162, 184); }

.btn-info:hover { color: rgb(255, 255, 255); background-color: rgb(19, 132,=
 150); border-color: rgb(17, 122, 139); }

.btn-info.focus, .btn-info:focus { color: rgb(255, 255, 255); background-co=
lor: rgb(19, 132, 150); border-color: rgb(17, 122, 139); box-shadow: rgba(5=
8, 176, 195, 0.5) 0px 0px 0px 0.2rem; }

.btn-info.disabled, .btn-info:disabled { color: rgb(255, 255, 255); backgro=
und-color: rgb(23, 162, 184); border-color: rgb(23, 162, 184); }

.btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):no=
t(.disabled):active, .show > .btn-info.dropdown-toggle { color: rgb(255, 25=
5, 255); background-color: rgb(17, 122, 139); border-color: rgb(16, 112, 12=
7); }

.btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabl=
ed):not(.disabled):active:focus, .show > .btn-info.dropdown-toggle:focus { =
box-shadow: rgba(58, 176, 195, 0.5) 0px 0px 0px 0.2rem; }

.btn-warning { color: rgb(33, 37, 41); background-color: rgb(255, 193, 7); =
border-color: rgb(255, 193, 7); }

.btn-warning:hover { color: rgb(33, 37, 41); background-color: rgb(224, 168=
, 0); border-color: rgb(211, 158, 0); }

.btn-warning.focus, .btn-warning:focus { color: rgb(33, 37, 41); background=
-color: rgb(224, 168, 0); border-color: rgb(211, 158, 0); box-shadow: rgba(=
222, 170, 12, 0.5) 0px 0px 0px 0.2rem; }

.btn-warning.disabled, .btn-warning:disabled { color: rgb(33, 37, 41); back=
ground-color: rgb(255, 193, 7); border-color: rgb(255, 193, 7); }

.btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabl=
ed):not(.disabled):active, .show > .btn-warning.dropdown-toggle { color: rg=
b(33, 37, 41); background-color: rgb(211, 158, 0); border-color: rgb(198, 1=
49, 0); }

.btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:=
disabled):not(.disabled):active:focus, .show > .btn-warning.dropdown-toggle=
:focus { box-shadow: rgba(222, 170, 12, 0.5) 0px 0px 0px 0.2rem; }

.btn-danger { color: rgb(255, 255, 255); background-color: rgb(220, 53, 69)=
; border-color: rgb(220, 53, 69); }

.btn-danger:hover { color: rgb(255, 255, 255); background-color: rgb(200, 3=
5, 51); border-color: rgb(189, 33, 48); }

.btn-danger.focus, .btn-danger:focus { color: rgb(255, 255, 255); backgroun=
d-color: rgb(200, 35, 51); border-color: rgb(189, 33, 48); box-shadow: rgba=
(225, 83, 97, 0.5) 0px 0px 0px 0.2rem; }

.btn-danger.disabled, .btn-danger:disabled { color: rgb(255, 255, 255); bac=
kground-color: rgb(220, 53, 69); border-color: rgb(220, 53, 69); }

.btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled=
):not(.disabled):active, .show > .btn-danger.dropdown-toggle { color: rgb(2=
55, 255, 255); background-color: rgb(189, 33, 48); border-color: rgb(178, 3=
1, 45); }

.btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:di=
sabled):not(.disabled):active:focus, .show > .btn-danger.dropdown-toggle:fo=
cus { box-shadow: rgba(225, 83, 97, 0.5) 0px 0px 0px 0.2rem; }

.btn-light { color: rgb(33, 37, 41); background-color: rgb(248, 249, 250); =
border-color: rgb(248, 249, 250); }

.btn-light:hover { color: rgb(33, 37, 41); background-color: rgb(226, 230, =
234); border-color: rgb(218, 224, 229); }

.btn-light.focus, .btn-light:focus { color: rgb(33, 37, 41); background-col=
or: rgb(226, 230, 234); border-color: rgb(218, 224, 229); box-shadow: rgba(=
216, 217, 219, 0.5) 0px 0px 0px 0.2rem; }

.btn-light.disabled, .btn-light:disabled { color: rgb(33, 37, 41); backgrou=
nd-color: rgb(248, 249, 250); border-color: rgb(248, 249, 250); }

.btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):=
not(.disabled):active, .show > .btn-light.dropdown-toggle { color: rgb(33, =
37, 41); background-color: rgb(218, 224, 229); border-color: rgb(211, 217, =
223); }

.btn-light:not(:disabled):not(.disabled).active:focus, .btn-light:not(:disa=
bled):not(.disabled):active:focus, .show > .btn-light.dropdown-toggle:focus=
 { box-shadow: rgba(216, 217, 219, 0.5) 0px 0px 0px 0.2rem; }

.btn-dark { color: rgb(255, 255, 255); background-color: rgb(52, 58, 64); b=
order-color: rgb(52, 58, 64); }

.btn-dark:hover { color: rgb(255, 255, 255); background-color: rgb(35, 39, =
43); border-color: rgb(29, 33, 36); }

.btn-dark.focus, .btn-dark:focus { color: rgb(255, 255, 255); background-co=
lor: rgb(35, 39, 43); border-color: rgb(29, 33, 36); box-shadow: rgba(82, 8=
8, 93, 0.5) 0px 0px 0px 0.2rem; }

.btn-dark.disabled, .btn-dark:disabled { color: rgb(255, 255, 255); backgro=
und-color: rgb(52, 58, 64); border-color: rgb(52, 58, 64); }

.btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):no=
t(.disabled):active, .show > .btn-dark.dropdown-toggle { color: rgb(255, 25=
5, 255); background-color: rgb(29, 33, 36); border-color: rgb(23, 26, 29); =
}

.btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabl=
ed):not(.disabled):active:focus, .show > .btn-dark.dropdown-toggle:focus { =
box-shadow: rgba(82, 88, 93, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-primary { color: rgb(0, 123, 255); border-color: rgb(0, 123, 2=
55); }

.btn-outline-primary:hover { color: rgb(255, 255, 255); background-color: r=
gb(0, 123, 255); border-color: rgb(0, 123, 255); }

.btn-outline-primary.focus, .btn-outline-primary:focus { box-shadow: rgba(0=
, 123, 255, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-primary.disabled, .btn-outline-primary:disabled { color: rgb(0=
, 123, 255); background-color: transparent; }

.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-pri=
mary:not(:disabled):not(.disabled):active, .show > .btn-outline-primary.dro=
pdown-toggle { color: rgb(255, 255, 255); background-color: rgb(0, 123, 255=
); border-color: rgb(0, 123, 255); }

.btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outli=
ne-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline=
-primary.dropdown-toggle:focus { box-shadow: rgba(0, 123, 255, 0.5) 0px 0px=
 0px 0.2rem; }

.btn-outline-secondary { color: rgb(108, 117, 125); border-color: rgb(108, =
117, 125); }

.btn-outline-secondary:hover { color: rgb(255, 255, 255); background-color:=
 rgb(108, 117, 125); border-color: rgb(108, 117, 125); }

.btn-outline-secondary.focus, .btn-outline-secondary:focus { box-shadow: rg=
ba(108, 117, 125, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-secondary.disabled, .btn-outline-secondary:disabled { color: r=
gb(108, 117, 125); background-color: transparent; }

.btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-s=
econdary:not(:disabled):not(.disabled):active, .show > .btn-outline-seconda=
ry.dropdown-toggle { color: rgb(255, 255, 255); background-color: rgb(108, =
117, 125); border-color: rgb(108, 117, 125); }

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-out=
line-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-out=
line-secondary.dropdown-toggle:focus { box-shadow: rgba(108, 117, 125, 0.5)=
 0px 0px 0px 0.2rem; }

.btn-outline-success { color: rgb(40, 167, 69); border-color: rgb(40, 167, =
69); }

.btn-outline-success:hover { color: rgb(255, 255, 255); background-color: r=
gb(40, 167, 69); border-color: rgb(40, 167, 69); }

.btn-outline-success.focus, .btn-outline-success:focus { box-shadow: rgba(4=
0, 167, 69, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-success.disabled, .btn-outline-success:disabled { color: rgb(4=
0, 167, 69); background-color: transparent; }

.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-suc=
cess:not(:disabled):not(.disabled):active, .show > .btn-outline-success.dro=
pdown-toggle { color: rgb(255, 255, 255); background-color: rgb(40, 167, 69=
); border-color: rgb(40, 167, 69); }

.btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outli=
ne-success:not(:disabled):not(.disabled):active:focus, .show > .btn-outline=
-success.dropdown-toggle:focus { box-shadow: rgba(40, 167, 69, 0.5) 0px 0px=
 0px 0.2rem; }

.btn-outline-info { color: rgb(23, 162, 184); border-color: rgb(23, 162, 18=
4); }

.btn-outline-info:hover { color: rgb(255, 255, 255); background-color: rgb(=
23, 162, 184); border-color: rgb(23, 162, 184); }

.btn-outline-info.focus, .btn-outline-info:focus { box-shadow: rgba(23, 162=
, 184, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-info.disabled, .btn-outline-info:disabled { color: rgb(23, 162=
, 184); background-color: transparent; }

.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:n=
ot(:disabled):not(.disabled):active, .show > .btn-outline-info.dropdown-tog=
gle { color: rgb(255, 255, 255); background-color: rgb(23, 162, 184); borde=
r-color: rgb(23, 162, 184); }

.btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-=
info:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-info.=
dropdown-toggle:focus { box-shadow: rgba(23, 162, 184, 0.5) 0px 0px 0px 0.2=
rem; }

.btn-outline-warning { color: rgb(255, 193, 7); border-color: rgb(255, 193,=
 7); }

.btn-outline-warning:hover { color: rgb(33, 37, 41); background-color: rgb(=
255, 193, 7); border-color: rgb(255, 193, 7); }

.btn-outline-warning.focus, .btn-outline-warning:focus { box-shadow: rgba(2=
55, 193, 7, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-warning.disabled, .btn-outline-warning:disabled { color: rgb(2=
55, 193, 7); background-color: transparent; }

.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-war=
ning:not(:disabled):not(.disabled):active, .show > .btn-outline-warning.dro=
pdown-toggle { color: rgb(33, 37, 41); background-color: rgb(255, 193, 7); =
border-color: rgb(255, 193, 7); }

.btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outli=
ne-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-outline=
-warning.dropdown-toggle:focus { box-shadow: rgba(255, 193, 7, 0.5) 0px 0px=
 0px 0.2rem; }

.btn-outline-danger { color: rgb(220, 53, 69); border-color: rgb(220, 53, 6=
9); }

.btn-outline-danger:hover { color: rgb(255, 255, 255); background-color: rg=
b(220, 53, 69); border-color: rgb(220, 53, 69); }

.btn-outline-danger.focus, .btn-outline-danger:focus { box-shadow: rgba(220=
, 53, 69, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-danger.disabled, .btn-outline-danger:disabled { color: rgb(220=
, 53, 69); background-color: transparent; }

.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-dang=
er:not(:disabled):not(.disabled):active, .show > .btn-outline-danger.dropdo=
wn-toggle { color: rgb(255, 255, 255); background-color: rgb(220, 53, 69); =
border-color: rgb(220, 53, 69); }

.btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outlin=
e-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-d=
anger.dropdown-toggle:focus { box-shadow: rgba(220, 53, 69, 0.5) 0px 0px 0p=
x 0.2rem; }

.btn-outline-light { color: rgb(248, 249, 250); border-color: rgb(248, 249,=
 250); }

.btn-outline-light:hover { color: rgb(33, 37, 41); background-color: rgb(24=
8, 249, 250); border-color: rgb(248, 249, 250); }

.btn-outline-light.focus, .btn-outline-light:focus { box-shadow: rgba(248, =
249, 250, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-light.disabled, .btn-outline-light:disabled { color: rgb(248, =
249, 250); background-color: transparent; }

.btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light=
:not(:disabled):not(.disabled):active, .show > .btn-outline-light.dropdown-=
toggle { color: rgb(33, 37, 41); background-color: rgb(248, 249, 250); bord=
er-color: rgb(248, 249, 250); }

.btn-outline-light:not(:disabled):not(.disabled).active:focus, .btn-outline=
-light:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-lig=
ht.dropdown-toggle:focus { box-shadow: rgba(248, 249, 250, 0.5) 0px 0px 0px=
 0.2rem; }

.btn-outline-dark { color: rgb(52, 58, 64); border-color: rgb(52, 58, 64); =
}

.btn-outline-dark:hover { color: rgb(255, 255, 255); background-color: rgb(=
52, 58, 64); border-color: rgb(52, 58, 64); }

.btn-outline-dark.focus, .btn-outline-dark:focus { box-shadow: rgba(52, 58,=
 64, 0.5) 0px 0px 0px 0.2rem; }

.btn-outline-dark.disabled, .btn-outline-dark:disabled { color: rgb(52, 58,=
 64); background-color: transparent; }

.btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:n=
ot(:disabled):not(.disabled):active, .show > .btn-outline-dark.dropdown-tog=
gle { color: rgb(255, 255, 255); background-color: rgb(52, 58, 64); border-=
color: rgb(52, 58, 64); }

.btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-=
dark:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-dark.=
dropdown-toggle:focus { box-shadow: rgba(52, 58, 64, 0.5) 0px 0px 0px 0.2re=
m; }

.btn-link { font-weight: 400; color: rgb(0, 123, 255); text-decoration: non=
e; }

.btn-link:hover { color: rgb(0, 86, 179); text-decoration: underline; }

.btn-link.focus, .btn-link:focus { text-decoration: underline; }

.btn-link.disabled, .btn-link:disabled { color: rgb(108, 117, 125); pointer=
-events: none; }

.btn-group-lg > .btn, .btn-lg { padding: 0.5rem 1rem; font-size: 1.25rem; l=
ine-height: 1.5; border-radius: 0.3rem; }

.btn-group-sm > .btn, .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875re=
m; line-height: 1.5; border-radius: 0.2rem; }

.btn-block { display: block; width: 100%; }

.btn-block + .btn-block { margin-top: 0.5rem; }

input[type=3D"button"].btn-block, input[type=3D"reset"].btn-block, input[ty=
pe=3D"submit"].btn-block { width: 100%; }

.fade { transition: opacity 0.15s linear 0s; }

@media (prefers-reduced-motion: reduce) {
  .fade { transition: none 0s ease 0s; }
}

.fade:not(.show) { opacity: 0; }

.collapse:not(.show) { display: none; }

.collapsing { position: relative; height: 0px; overflow: hidden; transition=
: height 0.35s ease 0s; }

@media (prefers-reduced-motion: reduce) {
  .collapsing { transition: none 0s ease 0s; }
}

.dropdown, .dropleft, .dropright, .dropup { position: relative; }

.dropdown-toggle { white-space: nowrap; }

.dropdown-toggle::after { display: inline-block; margin-left: 0.255em; vert=
ical-align: 0.255em; content: ""; border-width: 0.3em 0.3em 0px; border-top=
-style: solid; border-top-color: initial; border-right-style: solid; border=
-right-color: transparent; border-bottom-style: initial; border-bottom-colo=
r: initial; border-left-style: solid; border-left-color: transparent; }

.dropdown-toggle:empty::after { margin-left: 0px; }

.dropdown-menu { position: absolute; top: 100%; left: 0px; z-index: 1000; d=
isplay: none; float: left; min-width: 10rem; padding: 0.5rem 0px; margin: 0=
.125rem 0px 0px; font-size: 1rem; color: rgb(33, 37, 41); text-align: left;=
 list-style: none; background-color: rgb(255, 255, 255); background-clip: p=
adding-box; border: 1px solid rgba(0, 0, 0, 0.15); border-radius: 0.25rem; =
}

.dropdown-menu-left { right: auto; left: 0px; }

.dropdown-menu-right { right: 0px; left: auto; }

@media (min-width: 576px) {
  .dropdown-menu-sm-left { right: auto; left: 0px; }
  .dropdown-menu-sm-right { right: 0px; left: auto; }
}

@media (min-width: 768px) {
  .dropdown-menu-md-left { right: auto; left: 0px; }
  .dropdown-menu-md-right { right: 0px; left: auto; }
}

@media (min-width: 992px) {
  .dropdown-menu-lg-left { right: auto; left: 0px; }
  .dropdown-menu-lg-right { right: 0px; left: auto; }
}

@media (min-width: 1200px) {
  .dropdown-menu-xl-left { right: auto; left: 0px; }
  .dropdown-menu-xl-right { right: 0px; left: auto; }
}

.dropup .dropdown-menu { top: auto; bottom: 100%; margin-top: 0px; margin-b=
ottom: 0.125rem; }

.dropup .dropdown-toggle::after { display: inline-block; margin-left: 0.255=
em; vertical-align: 0.255em; content: ""; border-width: 0px 0.3em 0.3em; bo=
rder-top-style: initial; border-top-color: initial; border-right-style: sol=
id; border-right-color: transparent; border-bottom-style: solid; border-bot=
tom-color: initial; border-left-style: solid; border-left-color: transparen=
t; }

.dropup .dropdown-toggle:empty::after { margin-left: 0px; }

.dropright .dropdown-menu { top: 0px; right: auto; left: 100%; margin-top: =
0px; margin-left: 0.125rem; }

.dropright .dropdown-toggle::after { display: inline-block; margin-left: 0.=
255em; vertical-align: 0.255em; content: ""; border-width: 0.3em 0px 0.3em =
0.3em; border-top-style: solid; border-top-color: transparent; border-right=
-style: initial; border-right-color: initial; border-bottom-style: solid; b=
order-bottom-color: transparent; border-left-style: solid; border-left-colo=
r: initial; }

.dropright .dropdown-toggle:empty::after { margin-left: 0px; }

.dropright .dropdown-toggle::after { vertical-align: 0px; }

.dropleft .dropdown-menu { top: 0px; right: 100%; left: auto; margin-top: 0=
px; margin-right: 0.125rem; }

.dropleft .dropdown-toggle::after { display: inline-block; margin-left: 0.2=
55em; vertical-align: 0.255em; content: ""; }

.dropleft .dropdown-toggle::after { display: none; }

.dropleft .dropdown-toggle::before { display: inline-block; margin-right: 0=
.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid trans=
parent; border-right: 0.3em solid; border-bottom: 0.3em solid transparent; =
}

.dropleft .dropdown-toggle:empty::after { margin-left: 0px; }

.dropleft .dropdown-toggle::before { vertical-align: 0px; }

.dropdown-menu[x-placement^=3D"bottom"], .dropdown-menu[x-placement^=3D"lef=
t"], .dropdown-menu[x-placement^=3D"right"], .dropdown-menu[x-placement^=3D=
"top"] { right: auto; bottom: auto; }

.dropdown-divider { height: 0px; margin: 0.5rem 0px; overflow: hidden; bord=
er-top: 1px solid rgb(233, 236, 239); }

.dropdown-item { display: block; width: 100%; padding: 0.25rem 1.5rem; clea=
r: both; font-weight: 400; color: rgb(33, 37, 41); text-align: inherit; whi=
te-space: nowrap; background-color: transparent; border: 0px; }

.dropdown-item:focus, .dropdown-item:hover { color: rgb(22, 24, 27); text-d=
ecoration: none; background-color: rgb(233, 236, 239); }

.dropdown-item.active, .dropdown-item:active { color: rgb(255, 255, 255); t=
ext-decoration: none; background-color: rgb(0, 123, 255); }

.dropdown-item.disabled, .dropdown-item:disabled { color: rgb(173, 181, 189=
); pointer-events: none; background-color: transparent; }

.dropdown-menu.show { display: block; }

.dropdown-header { display: block; padding: 0.5rem 1.5rem; margin-bottom: 0=
px; font-size: 0.875rem; color: rgb(108, 117, 125); white-space: nowrap; }

.dropdown-item-text { display: block; padding: 0.25rem 1.5rem; color: rgb(3=
3, 37, 41); }

.btn-group, .btn-group-vertical { position: relative; display: inline-flex;=
 vertical-align: middle; }

.btn-group-vertical > .btn, .btn-group > .btn { position: relative; flex: 1=
 1 auto; }

.btn-group-vertical > .btn:hover, .btn-group > .btn:hover { z-index: 1; }

.btn-group-vertical > .btn.active, .btn-group-vertical > .btn:active, .btn-=
group-vertical > .btn:focus, .btn-group > .btn.active, .btn-group > .btn:ac=
tive, .btn-group > .btn:focus { z-index: 1; }

.btn-toolbar { display: flex; flex-wrap: wrap; justify-content: flex-start;=
 }

.btn-toolbar .input-group { width: auto; }

.btn-group > .btn-group:not(:first-child), .btn-group > .btn:not(:first-chi=
ld) { margin-left: -1px; }

.btn-group > .btn-group:not(:last-child) > .btn, .btn-group > .btn:not(:las=
t-child):not(.dropdown-toggle) { border-top-right-radius: 0px; border-botto=
m-right-radius: 0px; }

.btn-group > .btn-group:not(:first-child) > .btn, .btn-group > .btn:not(:fi=
rst-child) { border-top-left-radius: 0px; border-bottom-left-radius: 0px; }

.dropdown-toggle-split { padding-right: 0.5625rem; padding-left: 0.5625rem;=
 }

.dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after, .d=
ropup .dropdown-toggle-split::after { margin-left: 0px; }

.dropleft .dropdown-toggle-split::before { margin-right: 0px; }

.btn-group-sm > .btn + .dropdown-toggle-split, .btn-sm + .dropdown-toggle-s=
plit { padding-right: 0.375rem; padding-left: 0.375rem; }

.btn-group-lg > .btn + .dropdown-toggle-split, .btn-lg + .dropdown-toggle-s=
plit { padding-right: 0.75rem; padding-left: 0.75rem; }

.btn-group-vertical { flex-direction: column; align-items: flex-start; just=
ify-content: center; }

.btn-group-vertical > .btn, .btn-group-vertical > .btn-group { width: 100%;=
 }

.btn-group-vertical > .btn-group:not(:first-child), .btn-group-vertical > .=
btn:not(:first-child) { margin-top: -1px; }

.btn-group-vertical > .btn-group:not(:last-child) > .btn, .btn-group-vertic=
al > .btn:not(:last-child):not(.dropdown-toggle) { border-bottom-right-radi=
us: 0px; border-bottom-left-radius: 0px; }

.btn-group-vertical > .btn-group:not(:first-child) > .btn, .btn-group-verti=
cal > .btn:not(:first-child) { border-top-left-radius: 0px; border-top-righ=
t-radius: 0px; }

.btn-group-toggle > .btn, .btn-group-toggle > .btn-group > .btn { margin-bo=
ttom: 0px; }

.btn-group-toggle > .btn input[type=3D"checkbox"], .btn-group-toggle > .btn=
 input[type=3D"radio"], .btn-group-toggle > .btn-group > .btn input[type=3D=
"checkbox"], .btn-group-toggle > .btn-group > .btn input[type=3D"radio"] { =
position: absolute; clip: rect(0px, 0px, 0px, 0px); pointer-events: none; }

.input-group { position: relative; display: flex; flex-wrap: wrap; align-it=
ems: stretch; width: 100%; }

.input-group > .custom-file, .input-group > .custom-select, .input-group > =
.form-control, .input-group > .form-control-plaintext { position: relative;=
 flex: 1 1 auto; width: 1%; min-width: 0px; margin-bottom: 0px; }

.input-group > .custom-file + .custom-file, .input-group > .custom-file + .=
custom-select, .input-group > .custom-file + .form-control, .input-group > =
.custom-select + .custom-file, .input-group > .custom-select + .custom-sele=
ct, .input-group > .custom-select + .form-control, .input-group > .form-con=
trol + .custom-file, .input-group > .form-control + .custom-select, .input-=
group > .form-control + .form-control, .input-group > .form-control-plainte=
xt + .custom-file, .input-group > .form-control-plaintext + .custom-select,=
 .input-group > .form-control-plaintext + .form-control { margin-left: -1px=
; }

.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label, =
.input-group > .custom-select:focus, .input-group > .form-control:focus { z=
-index: 3; }

.input-group > .custom-file .custom-file-input:focus { z-index: 4; }

.input-group > .custom-select:not(:first-child), .input-group > .form-contr=
ol:not(:first-child) { border-top-left-radius: 0px; border-bottom-left-radi=
us: 0px; }

.input-group > .custom-file { display: flex; align-items: center; }

.input-group > .custom-file:not(:last-child) .custom-file-label, .input-gro=
up > .custom-file:not(:last-child) .custom-file-label::after { border-top-r=
ight-radius: 0px; border-bottom-right-radius: 0px; }

.input-group > .custom-file:not(:first-child) .custom-file-label { border-t=
op-left-radius: 0px; border-bottom-left-radius: 0px; }

.input-group:not(.has-validation) > .custom-file:not(:last-child) .custom-f=
ile-label, .input-group:not(.has-validation) > .custom-file:not(:last-child=
) .custom-file-label::after, .input-group:not(.has-validation) > .custom-se=
lect:not(:last-child), .input-group:not(.has-validation) > .form-control:no=
t(:last-child) { border-top-right-radius: 0px; border-bottom-right-radius: =
0px; }

.input-group.has-validation > .custom-file:nth-last-child(n+3) .custom-file=
-label, .input-group.has-validation > .custom-file:nth-last-child(n+3) .cus=
tom-file-label::after, .input-group.has-validation > .custom-select:nth-las=
t-child(n+3), .input-group.has-validation > .form-control:nth-last-child(n+=
3) { border-top-right-radius: 0px; border-bottom-right-radius: 0px; }

.input-group-append, .input-group-prepend { display: flex; }

.input-group-append .btn, .input-group-prepend .btn { position: relative; z=
-index: 2; }

.input-group-append .btn:focus, .input-group-prepend .btn:focus { z-index: =
3; }

.input-group-append .btn + .btn, .input-group-append .btn + .input-group-te=
xt, .input-group-append .input-group-text + .btn, .input-group-append .inpu=
t-group-text + .input-group-text, .input-group-prepend .btn + .btn, .input-=
group-prepend .btn + .input-group-text, .input-group-prepend .input-group-t=
ext + .btn, .input-group-prepend .input-group-text + .input-group-text { ma=
rgin-left: -1px; }

.input-group-prepend { margin-right: -1px; }

.input-group-append { margin-left: -1px; }

.input-group-text { display: flex; align-items: center; padding: 0.375rem 0=
.75rem; margin-bottom: 0px; font-size: 1rem; font-weight: 400; line-height:=
 1.5; color: rgb(73, 80, 87); text-align: center; white-space: nowrap; back=
ground-color: rgb(233, 236, 239); border: 1px solid rgb(206, 212, 218); bor=
der-radius: 0.25rem; }

.input-group-text input[type=3D"checkbox"], .input-group-text input[type=3D=
"radio"] { margin-top: 0px; }

.input-group-lg > .custom-select, .input-group-lg > .form-control:not(texta=
rea) { height: calc(1.5em + 2px + 1rem); }

.input-group-lg > .custom-select, .input-group-lg > .form-control, .input-g=
roup-lg > .input-group-append > .btn, .input-group-lg > .input-group-append=
 > .input-group-text, .input-group-lg > .input-group-prepend > .btn, .input=
-group-lg > .input-group-prepend > .input-group-text { padding: 0.5rem 1rem=
; font-size: 1.25rem; line-height: 1.5; border-radius: 0.3rem; }

.input-group-sm > .custom-select, .input-group-sm > .form-control:not(texta=
rea) { height: calc(1.5em + 2px + 0.5rem); }

.input-group-sm > .custom-select, .input-group-sm > .form-control, .input-g=
roup-sm > .input-group-append > .btn, .input-group-sm > .input-group-append=
 > .input-group-text, .input-group-sm > .input-group-prepend > .btn, .input=
-group-sm > .input-group-prepend > .input-group-text { padding: 0.25rem 0.5=
rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; }

.input-group-lg > .custom-select, .input-group-sm > .custom-select { paddin=
g-right: 1.75rem; }

.input-group.has-validation > .input-group-append:nth-last-child(n+3) > .bt=
n, .input-group.has-validation > .input-group-append:nth-last-child(n+3) > =
.input-group-text, .input-group:not(.has-validation) > .input-group-append:=
not(:last-child) > .btn, .input-group:not(.has-validation) > .input-group-a=
ppend:not(:last-child) > .input-group-text, .input-group > .input-group-app=
end:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group =
> .input-group-append:last-child > .input-group-text:not(:last-child), .inp=
ut-group > .input-group-prepend > .btn, .input-group > .input-group-prepend=
 > .input-group-text { border-top-right-radius: 0px; border-bottom-right-ra=
dius: 0px; }

.input-group > .input-group-append > .btn, .input-group > .input-group-appe=
nd > .input-group-text, .input-group > .input-group-prepend:first-child > .=
btn:not(:first-child), .input-group > .input-group-prepend:first-child > .i=
nput-group-text:not(:first-child), .input-group > .input-group-prepend:not(=
:first-child) > .btn, .input-group > .input-group-prepend:not(:first-child)=
 > .input-group-text { border-top-left-radius: 0px; border-bottom-left-radi=
us: 0px; }

.custom-control { position: relative; z-index: 1; display: block; min-heigh=
t: 1.5rem; padding-left: 1.5rem; -webkit-print-color-adjust: exact; }

.custom-control-inline { display: inline-flex; margin-right: 1rem; }

.custom-control-input { position: absolute; left: 0px; z-index: -1; width: =
1rem; height: 1.25rem; opacity: 0; }

.custom-control-input:checked ~ .custom-control-label::before { color: rgb(=
255, 255, 255); border-color: rgb(0, 123, 255); background-color: rgb(0, 12=
3, 255); }

.custom-control-input:focus ~ .custom-control-label::before { box-shadow: r=
gba(0, 123, 255, 0.25) 0px 0px 0px 0.2rem; }

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {=
 border-color: rgb(128, 189, 255); }

.custom-control-input:not(:disabled):active ~ .custom-control-label::before=
 { color: rgb(255, 255, 255); background-color: rgb(179, 215, 255); border-=
color: rgb(179, 215, 255); }

.custom-control-input:disabled ~ .custom-control-label, .custom-control-inp=
ut[disabled] ~ .custom-control-label { color: rgb(108, 117, 125); }

.custom-control-input:disabled ~ .custom-control-label::before, .custom-con=
trol-input[disabled] ~ .custom-control-label::before { background-color: rg=
b(233, 236, 239); }

.custom-control-label { position: relative; margin-bottom: 0px; vertical-al=
ign: top; }

.custom-control-label::before { position: absolute; top: 0.25rem; left: -1.=
5rem; display: block; width: 1rem; height: 1rem; pointer-events: none; cont=
ent: ""; background-color: rgb(255, 255, 255); border: 1px solid rgb(173, 1=
81, 189); }

.custom-control-label::after { position: absolute; top: 0.25rem; left: -1.5=
rem; display: block; width: 1rem; height: 1rem; content: ""; background: 50=
% center / 50% 50% no-repeat; }

.custom-checkbox .custom-control-label::before { border-radius: 0.25rem; }

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::aft=
er { background-image: url("data:image/svg+xml,%3csvg xmlns=3D'http://www.w=
3.org/2000/svg' width=3D'8' height=3D'8' viewBox=3D'0 0 8 8'%3e%3cpath fill=
=3D'%23fff' d=3D'M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193=
z'/%3e%3c/svg%3e"); }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-labe=
l::before { border-color: rgb(0, 123, 255); background-color: rgb(0, 123, 2=
55); }

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-labe=
l::after { background-image: url("data:image/svg+xml,%3csvg xmlns=3D'http:/=
/www.w3.org/2000/svg' width=3D'4' height=3D'4' viewBox=3D'0 0 4 4'%3e%3cpat=
h stroke=3D'%23fff' d=3D'M0 2h4'/%3e%3c/svg%3e"); }

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-l=
abel::before { background-color: rgba(0, 123, 255, 0.5); }

.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-con=
trol-label::before { background-color: rgba(0, 123, 255, 0.5); }

.custom-radio .custom-control-label::before { border-radius: 50%; }

.custom-radio .custom-control-input:checked ~ .custom-control-label::after =
{ background-image: url("data:image/svg+xml,%3csvg xmlns=3D'http://www.w3.o=
rg/2000/svg' width=3D'12' height=3D'12' viewBox=3D'-4 -4 8 8'%3e%3ccircle r=
=3D'3' fill=3D'%23fff'/%3e%3c/svg%3e"); }

.custom-radio .custom-control-input:disabled:checked ~ .custom-control-labe=
l::before { background-color: rgba(0, 123, 255, 0.5); }

.custom-switch { padding-left: 2.25rem; }

.custom-switch .custom-control-label::before { left: -2.25rem; width: 1.75r=
em; pointer-events: all; border-radius: 0.5rem; }

.custom-switch .custom-control-label::after { top: calc(2px + 0.25rem); lef=
t: calc(2px - 2.25rem); width: calc(-4px + 1rem); height: calc(-4px + 1rem)=
; background-color: rgb(173, 181, 189); border-radius: 0.5rem; transition: =
transform 0.15s ease-in-out 0s, background-color 0.15s ease-in-out 0s, bord=
er-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s, -webkit-tra=
nsform 0.15s ease-in-out 0s; }

@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after { transition: none 0s ease 0s=
; }
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after=
 { background-color: rgb(255, 255, 255); transform: translateX(0.75rem); }

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-lab=
el::before { background-color: rgba(0, 123, 255, 0.5); }

.custom-select { display: inline-block; width: 100%; height: calc(1.5em + 2=
px + 0.75rem); padding: 0.375rem 1.75rem 0.375rem 0.75rem; font-size: 1rem;=
 font-weight: 400; line-height: 1.5; color: rgb(73, 80, 87); vertical-align=
: middle; background: url("data:image/svg+xml,%3csvg xmlns=3D'http://www.w3=
.org/2000/svg' width=3D'4' height=3D'5' viewBox=3D'0 0 4 5'%3e%3cpath fill=
=3D'%23343a40' d=3D'M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem c=
enter / 8px 10px no-repeat rgb(255, 255, 255); border: 1px solid rgb(206, 2=
12, 218); border-radius: 0.25rem; appearance: none; }

.custom-select:focus { border-color: rgb(128, 189, 255); outline: 0px; box-=
shadow: rgba(0, 123, 255, 0.25) 0px 0px 0px 0.2rem; }

.custom-select[multiple], .custom-select[size]:not([size=3D"1"]) { height: =
auto; padding-right: 0.75rem; background-image: none; }

.custom-select:disabled { color: rgb(108, 117, 125); background-color: rgb(=
233, 236, 239); }

.custom-select-sm { height: calc(1.5em + 2px + 0.5rem); padding-top: 0.25re=
m; padding-bottom: 0.25rem; padding-left: 0.5rem; font-size: 0.875rem; }

.custom-select-lg { height: calc(1.5em + 2px + 1rem); padding-top: 0.5rem; =
padding-bottom: 0.5rem; padding-left: 1rem; font-size: 1.25rem; }

.custom-file { position: relative; display: inline-block; width: 100%; heig=
ht: calc(1.5em + 2px + 0.75rem); margin-bottom: 0px; }

.custom-file-input { position: relative; z-index: 2; width: 100%; height: c=
alc(1.5em + 2px + 0.75rem); margin: 0px; overflow: hidden; opacity: 0; }

.custom-file-input:focus ~ .custom-file-label { border-color: rgb(128, 189,=
 255); box-shadow: rgba(0, 123, 255, 0.25) 0px 0px 0px 0.2rem; }

.custom-file-input:disabled ~ .custom-file-label, .custom-file-input[disabl=
ed] ~ .custom-file-label { background-color: rgb(233, 236, 239); }

.custom-file-input:lang(en) ~ .custom-file-label::after { content: "Browse"=
; }

.custom-file-input ~ .custom-file-label[data-browse]::after { content: attr=
(data-browse); }

.custom-file-label { position: absolute; top: 0px; right: 0px; left: 0px; z=
-index: 1; height: calc(1.5em + 2px + 0.75rem); padding: 0.375rem 0.75rem; =
overflow: hidden; font-weight: 400; line-height: 1.5; color: rgb(73, 80, 87=
); background-color: rgb(255, 255, 255); border: 1px solid rgb(206, 212, 21=
8); border-radius: 0.25rem; }

.custom-file-label::after { position: absolute; top: 0px; right: 0px; botto=
m: 0px; z-index: 3; display: block; height: calc(1.5em + 0.75rem); padding:=
 0.375rem 0.75rem; line-height: 1.5; color: rgb(73, 80, 87); content: "Brow=
se"; background-color: rgb(233, 236, 239); border-left: inherit; border-rad=
ius: 0px 0.25rem 0.25rem 0px; }

.custom-range { width: 100%; height: 1.4rem; padding: 0px; background-color=
: transparent; appearance: none; }

.custom-range:focus { outline: 0px; }

.custom-range:focus::-webkit-slider-thumb { box-shadow: rgb(255, 255, 255) =
0px 0px 0px 1px, rgba(0, 123, 255, 0.25) 0px 0px 0px 0.2rem; }

.custom-range::-webkit-slider-thumb { width: 1rem; height: 1rem; margin-top=
: -0.25rem; background-color: rgb(0, 123, 255); border: 0px; border-radius:=
 1rem; transition: background-color 0.15s ease-in-out 0s, border-color 0.15=
s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s; appearance: none; }

@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb { transition: none 0s ease 0s; }
}

.custom-range::-webkit-slider-thumb:active { background-color: rgb(179, 215=
, 255); }

.custom-range::-webkit-slider-runnable-track { width: 100%; height: 0.5rem;=
 color: transparent; cursor: pointer; background-color: rgb(222, 226, 230);=
 border-color: transparent; border-radius: 1rem; }

@media (prefers-reduced-motion: reduce) {
}

@media (prefers-reduced-motion: reduce) {
}

.custom-range:disabled::-webkit-slider-thumb { background-color: rgb(173, 1=
81, 189); }

.custom-range:disabled::-webkit-slider-runnable-track { cursor: default; }

.custom-control-label::before, .custom-file-label, .custom-select { transit=
ion: background-color 0.15s ease-in-out 0s, border-color 0.15s ease-in-out =
0s, box-shadow 0.15s ease-in-out 0s; }

@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before, .custom-file-label, .custom-select { trans=
ition: none 0s ease 0s; }
}

.nav { display: flex; flex-wrap: wrap; padding-left: 0px; margin-bottom: 0p=
x; list-style: none; }

.nav-link { display: block; padding: 0.5rem 1rem; }

.nav-link:focus, .nav-link:hover { text-decoration: none; }

.nav-link.disabled { color: rgb(108, 117, 125); pointer-events: none; curso=
r: default; }

.nav-tabs { border-bottom: 1px solid rgb(222, 226, 230); }

.nav-tabs .nav-link { margin-bottom: -1px; border: 1px solid transparent; b=
order-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem; }

.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover { border-color: rgb(23=
3, 236, 239) rgb(233, 236, 239) rgb(222, 226, 230); }

.nav-tabs .nav-link.disabled { color: rgb(108, 117, 125); background-color:=
 transparent; border-color: transparent; }

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active { color: rgb=
(73, 80, 87); background-color: rgb(255, 255, 255); border-color: rgb(222, =
226, 230) rgb(222, 226, 230) rgb(255, 255, 255); }

.nav-tabs .dropdown-menu { margin-top: -1px; border-top-left-radius: 0px; b=
order-top-right-radius: 0px; }

.nav-pills .nav-link { border-radius: 0.25rem; }

.nav-pills .nav-link.active, .nav-pills .show > .nav-link { color: rgb(255,=
 255, 255); background-color: rgb(0, 123, 255); }

.nav-fill .nav-item, .nav-fill > .nav-link { flex: 1 1 auto; text-align: ce=
nter; }

.nav-justified .nav-item, .nav-justified > .nav-link { flex-basis: 0px; fle=
x-grow: 1; text-align: center; }

.tab-content > .tab-pane { display: none; }

.tab-content > .active { display: block; }

.navbar { position: relative; display: flex; flex-wrap: wrap; align-items: =
center; justify-content: space-between; padding: 0.5rem 1rem; }

.navbar .container, .navbar .container-fluid, .navbar .container-lg, .navba=
r .container-md, .navbar .container-sm, .navbar .container-xl { display: fl=
ex; flex-wrap: wrap; align-items: center; justify-content: space-between; }

.navbar-brand { display: inline-block; padding-top: 0.3125rem; padding-bott=
om: 0.3125rem; margin-right: 1rem; font-size: 1.25rem; line-height: inherit=
; white-space: nowrap; }

.navbar-brand:focus, .navbar-brand:hover { text-decoration: none; }

.navbar-nav { display: flex; flex-direction: column; padding-left: 0px; mar=
gin-bottom: 0px; list-style: none; }

.navbar-nav .nav-link { padding-right: 0px; padding-left: 0px; }

.navbar-nav .dropdown-menu { position: static; float: none; }

.navbar-text { display: inline-block; padding-top: 0.5rem; padding-bottom: =
0.5rem; }

.navbar-collapse { flex-basis: 100%; flex-grow: 1; align-items: center; }

.navbar-toggler { padding: 0.25rem 0.75rem; font-size: 1.25rem; line-height=
: 1; background-color: transparent; border: 1px solid transparent; border-r=
adius: 0.25rem; }

.navbar-toggler:focus, .navbar-toggler:hover { text-decoration: none; }

.navbar-toggler-icon { display: inline-block; width: 1.5em; height: 1.5em; =
vertical-align: middle; content: ""; background: 50% center / 100% 100% no-=
repeat; }

.navbar-nav-scroll { max-height: 75vh; overflow-y: auto; }

@media (max-width: 575.98px) {
  .navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid, .na=
vbar-expand-sm > .container-lg, .navbar-expand-sm > .container-md, .navbar-=
expand-sm > .container-sm, .navbar-expand-sm > .container-xl { padding-righ=
t: 0px; padding-left: 0px; }
}

@media (min-width: 576px) {
  .navbar-expand-sm { flex-flow: row; justify-content: flex-start; }
  .navbar-expand-sm .navbar-nav { flex-direction: row; }
  .navbar-expand-sm .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-sm .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid, .na=
vbar-expand-sm > .container-lg, .navbar-expand-sm > .container-md, .navbar-=
expand-sm > .container-sm, .navbar-expand-sm > .container-xl { flex-wrap: n=
owrap; }
  .navbar-expand-sm .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-sm .navbar-collapse { flex-basis: auto; display: flex !imp=
ortant; }
  .navbar-expand-sm .navbar-toggler { display: none; }
}

@media (max-width: 767.98px) {
  .navbar-expand-md > .container, .navbar-expand-md > .container-fluid, .na=
vbar-expand-md > .container-lg, .navbar-expand-md > .container-md, .navbar-=
expand-md > .container-sm, .navbar-expand-md > .container-xl { padding-righ=
t: 0px; padding-left: 0px; }
}

@media (min-width: 768px) {
  .navbar-expand-md { flex-flow: row; justify-content: flex-start; }
  .navbar-expand-md .navbar-nav { flex-direction: row; }
  .navbar-expand-md .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-md .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-md > .container, .navbar-expand-md > .container-fluid, .na=
vbar-expand-md > .container-lg, .navbar-expand-md > .container-md, .navbar-=
expand-md > .container-sm, .navbar-expand-md > .container-xl { flex-wrap: n=
owrap; }
  .navbar-expand-md .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-md .navbar-collapse { flex-basis: auto; display: flex !imp=
ortant; }
  .navbar-expand-md .navbar-toggler { display: none; }
}

@media (max-width: 991.98px) {
  .navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid, .na=
vbar-expand-lg > .container-lg, .navbar-expand-lg > .container-md, .navbar-=
expand-lg > .container-sm, .navbar-expand-lg > .container-xl { padding-righ=
t: 0px; padding-left: 0px; }
}

@media (min-width: 992px) {
  .navbar-expand-lg { flex-flow: row; justify-content: flex-start; }
  .navbar-expand-lg .navbar-nav { flex-direction: row; }
  .navbar-expand-lg .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-lg .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid, .na=
vbar-expand-lg > .container-lg, .navbar-expand-lg > .container-md, .navbar-=
expand-lg > .container-sm, .navbar-expand-lg > .container-xl { flex-wrap: n=
owrap; }
  .navbar-expand-lg .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-lg .navbar-collapse { flex-basis: auto; display: flex !imp=
ortant; }
  .navbar-expand-lg .navbar-toggler { display: none; }
}

@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid, .na=
vbar-expand-xl > .container-lg, .navbar-expand-xl > .container-md, .navbar-=
expand-xl > .container-sm, .navbar-expand-xl > .container-xl { padding-righ=
t: 0px; padding-left: 0px; }
}

@media (min-width: 1200px) {
  .navbar-expand-xl { flex-flow: row; justify-content: flex-start; }
  .navbar-expand-xl .navbar-nav { flex-direction: row; }
  .navbar-expand-xl .navbar-nav .dropdown-menu { position: absolute; }
  .navbar-expand-xl .navbar-nav .nav-link { padding-right: 0.5rem; padding-=
left: 0.5rem; }
  .navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid, .na=
vbar-expand-xl > .container-lg, .navbar-expand-xl > .container-md, .navbar-=
expand-xl > .container-sm, .navbar-expand-xl > .container-xl { flex-wrap: n=
owrap; }
  .navbar-expand-xl .navbar-nav-scroll { overflow: visible; }
  .navbar-expand-xl .navbar-collapse { flex-basis: auto; display: flex !imp=
ortant; }
  .navbar-expand-xl .navbar-toggler { display: none; }
}

.navbar-expand { flex-flow: row; justify-content: flex-start; }

.navbar-expand > .container, .navbar-expand > .container-fluid, .navbar-exp=
and > .container-lg, .navbar-expand > .container-md, .navbar-expand > .cont=
ainer-sm, .navbar-expand > .container-xl { padding-right: 0px; padding-left=
: 0px; }

.navbar-expand .navbar-nav { flex-direction: row; }

.navbar-expand .navbar-nav .dropdown-menu { position: absolute; }

.navbar-expand .navbar-nav .nav-link { padding-right: 0.5rem; padding-left:=
 0.5rem; }

.navbar-expand > .container, .navbar-expand > .container-fluid, .navbar-exp=
and > .container-lg, .navbar-expand > .container-md, .navbar-expand > .cont=
ainer-sm, .navbar-expand > .container-xl { flex-wrap: nowrap; }

.navbar-expand .navbar-nav-scroll { overflow: visible; }

.navbar-expand .navbar-collapse { flex-basis: auto; display: flex !importan=
t; }

.navbar-expand .navbar-toggler { display: none; }

.navbar-light .navbar-brand { color: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover { colo=
r: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-nav .nav-link { color: rgba(0, 0, 0, 0.5); }

.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-l=
ink:hover { color: rgba(0, 0, 0, 0.7); }

.navbar-light .navbar-nav .nav-link.disabled { color: rgba(0, 0, 0, 0.3); }

.navbar-light .navbar-nav .active > .nav-link, .navbar-light .navbar-nav .n=
av-link.active, .navbar-light .navbar-nav .nav-link.show, .navbar-light .na=
vbar-nav .show > .nav-link { color: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-toggler { color: rgba(0, 0, 0, 0.5); border-color: rg=
ba(0, 0, 0, 0.1); }

.navbar-light .navbar-toggler-icon { background-image: url("data:image/svg+=
xml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' width=3D'30' height=3D'30' =
viewBox=3D'0 0 30 30'%3e%3cpath stroke=3D'rgba%280, 0, 0, 0.5%29' stroke-li=
necap=3D'round' stroke-miterlimit=3D'10' stroke-width=3D'2' d=3D'M4 7h22M4 =
15h22M4 23h22'/%3e%3c/svg%3e"); }

.navbar-light .navbar-text { color: rgba(0, 0, 0, 0.5); }

.navbar-light .navbar-text a { color: rgba(0, 0, 0, 0.9); }

.navbar-light .navbar-text a:focus, .navbar-light .navbar-text a:hover { co=
lor: rgba(0, 0, 0, 0.9); }

.navbar-dark .navbar-brand { color: rgb(255, 255, 255); }

.navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover { color:=
 rgb(255, 255, 255); }

.navbar-dark .navbar-nav .nav-link { color: rgba(255, 255, 255, 0.5); }

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-lin=
k:hover { color: rgba(255, 255, 255, 0.75); }

.navbar-dark .navbar-nav .nav-link.disabled { color: rgba(255, 255, 255, 0.=
25); }

.navbar-dark .navbar-nav .active > .nav-link, .navbar-dark .navbar-nav .nav=
-link.active, .navbar-dark .navbar-nav .nav-link.show, .navbar-dark .navbar=
-nav .show > .nav-link { color: rgb(255, 255, 255); }

.navbar-dark .navbar-toggler { color: rgba(255, 255, 255, 0.5); border-colo=
r: rgba(255, 255, 255, 0.1); }

.navbar-dark .navbar-toggler-icon { background-image: url("data:image/svg+x=
ml,%3csvg xmlns=3D'http://www.w3.org/2000/svg' width=3D'30' height=3D'30' v=
iewBox=3D'0 0 30 30'%3e%3cpath stroke=3D'rgba%28255, 255, 255, 0.5%29' stro=
ke-linecap=3D'round' stroke-miterlimit=3D'10' stroke-width=3D'2' d=3D'M4 7h=
22M4 15h22M4 23h22'/%3e%3c/svg%3e"); }

.navbar-dark .navbar-text { color: rgba(255, 255, 255, 0.5); }

.navbar-dark .navbar-text a { color: rgb(255, 255, 255); }

.navbar-dark .navbar-text a:focus, .navbar-dark .navbar-text a:hover { colo=
r: rgb(255, 255, 255); }

.card { position: relative; display: flex; flex-direction: column; min-widt=
h: 0px; overflow-wrap: break-word; background-color: rgb(255, 255, 255); ba=
ckground-clip: border-box; border: 1px solid rgba(0, 0, 0, 0.125); border-r=
adius: 0.25rem; }

.card > hr { margin-right: 0px; margin-left: 0px; }

.card > .list-group { border-top: inherit; border-bottom: inherit; }

.card > .list-group:first-child { border-top-width: 0px; border-top-left-ra=
dius: calc(-1px + 0.25rem); border-top-right-radius: calc(-1px + 0.25rem); =
}

.card > .list-group:last-child { border-bottom-width: 0px; border-bottom-ri=
ght-radius: calc(-1px + 0.25rem); border-bottom-left-radius: calc(-1px + 0.=
25rem); }

.card > .card-header + .list-group, .card > .list-group + .card-footer { bo=
rder-top: 0px; }

.card-body { flex: 1 1 auto; min-height: 1px; padding: 1.25rem; }

.card-title { margin-bottom: 0.75rem; }

.card-subtitle { margin-top: -0.375rem; margin-bottom: 0px; }

.card-text:last-child { margin-bottom: 0px; }

.card-link:hover { text-decoration: none; }

.card-link + .card-link { margin-left: 1.25rem; }

.card-header { padding: 0.75rem 1.25rem; margin-bottom: 0px; background-col=
or: rgba(0, 0, 0, 0.03); border-bottom: 1px solid rgba(0, 0, 0, 0.125); }

.card-header:first-child { border-radius: calc(-1px + 0.25rem) calc(-1px + =
0.25rem) 0px 0px; }

.card-footer { padding: 0.75rem 1.25rem; background-color: rgba(0, 0, 0, 0.=
03); border-top: 1px solid rgba(0, 0, 0, 0.125); }

.card-footer:last-child { border-radius: 0px 0px calc(-1px + 0.25rem) calc(=
-1px + 0.25rem); }

.card-header-tabs { margin-right: -0.625rem; margin-bottom: -0.75rem; margi=
n-left: -0.625rem; border-bottom: 0px; }

.card-header-pills { margin-right: -0.625rem; margin-left: -0.625rem; }

.card-img-overlay { position: absolute; inset: 0px; padding: 1.25rem; borde=
r-radius: calc(-1px + 0.25rem); }

.card-img, .card-img-bottom, .card-img-top { flex-shrink: 0; width: 100%; }

.card-img, .card-img-top { border-top-left-radius: calc(-1px + 0.25rem); bo=
rder-top-right-radius: calc(-1px + 0.25rem); }

.card-img, .card-img-bottom { border-bottom-right-radius: calc(-1px + 0.25r=
em); border-bottom-left-radius: calc(-1px + 0.25rem); }

.card-deck .card { margin-bottom: 15px; }

@media (min-width: 576px) {
  .card-deck { display: flex; flex-flow: wrap; margin-right: -15px; margin-=
left: -15px; }
  .card-deck .card { flex: 1 0 0%; margin-right: 15px; margin-bottom: 0px; =
margin-left: 15px; }
}

.card-group > .card { margin-bottom: 15px; }

@media (min-width: 576px) {
  .card-group { display: flex; flex-flow: wrap; }
  .card-group > .card { flex: 1 0 0%; margin-bottom: 0px; }
  .card-group > .card + .card { margin-left: 0px; border-left: 0px; }
  .card-group > .card:not(:last-child) { border-top-right-radius: 0px; bord=
er-bottom-right-radius: 0px; }
  .card-group > .card:not(:last-child) .card-header, .card-group > .card:no=
t(:last-child) .card-img-top { border-top-right-radius: 0px; }
  .card-group > .card:not(:last-child) .card-footer, .card-group > .card:no=
t(:last-child) .card-img-bottom { border-bottom-right-radius: 0px; }
  .card-group > .card:not(:first-child) { border-top-left-radius: 0px; bord=
er-bottom-left-radius: 0px; }
  .card-group > .card:not(:first-child) .card-header, .card-group > .card:n=
ot(:first-child) .card-img-top { border-top-left-radius: 0px; }
  .card-group > .card:not(:first-child) .card-footer, .card-group > .card:n=
ot(:first-child) .card-img-bottom { border-bottom-left-radius: 0px; }
}

.card-columns .card { margin-bottom: 0.75rem; }

@media (min-width: 576px) {
  .card-columns { column-count: 3; column-gap: 1.25rem; orphans: 1; widows:=
 1; }
  .card-columns .card { display: inline-block; width: 100%; }
}

.accordion { overflow-anchor: none; }

.accordion > .card { overflow: hidden; }

.accordion > .card:not(:last-of-type) { border-bottom: 0px; border-bottom-r=
ight-radius: 0px; border-bottom-left-radius: 0px; }

.accordion > .card:not(:first-of-type) { border-top-left-radius: 0px; borde=
r-top-right-radius: 0px; }

.accordion > .card > .card-header { border-radius: 0px; margin-bottom: -1px=
; }

.breadcrumb { display: flex; flex-wrap: wrap; padding: 0.75rem 1rem; margin=
-bottom: 1rem; list-style: none; background-color: rgb(233, 236, 239); bord=
er-radius: 0.25rem; }

.breadcrumb-item + .breadcrumb-item { padding-left: 0.5rem; }

.breadcrumb-item + .breadcrumb-item::before { float: left; padding-right: 0=
.5rem; color: rgb(108, 117, 125); content: "/"; }

.breadcrumb-item + .breadcrumb-item:hover::before { text-decoration: underl=
ine; }

.breadcrumb-item + .breadcrumb-item:hover::before { text-decoration: none; =
}

.breadcrumb-item.active { color: rgb(108, 117, 125); }

.pagination { display: flex; padding-left: 0px; list-style: none; border-ra=
dius: 0.25rem; }

.page-link { position: relative; display: block; padding: 0.5rem 0.75rem; m=
argin-left: -1px; line-height: 1.25; color: rgb(0, 123, 255); background-co=
lor: rgb(255, 255, 255); border: 1px solid rgb(222, 226, 230); }

.page-link:hover { z-index: 2; color: rgb(0, 86, 179); text-decoration: non=
e; background-color: rgb(233, 236, 239); border-color: rgb(222, 226, 230); =
}

.page-link:focus { z-index: 3; outline: 0px; box-shadow: rgba(0, 123, 255, =
0.25) 0px 0px 0px 0.2rem; }

.page-item:first-child .page-link { margin-left: 0px; border-top-left-radiu=
s: 0.25rem; border-bottom-left-radius: 0.25rem; }

.page-item:last-child .page-link { border-top-right-radius: 0.25rem; border=
-bottom-right-radius: 0.25rem; }

.page-item.active .page-link { z-index: 3; color: rgb(255, 255, 255); backg=
round-color: rgb(0, 123, 255); border-color: rgb(0, 123, 255); }

.page-item.disabled .page-link { color: rgb(108, 117, 125); pointer-events:=
 none; cursor: auto; background-color: rgb(255, 255, 255); border-color: rg=
b(222, 226, 230); }

.pagination-lg .page-link { padding: 0.75rem 1.5rem; font-size: 1.25rem; li=
ne-height: 1.5; }

.pagination-lg .page-item:first-child .page-link { border-top-left-radius: =
0.3rem; border-bottom-left-radius: 0.3rem; }

.pagination-lg .page-item:last-child .page-link { border-top-right-radius: =
0.3rem; border-bottom-right-radius: 0.3rem; }

.pagination-sm .page-link { padding: 0.25rem 0.5rem; font-size: 0.875rem; l=
ine-height: 1.5; }

.pagination-sm .page-item:first-child .page-link { border-top-left-radius: =
0.2rem; border-bottom-left-radius: 0.2rem; }

.pagination-sm .page-item:last-child .page-link { border-top-right-radius: =
0.2rem; border-bottom-right-radius: 0.2rem; }

.badge { display: inline-block; padding: 0.25em 0.4em; font-size: 75%; font=
-weight: 700; line-height: 1; text-align: center; white-space: nowrap; vert=
ical-align: baseline; border-radius: 0.25rem; transition: color 0.15s ease-=
in-out 0s, background-color 0.15s ease-in-out 0s, border-color 0.15s ease-i=
n-out 0s, box-shadow 0.15s ease-in-out 0s; }

@media (prefers-reduced-motion: reduce) {
  .badge { transition: none 0s ease 0s; }
}

a.badge:focus, a.badge:hover { text-decoration: none; }

.badge:empty { display: none; }

.btn .badge { position: relative; top: -1px; }

.badge-pill { padding-right: 0.6em; padding-left: 0.6em; border-radius: 10r=
em; }

.badge-primary { color: rgb(255, 255, 255); background-color: rgb(0, 123, 2=
55); }

a.badge-primary:focus, a.badge-primary:hover { color: rgb(255, 255, 255); b=
ackground-color: rgb(0, 98, 204); }

a.badge-primary.focus, a.badge-primary:focus { outline: 0px; box-shadow: rg=
ba(0, 123, 255, 0.5) 0px 0px 0px 0.2rem; }

.badge-secondary { color: rgb(255, 255, 255); background-color: rgb(108, 11=
7, 125); }

a.badge-secondary:focus, a.badge-secondary:hover { color: rgb(255, 255, 255=
); background-color: rgb(84, 91, 98); }

a.badge-secondary.focus, a.badge-secondary:focus { outline: 0px; box-shadow=
: rgba(108, 117, 125, 0.5) 0px 0px 0px 0.2rem; }

.badge-success { color: rgb(255, 255, 255); background-color: rgb(40, 167, =
69); }

a.badge-success:focus, a.badge-success:hover { color: rgb(255, 255, 255); b=
ackground-color: rgb(30, 126, 52); }

a.badge-success.focus, a.badge-success:focus { outline: 0px; box-shadow: rg=
ba(40, 167, 69, 0.5) 0px 0px 0px 0.2rem; }

.badge-info { color: rgb(255, 255, 255); background-color: rgb(23, 162, 184=
); }

a.badge-info:focus, a.badge-info:hover { color: rgb(255, 255, 255); backgro=
und-color: rgb(17, 122, 139); }

a.badge-info.focus, a.badge-info:focus { outline: 0px; box-shadow: rgba(23,=
 162, 184, 0.5) 0px 0px 0px 0.2rem; }

.badge-warning { color: rgb(33, 37, 41); background-color: rgb(255, 193, 7)=
; }

a.badge-warning:focus, a.badge-warning:hover { color: rgb(33, 37, 41); back=
ground-color: rgb(211, 158, 0); }

a.badge-warning.focus, a.badge-warning:focus { outline: 0px; box-shadow: rg=
ba(255, 193, 7, 0.5) 0px 0px 0px 0.2rem; }

.badge-danger { color: rgb(255, 255, 255); background-color: rgb(220, 53, 6=
9); }

a.badge-danger:focus, a.badge-danger:hover { color: rgb(255, 255, 255); bac=
kground-color: rgb(189, 33, 48); }

a.badge-danger.focus, a.badge-danger:focus { outline: 0px; box-shadow: rgba=
(220, 53, 69, 0.5) 0px 0px 0px 0.2rem; }

.badge-light { color: rgb(33, 37, 41); background-color: rgb(248, 249, 250)=
; }

a.badge-light:focus, a.badge-light:hover { color: rgb(33, 37, 41); backgrou=
nd-color: rgb(218, 224, 229); }

a.badge-light.focus, a.badge-light:focus { outline: 0px; box-shadow: rgba(2=
48, 249, 250, 0.5) 0px 0px 0px 0.2rem; }

.badge-dark { color: rgb(255, 255, 255); background-color: rgb(52, 58, 64);=
 }

a.badge-dark:focus, a.badge-dark:hover { color: rgb(255, 255, 255); backgro=
und-color: rgb(29, 33, 36); }

a.badge-dark.focus, a.badge-dark:focus { outline: 0px; box-shadow: rgba(52,=
 58, 64, 0.5) 0px 0px 0px 0.2rem; }

.jumbotron { padding: 2rem 1rem; margin-bottom: 2rem; background-color: rgb=
(233, 236, 239); border-radius: 0.3rem; }

@media (min-width: 576px) {
  .jumbotron { padding: 4rem 2rem; }
}

.jumbotron-fluid { padding-right: 0px; padding-left: 0px; border-radius: 0p=
x; }

.alert { position: relative; padding: 0.75rem 1.25rem; margin-bottom: 1rem;=
 border: 1px solid transparent; border-radius: 0.25rem; }

.alert-heading { color: inherit; }

.alert-link { font-weight: 700; }

.alert-dismissible { padding-right: 4rem; }

.alert-dismissible .close { position: absolute; top: 0px; right: 0px; z-ind=
ex: 2; padding: 0.75rem 1.25rem; color: inherit; }

.alert-primary { color: rgb(0, 64, 133); background-color: rgb(204, 229, 25=
5); border-color: rgb(184, 218, 255); }

.alert-primary hr { border-top-color: rgb(159, 205, 255); }

.alert-primary .alert-link { color: rgb(0, 39, 82); }

.alert-secondary { color: rgb(56, 61, 65); background-color: rgb(226, 227, =
229); border-color: rgb(214, 216, 219); }

.alert-secondary hr { border-top-color: rgb(200, 203, 207); }

.alert-secondary .alert-link { color: rgb(32, 35, 38); }

.alert-success { color: rgb(21, 87, 36); background-color: rgb(212, 237, 21=
8); border-color: rgb(195, 230, 203); }

.alert-success hr { border-top-color: rgb(177, 223, 187); }

.alert-success .alert-link { color: rgb(11, 46, 19); }

.alert-info { color: rgb(12, 84, 96); background-color: rgb(209, 236, 241);=
 border-color: rgb(190, 229, 235); }

.alert-info hr { border-top-color: rgb(171, 221, 229); }

.alert-info .alert-link { color: rgb(6, 44, 51); }

.alert-warning { color: rgb(133, 100, 4); background-color: rgb(255, 243, 2=
05); border-color: rgb(255, 238, 186); }

.alert-warning hr { border-top-color: rgb(255, 232, 161); }

.alert-warning .alert-link { color: rgb(83, 63, 3); }

.alert-danger { color: rgb(114, 28, 36); background-color: rgb(248, 215, 21=
8); border-color: rgb(245, 198, 203); }

.alert-danger hr { border-top-color: rgb(241, 176, 183); }

.alert-danger .alert-link { color: rgb(73, 18, 23); }

.alert-light { color: rgb(129, 129, 130); background-color: rgb(254, 254, 2=
54); border-color: rgb(253, 253, 254); }

.alert-light hr { border-top-color: rgb(236, 236, 246); }

.alert-light .alert-link { color: rgb(104, 104, 104); }

.alert-dark { color: rgb(27, 30, 33); background-color: rgb(214, 216, 217);=
 border-color: rgb(198, 200, 202); }

.alert-dark hr { border-top-color: rgb(185, 187, 190); }

.alert-dark .alert-link { color: rgb(4, 5, 5); }

@-webkit-keyframes progress-bar-stripes {=20
  0% { background-position: 1rem 0px; }
  100% { background-position: 0px 0px; }
}

@keyframes progress-bar-stripes {=20
  0% { background-position: 1rem 0px; }
  100% { background-position: 0px 0px; }
}

.progress { display: flex; height: 1rem; overflow: hidden; line-height: 0; =
font-size: 0.75rem; background-color: rgb(233, 236, 239); border-radius: 0.=
25rem; }

.progress-bar { display: flex; flex-direction: column; justify-content: cen=
ter; overflow: hidden; color: rgb(255, 255, 255); text-align: center; white=
-space: nowrap; background-color: rgb(0, 123, 255); transition: width 0.6s =
ease 0s; }

@media (prefers-reduced-motion: reduce) {
  .progress-bar { transition: none 0s ease 0s; }
}

.progress-bar-striped { background-image: linear-gradient(45deg, rgba(255, =
255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, =
0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); ba=
ckground-size: 1rem 1rem; }

.progress-bar-animated { animation: 1s linear 0s infinite normal none runni=
ng progress-bar-stripes; }

@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated { animation: auto ease 0s 1 normal none running no=
ne; }
}

.media { display: flex; align-items: flex-start; }

.media-body { flex: 1 1 0%; }

.list-group { display: flex; flex-direction: column; padding-left: 0px; mar=
gin-bottom: 0px; border-radius: 0.25rem; }

.list-group-item-action { width: 100%; color: rgb(73, 80, 87); text-align: =
inherit; }

.list-group-item-action:focus, .list-group-item-action:hover { z-index: 1; =
color: rgb(73, 80, 87); text-decoration: none; background-color: rgb(248, 2=
49, 250); }

.list-group-item-action:active { color: rgb(33, 37, 41); background-color: =
rgb(233, 236, 239); }

.list-group-item { position: relative; display: block; padding: 0.75rem 1.2=
5rem; background-color: rgb(255, 255, 255); border: 1px solid rgba(0, 0, 0,=
 0.125); }

.list-group-item:first-child { border-top-left-radius: inherit; border-top-=
right-radius: inherit; }

.list-group-item:last-child { border-bottom-right-radius: inherit; border-b=
ottom-left-radius: inherit; }

.list-group-item.disabled, .list-group-item:disabled { color: rgb(108, 117,=
 125); pointer-events: none; background-color: rgb(255, 255, 255); }

.list-group-item.active { z-index: 2; color: rgb(255, 255, 255); background=
-color: rgb(0, 123, 255); border-color: rgb(0, 123, 255); }

.list-group-item + .list-group-item { border-top-width: 0px; }

.list-group-item + .list-group-item.active { margin-top: -1px; border-top-w=
idth: 1px; }

.list-group-horizontal { flex-direction: row; }

.list-group-horizontal > .list-group-item:first-child { border-bottom-left-=
radius: 0.25rem; border-top-right-radius: 0px; }

.list-group-horizontal > .list-group-item:last-child { border-top-right-rad=
ius: 0.25rem; border-bottom-left-radius: 0px; }

.list-group-horizontal > .list-group-item.active { margin-top: 0px; }

.list-group-horizontal > .list-group-item + .list-group-item { border-top-w=
idth: 1px; border-left-width: 0px; }

.list-group-horizontal > .list-group-item + .list-group-item.active { margi=
n-left: -1px; border-left-width: 1px; }

@media (min-width: 576px) {
  .list-group-horizontal-sm { flex-direction: row; }
  .list-group-horizontal-sm > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-sm > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-sm > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-sm > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 768px) {
  .list-group-horizontal-md { flex-direction: row; }
  .list-group-horizontal-md > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-md > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-md > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-md > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 992px) {
  .list-group-horizontal-lg { flex-direction: row; }
  .list-group-horizontal-lg > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-lg > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-lg > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-lg > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

@media (min-width: 1200px) {
  .list-group-horizontal-xl { flex-direction: row; }
  .list-group-horizontal-xl > .list-group-item:first-child { border-bottom-=
left-radius: 0.25rem; border-top-right-radius: 0px; }
  .list-group-horizontal-xl > .list-group-item:last-child { border-top-righ=
t-radius: 0.25rem; border-bottom-left-radius: 0px; }
  .list-group-horizontal-xl > .list-group-item.active { margin-top: 0px; }
  .list-group-horizontal-xl > .list-group-item + .list-group-item { border-=
top-width: 1px; border-left-width: 0px; }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active { =
margin-left: -1px; border-left-width: 1px; }
}

.list-group-flush { border-radius: 0px; }

.list-group-flush > .list-group-item { border-width: 0px 0px 1px; }

.list-group-flush > .list-group-item:last-child { border-bottom-width: 0px;=
 }

.list-group-item-primary { color: rgb(0, 64, 133); background-color: rgb(18=
4, 218, 255); }

.list-group-item-primary.list-group-item-action:focus, .list-group-item-pri=
mary.list-group-item-action:hover { color: rgb(0, 64, 133); background-colo=
r: rgb(159, 205, 255); }

.list-group-item-primary.list-group-item-action.active { color: rgb(255, 25=
5, 255); background-color: rgb(0, 64, 133); border-color: rgb(0, 64, 133); =
}

.list-group-item-secondary { color: rgb(56, 61, 65); background-color: rgb(=
214, 216, 219); }

.list-group-item-secondary.list-group-item-action:focus, .list-group-item-s=
econdary.list-group-item-action:hover { color: rgb(56, 61, 65); background-=
color: rgb(200, 203, 207); }

.list-group-item-secondary.list-group-item-action.active { color: rgb(255, =
255, 255); background-color: rgb(56, 61, 65); border-color: rgb(56, 61, 65)=
; }

.list-group-item-success { color: rgb(21, 87, 36); background-color: rgb(19=
5, 230, 203); }

.list-group-item-success.list-group-item-action:focus, .list-group-item-suc=
cess.list-group-item-action:hover { color: rgb(21, 87, 36); background-colo=
r: rgb(177, 223, 187); }

.list-group-item-success.list-group-item-action.active { color: rgb(255, 25=
5, 255); background-color: rgb(21, 87, 36); border-color: rgb(21, 87, 36); =
}

.list-group-item-info { color: rgb(12, 84, 96); background-color: rgb(190, =
229, 235); }

.list-group-item-info.list-group-item-action:focus, .list-group-item-info.l=
ist-group-item-action:hover { color: rgb(12, 84, 96); background-color: rgb=
(171, 221, 229); }

.list-group-item-info.list-group-item-action.active { color: rgb(255, 255, =
255); background-color: rgb(12, 84, 96); border-color: rgb(12, 84, 96); }

.list-group-item-warning { color: rgb(133, 100, 4); background-color: rgb(2=
55, 238, 186); }

.list-group-item-warning.list-group-item-action:focus, .list-group-item-war=
ning.list-group-item-action:hover { color: rgb(133, 100, 4); background-col=
or: rgb(255, 232, 161); }

.list-group-item-warning.list-group-item-action.active { color: rgb(255, 25=
5, 255); background-color: rgb(133, 100, 4); border-color: rgb(133, 100, 4)=
; }

.list-group-item-danger { color: rgb(114, 28, 36); background-color: rgb(24=
5, 198, 203); }

.list-group-item-danger.list-group-item-action:focus, .list-group-item-dang=
er.list-group-item-action:hover { color: rgb(114, 28, 36); background-color=
: rgb(241, 176, 183); }

.list-group-item-danger.list-group-item-action.active { color: rgb(255, 255=
, 255); background-color: rgb(114, 28, 36); border-color: rgb(114, 28, 36);=
 }

.list-group-item-light { color: rgb(129, 129, 130); background-color: rgb(2=
53, 253, 254); }

.list-group-item-light.list-group-item-action:focus, .list-group-item-light=
.list-group-item-action:hover { color: rgb(129, 129, 130); background-color=
: rgb(236, 236, 246); }

.list-group-item-light.list-group-item-action.active { color: rgb(255, 255,=
 255); background-color: rgb(129, 129, 130); border-color: rgb(129, 129, 13=
0); }

.list-group-item-dark { color: rgb(27, 30, 33); background-color: rgb(198, =
200, 202); }

.list-group-item-dark.list-group-item-action:focus, .list-group-item-dark.l=
ist-group-item-action:hover { color: rgb(27, 30, 33); background-color: rgb=
(185, 187, 190); }

.list-group-item-dark.list-group-item-action.active { color: rgb(255, 255, =
255); background-color: rgb(27, 30, 33); border-color: rgb(27, 30, 33); }

.close { float: right; font-size: 1.5rem; font-weight: 700; line-height: 1;=
 color: rgb(0, 0, 0); text-shadow: rgb(255, 255, 255) 0px 1px 0px; opacity:=
 0.5; }

.close:hover { color: rgb(0, 0, 0); text-decoration: none; }

.close:not(:disabled):not(.disabled):focus, .close:not(:disabled):not(.disa=
bled):hover { opacity: 0.75; }

button.close { padding: 0px; background-color: transparent; border: 0px; }

a.close.disabled { pointer-events: none; }

.toast { flex-basis: 350px; max-width: 350px; font-size: 0.875rem; backgrou=
nd-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border: =
1px solid rgba(0, 0, 0, 0.1); box-shadow: rgba(0, 0, 0, 0.1) 0px 0.25rem 0.=
75rem; opacity: 0; border-radius: 0.25rem; }

.toast:not(:last-child) { margin-bottom: 0.75rem; }

.toast.showing { opacity: 1; }

.toast.show { display: block; opacity: 1; }

.toast.hide { display: none; }

.toast-header { display: flex; align-items: center; padding: 0.25rem 0.75re=
m; color: rgb(108, 117, 125); background-color: rgba(255, 255, 255, 0.85); =
background-clip: padding-box; border-bottom: 1px solid rgba(0, 0, 0, 0.05);=
 border-top-left-radius: calc(-1px + 0.25rem); border-top-right-radius: cal=
c(-1px + 0.25rem); }

.toast-body { padding: 0.75rem; }

.modal-open { overflow: hidden; }

.modal-open .modal { overflow: hidden auto; }

.modal { position: fixed; top: 0px; left: 0px; z-index: 1050; display: none=
; width: 100%; height: 100%; overflow: hidden; outline: 0px; }

.modal-dialog { position: relative; width: auto; margin: 0.5rem; pointer-ev=
ents: none; }

.modal.fade .modal-dialog { transition: transform 0.3s ease-out 0s, -webkit=
-transform 0.3s ease-out 0s; transform: translate(0px, -50px); }

@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog { transition: none 0s ease 0s; }
}

.modal.show .modal-dialog { transform: none; }

.modal.modal-static .modal-dialog { transform: scale(1.02); }

.modal-dialog-scrollable { display: flex; max-height: calc(100% - 1rem); }

.modal-dialog-scrollable .modal-content { max-height: calc(-1rem + 100vh); =
overflow: hidden; }

.modal-dialog-scrollable .modal-footer, .modal-dialog-scrollable .modal-hea=
der { flex-shrink: 0; }

.modal-dialog-scrollable .modal-body { overflow-y: auto; }

.modal-dialog-centered { display: flex; align-items: center; min-height: ca=
lc(100% - 1rem); }

.modal-dialog-centered::before { display: block; height: min-content; conte=
nt: ""; }

.modal-dialog-centered.modal-dialog-scrollable { flex-direction: column; ju=
stify-content: center; height: 100%; }

.modal-dialog-centered.modal-dialog-scrollable .modal-content { max-height:=
 none; }

.modal-dialog-centered.modal-dialog-scrollable::before { content: none; }

.modal-content { position: relative; display: flex; flex-direction: column;=
 width: 100%; pointer-events: auto; background-color: rgb(255, 255, 255); b=
ackground-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-r=
adius: 0.3rem; outline: 0px; }

.modal-backdrop { position: fixed; top: 0px; left: 0px; z-index: 1040; widt=
h: 100vw; height: 100vh; background-color: rgb(0, 0, 0); }

.modal-backdrop.fade { opacity: 0; }

.modal-backdrop.show { opacity: 0.5; }

.modal-header { display: flex; align-items: flex-start; justify-content: sp=
ace-between; padding: 1rem; border-bottom: 1px solid rgb(222, 226, 230); bo=
rder-top-left-radius: calc(-1px + 0.3rem); border-top-right-radius: calc(-1=
px + 0.3rem); }

.modal-header .close { padding: 1rem; margin: -1rem -1rem -1rem auto; }

.modal-title { margin-bottom: 0px; line-height: 1.5; }

.modal-body { position: relative; flex: 1 1 auto; padding: 1rem; }

.modal-footer { display: flex; flex-wrap: wrap; align-items: center; justif=
y-content: flex-end; padding: 0.75rem; border-top: 1px solid rgb(222, 226, =
230); border-bottom-right-radius: calc(-1px + 0.3rem); border-bottom-left-r=
adius: calc(-1px + 0.3rem); }

.modal-footer > * { margin: 0.25rem; }

.modal-scrollbar-measure { position: absolute; top: -9999px; width: 50px; h=
eight: 50px; overflow: scroll; }

@media (min-width: 576px) {
  .modal-dialog { max-width: 500px; margin: 1.75rem auto; }
  .modal-dialog-scrollable { max-height: calc(100% - 3.5rem); }
  .modal-dialog-scrollable .modal-content { max-height: calc(-3.5rem + 100v=
h); }
  .modal-dialog-centered { min-height: calc(100% - 3.5rem); }
  .modal-dialog-centered::before { height: min-content; }
  .modal-sm { max-width: 300px; }
}

@media (min-width: 992px) {
  .modal-lg, .modal-xl { max-width: 800px; }
}

@media (min-width: 1200px) {
  .modal-xl { max-width: 1140px; }
}

.tooltip { position: absolute; z-index: 1070; display: block; margin: 0px; =
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvet=
ica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color =
Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-style=
: normal; font-weight: 400; line-height: 1.5; text-align: start; text-decor=
ation: none; text-shadow: none; text-transform: none; letter-spacing: norma=
l; word-break: normal; word-spacing: normal; white-space: normal; line-brea=
k: auto; font-size: 0.875rem; overflow-wrap: break-word; opacity: 0; }

.tooltip.show { opacity: 0.9; }

.tooltip .arrow { position: absolute; display: block; width: 0.8rem; height=
: 0.4rem; }

.tooltip .arrow::before { position: absolute; content: ""; border-color: tr=
ansparent; border-style: solid; }

.bs-tooltip-auto[x-placement^=3D"top"], .bs-tooltip-top { padding: 0.4rem 0=
px; }

.bs-tooltip-auto[x-placement^=3D"top"] .arrow, .bs-tooltip-top .arrow { bot=
tom: 0px; }

.bs-tooltip-auto[x-placement^=3D"top"] .arrow::before, .bs-tooltip-top .arr=
ow::before { top: 0px; border-width: 0.4rem 0.4rem 0px; border-top-color: r=
gb(0, 0, 0); }

.bs-tooltip-auto[x-placement^=3D"right"], .bs-tooltip-right { padding: 0px =
0.4rem; }

.bs-tooltip-auto[x-placement^=3D"right"] .arrow, .bs-tooltip-right .arrow {=
 left: 0px; width: 0.4rem; height: 0.8rem; }

.bs-tooltip-auto[x-placement^=3D"right"] .arrow::before, .bs-tooltip-right =
.arrow::before { right: 0px; border-width: 0.4rem 0.4rem 0.4rem 0px; border=
-right-color: rgb(0, 0, 0); }

.bs-tooltip-auto[x-placement^=3D"bottom"], .bs-tooltip-bottom { padding: 0.=
4rem 0px; }

.bs-tooltip-auto[x-placement^=3D"bottom"] .arrow, .bs-tooltip-bottom .arrow=
 { top: 0px; }

.bs-tooltip-auto[x-placement^=3D"bottom"] .arrow::before, .bs-tooltip-botto=
m .arrow::before { bottom: 0px; border-width: 0px 0.4rem 0.4rem; border-bot=
tom-color: rgb(0, 0, 0); }

.bs-tooltip-auto[x-placement^=3D"left"], .bs-tooltip-left { padding: 0px 0.=
4rem; }

.bs-tooltip-auto[x-placement^=3D"left"] .arrow, .bs-tooltip-left .arrow { r=
ight: 0px; width: 0.4rem; height: 0.8rem; }

.bs-tooltip-auto[x-placement^=3D"left"] .arrow::before, .bs-tooltip-left .a=
rrow::before { left: 0px; border-width: 0.4rem 0px 0.4rem 0.4rem; border-le=
ft-color: rgb(0, 0, 0); }

.tooltip-inner { max-width: 200px; padding: 0.25rem 0.5rem; color: rgb(255,=
 255, 255); text-align: center; background-color: rgb(0, 0, 0); border-radi=
us: 0.25rem; }

.popover { position: absolute; top: 0px; left: 0px; z-index: 1060; display:=
 block; max-width: 276px; font-family: -apple-system, BlinkMacSystemFont, "=
Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans",=
 sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Not=
o Color Emoji"; font-style: normal; font-weight: 400; line-height: 1.5; tex=
t-align: start; text-decoration: none; text-shadow: none; text-transform: n=
one; letter-spacing: normal; word-break: normal; word-spacing: normal; whit=
e-space: normal; line-break: auto; font-size: 0.875rem; overflow-wrap: brea=
k-word; background-color: rgb(255, 255, 255); background-clip: padding-box;=
 border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem; }

.popover .arrow { position: absolute; display: block; width: 1rem; height: =
0.5rem; margin: 0px 0.3rem; }

.popover .arrow::after, .popover .arrow::before { position: absolute; displ=
ay: block; content: ""; border-color: transparent; border-style: solid; }

.bs-popover-auto[x-placement^=3D"top"], .bs-popover-top { margin-bottom: 0.=
5rem; }

.bs-popover-auto[x-placement^=3D"top"] > .arrow, .bs-popover-top > .arrow {=
 bottom: calc(-1px - 0.5rem); }

.bs-popover-auto[x-placement^=3D"top"] > .arrow::before, .bs-popover-top > =
.arrow::before { bottom: 0px; border-width: 0.5rem 0.5rem 0px; border-top-c=
olor: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[x-placement^=3D"top"] > .arrow::after, .bs-popover-top > .=
arrow::after { bottom: 1px; border-width: 0.5rem 0.5rem 0px; border-top-col=
or: rgb(255, 255, 255); }

.bs-popover-auto[x-placement^=3D"right"], .bs-popover-right { margin-left: =
0.5rem; }

.bs-popover-auto[x-placement^=3D"right"] > .arrow, .bs-popover-right > .arr=
ow { left: calc(-1px - 0.5rem); width: 0.5rem; height: 1rem; margin: 0.3rem=
 0px; }

.bs-popover-auto[x-placement^=3D"right"] > .arrow::before, .bs-popover-righ=
t > .arrow::before { left: 0px; border-width: 0.5rem 0.5rem 0.5rem 0px; bor=
der-right-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[x-placement^=3D"right"] > .arrow::after, .bs-popover-right=
 > .arrow::after { left: 1px; border-width: 0.5rem 0.5rem 0.5rem 0px; borde=
r-right-color: rgb(255, 255, 255); }

.bs-popover-auto[x-placement^=3D"bottom"], .bs-popover-bottom { margin-top:=
 0.5rem; }

.bs-popover-auto[x-placement^=3D"bottom"] > .arrow, .bs-popover-bottom > .a=
rrow { top: calc(-1px - 0.5rem); }

.bs-popover-auto[x-placement^=3D"bottom"] > .arrow::before, .bs-popover-bot=
tom > .arrow::before { top: 0px; border-width: 0px 0.5rem 0.5rem; border-bo=
ttom-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[x-placement^=3D"bottom"] > .arrow::after, .bs-popover-bott=
om > .arrow::after { top: 1px; border-width: 0px 0.5rem 0.5rem; border-bott=
om-color: rgb(255, 255, 255); }

.bs-popover-auto[x-placement^=3D"bottom"] .popover-header::before, .bs-popo=
ver-bottom .popover-header::before { position: absolute; top: 0px; left: 50=
%; display: block; width: 1rem; margin-left: -0.5rem; content: ""; border-b=
ottom: 1px solid rgb(247, 247, 247); }

.bs-popover-auto[x-placement^=3D"left"], .bs-popover-left { margin-right: 0=
.5rem; }

.bs-popover-auto[x-placement^=3D"left"] > .arrow, .bs-popover-left > .arrow=
 { right: calc(-1px - 0.5rem); width: 0.5rem; height: 1rem; margin: 0.3rem =
0px; }

.bs-popover-auto[x-placement^=3D"left"] > .arrow::before, .bs-popover-left =
> .arrow::before { right: 0px; border-width: 0.5rem 0px 0.5rem 0.5rem; bord=
er-left-color: rgba(0, 0, 0, 0.25); }

.bs-popover-auto[x-placement^=3D"left"] > .arrow::after, .bs-popover-left >=
 .arrow::after { right: 1px; border-width: 0.5rem 0px 0.5rem 0.5rem; border=
-left-color: rgb(255, 255, 255); }

.popover-header { padding: 0.5rem 0.75rem; margin-bottom: 0px; font-size: 1=
rem; background-color: rgb(247, 247, 247); border-bottom: 1px solid rgb(235=
, 235, 235); border-top-left-radius: calc(-1px + 0.3rem); border-top-right-=
radius: calc(-1px + 0.3rem); }

.popover-header:empty { display: none; }

.popover-body { padding: 0.5rem 0.75rem; color: rgb(33, 37, 41); }

.carousel { position: relative; }

.carousel.pointer-event { touch-action: pan-y; }

.carousel-inner { position: relative; width: 100%; overflow: hidden; }

.carousel-inner::after { display: block; clear: both; content: ""; }

.carousel-item { position: relative; display: none; float: left; width: 100=
%; margin-right: -100%; backface-visibility: hidden; transition: transform =
0.6s ease-in-out 0s, -webkit-transform 0.6s ease-in-out 0s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-item { transition: none 0s ease 0s; }
}

.carousel-item-next, .carousel-item-prev, .carousel-item.active { display: =
block; }

.active.carousel-item-right, .carousel-item-next:not(.carousel-item-left) {=
 transform: translateX(100%); }

.active.carousel-item-left, .carousel-item-prev:not(.carousel-item-right) {=
 transform: translateX(-100%); }

.carousel-fade .carousel-item { opacity: 0; transition-property: opacity; t=
ransform: none; }

.carousel-fade .carousel-item-next.carousel-item-left, .carousel-fade .caro=
usel-item-prev.carousel-item-right, .carousel-fade .carousel-item.active { =
z-index: 1; opacity: 1; }

.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-=
item-right { z-index: 0; opacity: 0; transition: opacity 0s ease 0.6s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left, .carousel-fade .active.carouse=
l-item-right { transition: none 0s ease 0s; }
}

.carousel-control-next, .carousel-control-prev { position: absolute; top: 0=
px; bottom: 0px; z-index: 1; display: flex; align-items: center; justify-co=
ntent: center; width: 15%; padding: 0px; color: rgb(255, 255, 255); text-al=
ign: center; background: 0px 0px; border: 0px; opacity: 0.5; transition: op=
acity 0.15s ease 0s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-control-next, .carousel-control-prev { transition: none 0s ease=
 0s; }
}

.carousel-control-next:focus, .carousel-control-next:hover, .carousel-contr=
ol-prev:focus, .carousel-control-prev:hover { color: rgb(255, 255, 255); te=
xt-decoration: none; outline: 0px; opacity: 0.9; }

.carousel-control-prev { left: 0px; }

.carousel-control-next { right: 0px; }

.carousel-control-next-icon, .carousel-control-prev-icon { display: inline-=
block; width: 20px; height: 20px; background: 50% center / 100% 100% no-rep=
eat; }

.carousel-control-prev-icon { background-image: url("data:image/svg+xml,%3c=
svg xmlns=3D'http://www.w3.org/2000/svg' fill=3D'%23fff' width=3D'8' height=
=3D'8' viewBox=3D'0 0 8 8'%3e%3cpath d=3D'M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2=
.5-2.5L5.25 0z'/%3e%3c/svg%3e"); }

.carousel-control-next-icon { background-image: url("data:image/svg+xml,%3c=
svg xmlns=3D'http://www.w3.org/2000/svg' fill=3D'%23fff' width=3D'8' height=
=3D'8' viewBox=3D'0 0 8 8'%3e%3cpath d=3D'M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L=
2.75 8l4-4-4-4z'/%3e%3c/svg%3e"); }

.carousel-indicators { position: absolute; right: 0px; bottom: 0px; left: 0=
px; z-index: 15; display: flex; justify-content: center; padding-left: 0px;=
 margin-right: 15%; margin-left: 15%; list-style: none; }

.carousel-indicators li { box-sizing: content-box; flex: 0 1 auto; width: 3=
0px; height: 3px; margin-right: 3px; margin-left: 3px; text-indent: -999px;=
 cursor: pointer; background-color: rgb(255, 255, 255); background-clip: pa=
dding-box; border-top: 10px solid transparent; border-bottom: 10px solid tr=
ansparent; opacity: 0.5; transition: opacity 0.6s ease 0s; }

@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li { transition: none 0s ease 0s; }
}

.carousel-indicators .active { opacity: 1; }

.carousel-caption { position: absolute; right: 15%; bottom: 20px; left: 15%=
; z-index: 10; padding-top: 20px; padding-bottom: 20px; color: rgb(255, 255=
, 255); text-align: center; }

@-webkit-keyframes spinner-border {=20
  100% { transform: rotate(360deg); }
}

@keyframes spinner-border {=20
  100% { transform: rotate(360deg); }
}

.spinner-border { display: inline-block; width: 2rem; height: 2rem; vertica=
l-align: -0.125em; border-width: 0.25em; border-style: solid; border-color:=
 currentcolor transparent currentcolor currentcolor; border-image: initial;=
 border-radius: 50%; animation: 0.75s linear 0s infinite normal none runnin=
g spinner-border; }

.spinner-border-sm { width: 1rem; height: 1rem; border-width: 0.2em; }

@-webkit-keyframes spinner-grow {=20
  0% { transform: scale(0); }
  50% { opacity: 1; transform: none; }
}

@keyframes spinner-grow {=20
  0% { transform: scale(0); }
  50% { opacity: 1; transform: none; }
}

.spinner-grow { display: inline-block; width: 2rem; height: 2rem; vertical-=
align: -0.125em; background-color: currentcolor; border-radius: 50%; opacit=
y: 0; animation: 0.75s linear 0s infinite normal none running spinner-grow;=
 }

.spinner-grow-sm { width: 1rem; height: 1rem; }

@media (prefers-reduced-motion: reduce) {
  .spinner-border, .spinner-grow { animation-duration: 1.5s; }
}

.align-baseline { vertical-align: baseline !important; }

.align-top { vertical-align: top !important; }

.align-middle { vertical-align: middle !important; }

.align-bottom { vertical-align: bottom !important; }

.align-text-bottom { vertical-align: text-bottom !important; }

.align-text-top { vertical-align: text-top !important; }

.bg-primary { background-color: rgb(0, 123, 255) !important; }

a.bg-primary:focus, a.bg-primary:hover, button.bg-primary:focus, button.bg-=
primary:hover { background-color: rgb(0, 98, 204) !important; }

.bg-secondary { background-color: rgb(108, 117, 125) !important; }

a.bg-secondary:focus, a.bg-secondary:hover, button.bg-secondary:focus, butt=
on.bg-secondary:hover { background-color: rgb(84, 91, 98) !important; }

.bg-success { background-color: rgb(40, 167, 69) !important; }

a.bg-success:focus, a.bg-success:hover, button.bg-success:focus, button.bg-=
success:hover { background-color: rgb(30, 126, 52) !important; }

.bg-info { background-color: rgb(23, 162, 184) !important; }

a.bg-info:focus, a.bg-info:hover, button.bg-info:focus, button.bg-info:hove=
r { background-color: rgb(17, 122, 139) !important; }

.bg-warning { background-color: rgb(255, 193, 7) !important; }

a.bg-warning:focus, a.bg-warning:hover, button.bg-warning:focus, button.bg-=
warning:hover { background-color: rgb(211, 158, 0) !important; }

.bg-danger { background-color: rgb(220, 53, 69) !important; }

a.bg-danger:focus, a.bg-danger:hover, button.bg-danger:focus, button.bg-dan=
ger:hover { background-color: rgb(189, 33, 48) !important; }

.bg-light { background-color: rgb(248, 249, 250) !important; }

a.bg-light:focus, a.bg-light:hover, button.bg-light:focus, button.bg-light:=
hover { background-color: rgb(218, 224, 229) !important; }

.bg-dark { background-color: rgb(52, 58, 64) !important; }

a.bg-dark:focus, a.bg-dark:hover, button.bg-dark:focus, button.bg-dark:hove=
r { background-color: rgb(29, 33, 36) !important; }

.bg-white { background-color: rgb(255, 255, 255) !important; }

.bg-transparent { background-color: transparent !important; }

.border { border: 1px solid rgb(222, 226, 230) !important; }

.border-top { border-top: 1px solid rgb(222, 226, 230) !important; }

.border-right { border-right: 1px solid rgb(222, 226, 230) !important; }

.border-bottom { border-bottom: 1px solid rgb(222, 226, 230) !important; }

.border-left { border-left: 1px solid rgb(222, 226, 230) !important; }

.border-0 { border: 0px !important; }

.border-top-0 { border-top: 0px !important; }

.border-right-0 { border-right: 0px !important; }

.border-bottom-0 { border-bottom: 0px !important; }

.border-left-0 { border-left: 0px !important; }

.border-primary { border-color: rgb(0, 123, 255) !important; }

.border-secondary { border-color: rgb(108, 117, 125) !important; }

.border-success { border-color: rgb(40, 167, 69) !important; }

.border-info { border-color: rgb(23, 162, 184) !important; }

.border-warning { border-color: rgb(255, 193, 7) !important; }

.border-danger { border-color: rgb(220, 53, 69) !important; }

.border-light { border-color: rgb(248, 249, 250) !important; }

.border-dark { border-color: rgb(52, 58, 64) !important; }

.border-white { border-color: rgb(255, 255, 255) !important; }

.rounded-sm { border-radius: 0.2rem !important; }

.rounded { border-radius: 0.25rem !important; }

.rounded-top { border-top-left-radius: 0.25rem !important; border-top-right=
-radius: 0.25rem !important; }

.rounded-right { border-top-right-radius: 0.25rem !important; border-bottom=
-right-radius: 0.25rem !important; }

.rounded-bottom { border-bottom-right-radius: 0.25rem !important; border-bo=
ttom-left-radius: 0.25rem !important; }

.rounded-left { border-top-left-radius: 0.25rem !important; border-bottom-l=
eft-radius: 0.25rem !important; }

.rounded-lg { border-radius: 0.3rem !important; }

.rounded-circle { border-radius: 50% !important; }

.rounded-pill { border-radius: 50rem !important; }

.rounded-0 { border-radius: 0px !important; }

.clearfix::after { display: block; clear: both; content: ""; }

.d-none { display: none !important; }

.d-inline { display: inline !important; }

.d-inline-block { display: inline-block !important; }

.d-block { display: block !important; }

.d-table { display: table !important; }

.d-table-row { display: table-row !important; }

.d-table-cell { display: table-cell !important; }

.d-flex { display: flex !important; }

.d-inline-flex { display: inline-flex !important; }

@media (min-width: 576px) {
  .d-sm-none { display: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-table { display: table !important; }
  .d-sm-table-row { display: table-row !important; }
  .d-sm-table-cell { display: table-cell !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-table { display: table !important; }
  .d-md-table-row { display: table-row !important; }
  .d-md-table-cell { display: table-cell !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline-flex { display: inline-flex !important; }
}

@media (min-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-table { display: table !important; }
  .d-lg-table-row { display: table-row !important; }
  .d-lg-table-cell { display: table-cell !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
}

@media (min-width: 1200px) {
  .d-xl-none { display: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-table { display: table !important; }
  .d-xl-table-row { display: table-row !important; }
  .d-xl-table-cell { display: table-cell !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
}

@media print {
  .d-print-none { display: none !important; }
  .d-print-inline { display: inline !important; }
  .d-print-inline-block { display: inline-block !important; }
  .d-print-block { display: block !important; }
  .d-print-table { display: table !important; }
  .d-print-table-row { display: table-row !important; }
  .d-print-table-cell { display: table-cell !important; }
  .d-print-flex { display: flex !important; }
  .d-print-inline-flex { display: inline-flex !important; }
}

.embed-responsive { position: relative; display: block; width: 100%; paddin=
g: 0px; overflow: hidden; }

.embed-responsive::before { display: block; content: ""; }

.embed-responsive .embed-responsive-item, .embed-responsive embed, .embed-r=
esponsive iframe, .embed-responsive object, .embed-responsive video { posit=
ion: absolute; top: 0px; bottom: 0px; left: 0px; width: 100%; height: 100%;=
 border: 0px; }

.embed-responsive-21by9::before { padding-top: 42.8571%; }

.embed-responsive-16by9::before { padding-top: 56.25%; }

.embed-responsive-4by3::before { padding-top: 75%; }

.embed-responsive-1by1::before { padding-top: 100%; }

.flex-row { flex-direction: row !important; }

.flex-column { flex-direction: column !important; }

.flex-row-reverse { flex-direction: row-reverse !important; }

.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }

.flex-nowrap { flex-wrap: nowrap !important; }

.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.flex-fill { flex: 1 1 auto !important; }

.flex-grow-0 { flex-grow: 0 !important; }

.flex-grow-1 { flex-grow: 1 !important; }

.flex-shrink-0 { flex-shrink: 0 !important; }

.flex-shrink-1 { flex-shrink: 1 !important; }

.justify-content-start { justify-content: flex-start !important; }

.justify-content-end { justify-content: flex-end !important; }

.justify-content-center { justify-content: center !important; }

.justify-content-between { justify-content: space-between !important; }

.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }

.align-items-end { align-items: flex-end !important; }

.align-items-center { align-items: center !important; }

.align-items-baseline { align-items: baseline !important; }

.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }

.align-content-end { align-content: flex-end !important; }

.align-content-center { align-content: center !important; }

.align-content-between { align-content: space-between !important; }

.align-content-around { align-content: space-around !important; }

.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }

.align-self-start { align-self: flex-start !important; }

.align-self-end { align-self: flex-end !important; }

.align-self-center { align-self: center !important; }

.align-self-baseline { align-self: baseline !important; }

.align-self-stretch { align-self: stretch !important; }

@media (min-width: 576px) {
  .flex-sm-row { flex-direction: row !important; }
  .flex-sm-column { flex-direction: column !important; }
  .flex-sm-row-reverse { flex-direction: row-reverse !important; }
  .flex-sm-column-reverse { flex-direction: column-reverse !important; }
  .flex-sm-wrap { flex-wrap: wrap !important; }
  .flex-sm-nowrap { flex-wrap: nowrap !important; }
  .flex-sm-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .flex-sm-fill { flex: 1 1 auto !important; }
  .flex-sm-grow-0 { flex-grow: 0 !important; }
  .flex-sm-grow-1 { flex-grow: 1 !important; }
  .flex-sm-shrink-0 { flex-shrink: 0 !important; }
  .flex-sm-shrink-1 { flex-shrink: 1 !important; }
  .justify-content-sm-start { justify-content: flex-start !important; }
  .justify-content-sm-end { justify-content: flex-end !important; }
  .justify-content-sm-center { justify-content: center !important; }
  .justify-content-sm-between { justify-content: space-between !important; =
}
  .justify-content-sm-around { justify-content: space-around !important; }
  .align-items-sm-start { align-items: flex-start !important; }
  .align-items-sm-end { align-items: flex-end !important; }
  .align-items-sm-center { align-items: center !important; }
  .align-items-sm-baseline { align-items: baseline !important; }
  .align-items-sm-stretch { align-items: stretch !important; }
  .align-content-sm-start { align-content: flex-start !important; }
  .align-content-sm-end { align-content: flex-end !important; }
  .align-content-sm-center { align-content: center !important; }
  .align-content-sm-between { align-content: space-between !important; }
  .align-content-sm-around { align-content: space-around !important; }
  .align-content-sm-stretch { align-content: stretch !important; }
  .align-self-sm-auto { align-self: auto !important; }
  .align-self-sm-start { align-self: flex-start !important; }
  .align-self-sm-end { align-self: flex-end !important; }
  .align-self-sm-center { align-self: center !important; }
  .align-self-sm-baseline { align-self: baseline !important; }
  .align-self-sm-stretch { align-self: stretch !important; }
}

@media (min-width: 768px) {
  .flex-md-row { flex-direction: row !important; }
  .flex-md-column { flex-direction: column !important; }
  .flex-md-row-reverse { flex-direction: row-reverse !important; }
  .flex-md-column-reverse { flex-direction: column-reverse !important; }
  .flex-md-wrap { flex-wrap: wrap !important; }
  .flex-md-nowrap { flex-wrap: nowrap !important; }
  .flex-md-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .flex-md-fill { flex: 1 1 auto !important; }
  .flex-md-grow-0 { flex-grow: 0 !important; }
  .flex-md-grow-1 { flex-grow: 1 !important; }
  .flex-md-shrink-0 { flex-shrink: 0 !important; }
  .flex-md-shrink-1 { flex-shrink: 1 !important; }
  .justify-content-md-start { justify-content: flex-start !important; }
  .justify-content-md-end { justify-content: flex-end !important; }
  .justify-content-md-center { justify-content: center !important; }
  .justify-content-md-between { justify-content: space-between !important; =
}
  .justify-content-md-around { justify-content: space-around !important; }
  .align-items-md-start { align-items: flex-start !important; }
  .align-items-md-end { align-items: flex-end !important; }
  .align-items-md-center { align-items: center !important; }
  .align-items-md-baseline { align-items: baseline !important; }
  .align-items-md-stretch { align-items: stretch !important; }
  .align-content-md-start { align-content: flex-start !important; }
  .align-content-md-end { align-content: flex-end !important; }
  .align-content-md-center { align-content: center !important; }
  .align-content-md-between { align-content: space-between !important; }
  .align-content-md-around { align-content: space-around !important; }
  .align-content-md-stretch { align-content: stretch !important; }
  .align-self-md-auto { align-self: auto !important; }
  .align-self-md-start { align-self: flex-start !important; }
  .align-self-md-end { align-self: flex-end !important; }
  .align-self-md-center { align-self: center !important; }
  .align-self-md-baseline { align-self: baseline !important; }
  .align-self-md-stretch { align-self: stretch !important; }
}

@media (min-width: 992px) {
  .flex-lg-row { flex-direction: row !important; }
  .flex-lg-column { flex-direction: column !important; }
  .flex-lg-row-reverse { flex-direction: row-reverse !important; }
  .flex-lg-column-reverse { flex-direction: column-reverse !important; }
  .flex-lg-wrap { flex-wrap: wrap !important; }
  .flex-lg-nowrap { flex-wrap: nowrap !important; }
  .flex-lg-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .flex-lg-fill { flex: 1 1 auto !important; }
  .flex-lg-grow-0 { flex-grow: 0 !important; }
  .flex-lg-grow-1 { flex-grow: 1 !important; }
  .flex-lg-shrink-0 { flex-shrink: 0 !important; }
  .flex-lg-shrink-1 { flex-shrink: 1 !important; }
  .justify-content-lg-start { justify-content: flex-start !important; }
  .justify-content-lg-end { justify-content: flex-end !important; }
  .justify-content-lg-center { justify-content: center !important; }
  .justify-content-lg-between { justify-content: space-between !important; =
}
  .justify-content-lg-around { justify-content: space-around !important; }
  .align-items-lg-start { align-items: flex-start !important; }
  .align-items-lg-end { align-items: flex-end !important; }
  .align-items-lg-center { align-items: center !important; }
  .align-items-lg-baseline { align-items: baseline !important; }
  .align-items-lg-stretch { align-items: stretch !important; }
  .align-content-lg-start { align-content: flex-start !important; }
  .align-content-lg-end { align-content: flex-end !important; }
  .align-content-lg-center { align-content: center !important; }
  .align-content-lg-between { align-content: space-between !important; }
  .align-content-lg-around { align-content: space-around !important; }
  .align-content-lg-stretch { align-content: stretch !important; }
  .align-self-lg-auto { align-self: auto !important; }
  .align-self-lg-start { align-self: flex-start !important; }
  .align-self-lg-end { align-self: flex-end !important; }
  .align-self-lg-center { align-self: center !important; }
  .align-self-lg-baseline { align-self: baseline !important; }
  .align-self-lg-stretch { align-self: stretch !important; }
}

@media (min-width: 1200px) {
  .flex-xl-row { flex-direction: row !important; }
  .flex-xl-column { flex-direction: column !important; }
  .flex-xl-row-reverse { flex-direction: row-reverse !important; }
  .flex-xl-column-reverse { flex-direction: column-reverse !important; }
  .flex-xl-wrap { flex-wrap: wrap !important; }
  .flex-xl-nowrap { flex-wrap: nowrap !important; }
  .flex-xl-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .flex-xl-fill { flex: 1 1 auto !important; }
  .flex-xl-grow-0 { flex-grow: 0 !important; }
  .flex-xl-grow-1 { flex-grow: 1 !important; }
  .flex-xl-shrink-0 { flex-shrink: 0 !important; }
  .flex-xl-shrink-1 { flex-shrink: 1 !important; }
  .justify-content-xl-start { justify-content: flex-start !important; }
  .justify-content-xl-end { justify-content: flex-end !important; }
  .justify-content-xl-center { justify-content: center !important; }
  .justify-content-xl-between { justify-content: space-between !important; =
}
  .justify-content-xl-around { justify-content: space-around !important; }
  .align-items-xl-start { align-items: flex-start !important; }
  .align-items-xl-end { align-items: flex-end !important; }
  .align-items-xl-center { align-items: center !important; }
  .align-items-xl-baseline { align-items: baseline !important; }
  .align-items-xl-stretch { align-items: stretch !important; }
  .align-content-xl-start { align-content: flex-start !important; }
  .align-content-xl-end { align-content: flex-end !important; }
  .align-content-xl-center { align-content: center !important; }
  .align-content-xl-between { align-content: space-between !important; }
  .align-content-xl-around { align-content: space-around !important; }
  .align-content-xl-stretch { align-content: stretch !important; }
  .align-self-xl-auto { align-self: auto !important; }
  .align-self-xl-start { align-self: flex-start !important; }
  .align-self-xl-end { align-self: flex-end !important; }
  .align-self-xl-center { align-self: center !important; }
  .align-self-xl-baseline { align-self: baseline !important; }
  .align-self-xl-stretch { align-self: stretch !important; }
}

.float-left { float: left !important; }

.float-right { float: right !important; }

.float-none { float: none !important; }

@media (min-width: 576px) {
  .float-sm-left { float: left !important; }
  .float-sm-right { float: right !important; }
  .float-sm-none { float: none !important; }
}

@media (min-width: 768px) {
  .float-md-left { float: left !important; }
  .float-md-right { float: right !important; }
  .float-md-none { float: none !important; }
}

@media (min-width: 992px) {
  .float-lg-left { float: left !important; }
  .float-lg-right { float: right !important; }
  .float-lg-none { float: none !important; }
}

@media (min-width: 1200px) {
  .float-xl-left { float: left !important; }
  .float-xl-right { float: right !important; }
  .float-xl-none { float: none !important; }
}

.user-select-all { user-select: all !important; }

.user-select-auto { user-select: auto !important; }

.user-select-none { user-select: none !important; }

.overflow-auto { overflow: auto !important; }

.overflow-hidden { overflow: hidden !important; }

.position-static { position: static !important; }

.position-relative { position: relative !important; }

.position-absolute { position: absolute !important; }

.position-fixed { position: fixed !important; }

.position-sticky { position: sticky !important; }

.fixed-top { position: fixed; top: 0px; right: 0px; left: 0px; z-index: 103=
0; }

.fixed-bottom { position: fixed; right: 0px; bottom: 0px; left: 0px; z-inde=
x: 1030; }

@supports ((position:-webkit-sticky) or (position:sticky)) {
  .sticky-top { position: sticky; top: 0px; z-index: 1020; }
}

.sr-only { position: absolute; width: 1px; height: 1px; padding: 0px; margi=
n: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: now=
rap; border: 0px; }

.sr-only-focusable:active, .sr-only-focusable:focus { position: static; wid=
th: auto; height: auto; overflow: visible; clip: auto; white-space: normal;=
 }

.shadow-sm { box-shadow: rgba(0, 0, 0, 0.075) 0px 0.125rem 0.25rem !importa=
nt; }

.shadow { box-shadow: rgba(0, 0, 0, 0.15) 0px 0.5rem 1rem !important; }

.shadow-lg { box-shadow: rgba(0, 0, 0, 0.176) 0px 1rem 3rem !important; }

.shadow-none { box-shadow: none !important; }

.w-25 { width: 25% !important; }

.w-50 { width: 50% !important; }

.w-75 { width: 75% !important; }

.w-100 { width: 100% !important; }

.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }

.h-50 { height: 50% !important; }

.h-75 { height: 75% !important; }

.h-100 { height: 100% !important; }

.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }

.mh-100 { max-height: 100% !important; }

.min-vw-100 { min-width: 100vw !important; }

.min-vh-100 { min-height: 100vh !important; }

.vw-100 { width: 100vw !important; }

.vh-100 { height: 100vh !important; }

.m-0 { margin: 0px !important; }

.mt-0, .my-0 { margin-top: 0px !important; }

.mr-0, .mx-0 { margin-right: 0px !important; }

.mb-0, .my-0 { margin-bottom: 0px !important; }

.ml-0, .mx-0 { margin-left: 0px !important; }

.m-1 { margin: 0.25rem !important; }

.mt-1, .my-1 { margin-top: 0.25rem !important; }

.mr-1, .mx-1 { margin-right: 0.25rem !important; }

.mb-1, .my-1 { margin-bottom: 0.25rem !important; }

.ml-1, .mx-1 { margin-left: 0.25rem !important; }

.m-2 { margin: 0.5rem !important; }

.mt-2, .my-2 { margin-top: 0.5rem !important; }

.mr-2, .mx-2 { margin-right: 0.5rem !important; }

.mb-2, .my-2 { margin-bottom: 0.5rem !important; }

.ml-2, .mx-2 { margin-left: 0.5rem !important; }

.m-3 { margin: 1rem !important; }

.mt-3, .my-3 { margin-top: 1rem !important; }

.mr-3, .mx-3 { margin-right: 1rem !important; }

.mb-3, .my-3 { margin-bottom: 1rem !important; }

.ml-3, .mx-3 { margin-left: 1rem !important; }

.m-4 { margin: 1.5rem !important; }

.mt-4, .my-4 { margin-top: 1.5rem !important; }

.mr-4, .mx-4 { margin-right: 1.5rem !important; }

.mb-4, .my-4 { margin-bottom: 1.5rem !important; }

.ml-4, .mx-4 { margin-left: 1.5rem !important; }

.m-5 { margin: 3rem !important; }

.mt-5, .my-5 { margin-top: 3rem !important; }

.mr-5, .mx-5 { margin-right: 3rem !important; }

.mb-5, .my-5 { margin-bottom: 3rem !important; }

.ml-5, .mx-5 { margin-left: 3rem !important; }

.p-0 { padding: 0px !important; }

.pt-0, .py-0 { padding-top: 0px !important; }

.pr-0, .px-0 { padding-right: 0px !important; }

.pb-0, .py-0 { padding-bottom: 0px !important; }

.pl-0, .px-0 { padding-left: 0px !important; }

.p-1 { padding: 0.25rem !important; }

.pt-1, .py-1 { padding-top: 0.25rem !important; }

.pr-1, .px-1 { padding-right: 0.25rem !important; }

.pb-1, .py-1 { padding-bottom: 0.25rem !important; }

.pl-1, .px-1 { padding-left: 0.25rem !important; }

.p-2 { padding: 0.5rem !important; }

.pt-2, .py-2 { padding-top: 0.5rem !important; }

.pr-2, .px-2 { padding-right: 0.5rem !important; }

.pb-2, .py-2 { padding-bottom: 0.5rem !important; }

.pl-2, .px-2 { padding-left: 0.5rem !important; }

.p-3 { padding: 1rem !important; }

.pt-3, .py-3 { padding-top: 1rem !important; }

.pr-3, .px-3 { padding-right: 1rem !important; }

.pb-3, .py-3 { padding-bottom: 1rem !important; }

.pl-3, .px-3 { padding-left: 1rem !important; }

.p-4 { padding: 1.5rem !important; }

.pt-4, .py-4 { padding-top: 1.5rem !important; }

.pr-4, .px-4 { padding-right: 1.5rem !important; }

.pb-4, .py-4 { padding-bottom: 1.5rem !important; }

.pl-4, .px-4 { padding-left: 1.5rem !important; }

.p-5 { padding: 3rem !important; }

.pt-5, .py-5 { padding-top: 3rem !important; }

.pr-5, .px-5 { padding-right: 3rem !important; }

.pb-5, .py-5 { padding-bottom: 3rem !important; }

.pl-5, .px-5 { padding-left: 3rem !important; }

.m-n1 { margin: -0.25rem !important; }

.mt-n1, .my-n1 { margin-top: -0.25rem !important; }

.mr-n1, .mx-n1 { margin-right: -0.25rem !important; }

.mb-n1, .my-n1 { margin-bottom: -0.25rem !important; }

.ml-n1, .mx-n1 { margin-left: -0.25rem !important; }

.m-n2 { margin: -0.5rem !important; }

.mt-n2, .my-n2 { margin-top: -0.5rem !important; }

.mr-n2, .mx-n2 { margin-right: -0.5rem !important; }

.mb-n2, .my-n2 { margin-bottom: -0.5rem !important; }

.ml-n2, .mx-n2 { margin-left: -0.5rem !important; }

.m-n3 { margin: -1rem !important; }

.mt-n3, .my-n3 { margin-top: -1rem !important; }

.mr-n3, .mx-n3 { margin-right: -1rem !important; }

.mb-n3, .my-n3 { margin-bottom: -1rem !important; }

.ml-n3, .mx-n3 { margin-left: -1rem !important; }

.m-n4 { margin: -1.5rem !important; }

.mt-n4, .my-n4 { margin-top: -1.5rem !important; }

.mr-n4, .mx-n4 { margin-right: -1.5rem !important; }

.mb-n4, .my-n4 { margin-bottom: -1.5rem !important; }

.ml-n4, .mx-n4 { margin-left: -1.5rem !important; }

.m-n5 { margin: -3rem !important; }

.mt-n5, .my-n5 { margin-top: -3rem !important; }

.mr-n5, .mx-n5 { margin-right: -3rem !important; }

.mb-n5, .my-n5 { margin-bottom: -3rem !important; }

.ml-n5, .mx-n5 { margin-left: -3rem !important; }

.m-auto { margin: auto !important; }

.mt-auto, .my-auto { margin-top: auto !important; }

.mr-auto, .mx-auto { margin-right: auto !important; }

.mb-auto, .my-auto { margin-bottom: auto !important; }

.ml-auto, .mx-auto { margin-left: auto !important; }

@media (min-width: 576px) {
  .m-sm-0 { margin: 0px !important; }
  .mt-sm-0, .my-sm-0 { margin-top: 0px !important; }
  .mr-sm-0, .mx-sm-0 { margin-right: 0px !important; }
  .mb-sm-0, .my-sm-0 { margin-bottom: 0px !important; }
  .ml-sm-0, .mx-sm-0 { margin-left: 0px !important; }
  .m-sm-1 { margin: 0.25rem !important; }
  .mt-sm-1, .my-sm-1 { margin-top: 0.25rem !important; }
  .mr-sm-1, .mx-sm-1 { margin-right: 0.25rem !important; }
  .mb-sm-1, .my-sm-1 { margin-bottom: 0.25rem !important; }
  .ml-sm-1, .mx-sm-1 { margin-left: 0.25rem !important; }
  .m-sm-2 { margin: 0.5rem !important; }
  .mt-sm-2, .my-sm-2 { margin-top: 0.5rem !important; }
  .mr-sm-2, .mx-sm-2 { margin-right: 0.5rem !important; }
  .mb-sm-2, .my-sm-2 { margin-bottom: 0.5rem !important; }
  .ml-sm-2, .mx-sm-2 { margin-left: 0.5rem !important; }
  .m-sm-3 { margin: 1rem !important; }
  .mt-sm-3, .my-sm-3 { margin-top: 1rem !important; }
  .mr-sm-3, .mx-sm-3 { margin-right: 1rem !important; }
  .mb-sm-3, .my-sm-3 { margin-bottom: 1rem !important; }
  .ml-sm-3, .mx-sm-3 { margin-left: 1rem !important; }
  .m-sm-4 { margin: 1.5rem !important; }
  .mt-sm-4, .my-sm-4 { margin-top: 1.5rem !important; }
  .mr-sm-4, .mx-sm-4 { margin-right: 1.5rem !important; }
  .mb-sm-4, .my-sm-4 { margin-bottom: 1.5rem !important; }
  .ml-sm-4, .mx-sm-4 { margin-left: 1.5rem !important; }
  .m-sm-5 { margin: 3rem !important; }
  .mt-sm-5, .my-sm-5 { margin-top: 3rem !important; }
  .mr-sm-5, .mx-sm-5 { margin-right: 3rem !important; }
  .mb-sm-5, .my-sm-5 { margin-bottom: 3rem !important; }
  .ml-sm-5, .mx-sm-5 { margin-left: 3rem !important; }
  .p-sm-0 { padding: 0px !important; }
  .pt-sm-0, .py-sm-0 { padding-top: 0px !important; }
  .pr-sm-0, .px-sm-0 { padding-right: 0px !important; }
  .pb-sm-0, .py-sm-0 { padding-bottom: 0px !important; }
  .pl-sm-0, .px-sm-0 { padding-left: 0px !important; }
  .p-sm-1 { padding: 0.25rem !important; }
  .pt-sm-1, .py-sm-1 { padding-top: 0.25rem !important; }
  .pr-sm-1, .px-sm-1 { padding-right: 0.25rem !important; }
  .pb-sm-1, .py-sm-1 { padding-bottom: 0.25rem !important; }
  .pl-sm-1, .px-sm-1 { padding-left: 0.25rem !important; }
  .p-sm-2 { padding: 0.5rem !important; }
  .pt-sm-2, .py-sm-2 { padding-top: 0.5rem !important; }
  .pr-sm-2, .px-sm-2 { padding-right: 0.5rem !important; }
  .pb-sm-2, .py-sm-2 { padding-bottom: 0.5rem !important; }
  .pl-sm-2, .px-sm-2 { padding-left: 0.5rem !important; }
  .p-sm-3 { padding: 1rem !important; }
  .pt-sm-3, .py-sm-3 { padding-top: 1rem !important; }
  .pr-sm-3, .px-sm-3 { padding-right: 1rem !important; }
  .pb-sm-3, .py-sm-3 { padding-bottom: 1rem !important; }
  .pl-sm-3, .px-sm-3 { padding-left: 1rem !important; }
  .p-sm-4 { padding: 1.5rem !important; }
  .pt-sm-4, .py-sm-4 { padding-top: 1.5rem !important; }
  .pr-sm-4, .px-sm-4 { padding-right: 1.5rem !important; }
  .pb-sm-4, .py-sm-4 { padding-bottom: 1.5rem !important; }
  .pl-sm-4, .px-sm-4 { padding-left: 1.5rem !important; }
  .p-sm-5 { padding: 3rem !important; }
  .pt-sm-5, .py-sm-5 { padding-top: 3rem !important; }
  .pr-sm-5, .px-sm-5 { padding-right: 3rem !important; }
  .pb-sm-5, .py-sm-5 { padding-bottom: 3rem !important; }
  .pl-sm-5, .px-sm-5 { padding-left: 3rem !important; }
  .m-sm-n1 { margin: -0.25rem !important; }
  .mt-sm-n1, .my-sm-n1 { margin-top: -0.25rem !important; }
  .mr-sm-n1, .mx-sm-n1 { margin-right: -0.25rem !important; }
  .mb-sm-n1, .my-sm-n1 { margin-bottom: -0.25rem !important; }
  .ml-sm-n1, .mx-sm-n1 { margin-left: -0.25rem !important; }
  .m-sm-n2 { margin: -0.5rem !important; }
  .mt-sm-n2, .my-sm-n2 { margin-top: -0.5rem !important; }
  .mr-sm-n2, .mx-sm-n2 { margin-right: -0.5rem !important; }
  .mb-sm-n2, .my-sm-n2 { margin-bottom: -0.5rem !important; }
  .ml-sm-n2, .mx-sm-n2 { margin-left: -0.5rem !important; }
  .m-sm-n3 { margin: -1rem !important; }
  .mt-sm-n3, .my-sm-n3 { margin-top: -1rem !important; }
  .mr-sm-n3, .mx-sm-n3 { margin-right: -1rem !important; }
  .mb-sm-n3, .my-sm-n3 { margin-bottom: -1rem !important; }
  .ml-sm-n3, .mx-sm-n3 { margin-left: -1rem !important; }
  .m-sm-n4 { margin: -1.5rem !important; }
  .mt-sm-n4, .my-sm-n4 { margin-top: -1.5rem !important; }
  .mr-sm-n4, .mx-sm-n4 { margin-right: -1.5rem !important; }
  .mb-sm-n4, .my-sm-n4 { margin-bottom: -1.5rem !important; }
  .ml-sm-n4, .mx-sm-n4 { margin-left: -1.5rem !important; }
  .m-sm-n5 { margin: -3rem !important; }
  .mt-sm-n5, .my-sm-n5 { margin-top: -3rem !important; }
  .mr-sm-n5, .mx-sm-n5 { margin-right: -3rem !important; }
  .mb-sm-n5, .my-sm-n5 { margin-bottom: -3rem !important; }
  .ml-sm-n5, .mx-sm-n5 { margin-left: -3rem !important; }
  .m-sm-auto { margin: auto !important; }
  .mt-sm-auto, .my-sm-auto { margin-top: auto !important; }
  .mr-sm-auto, .mx-sm-auto { margin-right: auto !important; }
  .mb-sm-auto, .my-sm-auto { margin-bottom: auto !important; }
  .ml-sm-auto, .mx-sm-auto { margin-left: auto !important; }
}

@media (min-width: 768px) {
  .m-md-0 { margin: 0px !important; }
  .mt-md-0, .my-md-0 { margin-top: 0px !important; }
  .mr-md-0, .mx-md-0 { margin-right: 0px !important; }
  .mb-md-0, .my-md-0 { margin-bottom: 0px !important; }
  .ml-md-0, .mx-md-0 { margin-left: 0px !important; }
  .m-md-1 { margin: 0.25rem !important; }
  .mt-md-1, .my-md-1 { margin-top: 0.25rem !important; }
  .mr-md-1, .mx-md-1 { margin-right: 0.25rem !important; }
  .mb-md-1, .my-md-1 { margin-bottom: 0.25rem !important; }
  .ml-md-1, .mx-md-1 { margin-left: 0.25rem !important; }
  .m-md-2 { margin: 0.5rem !important; }
  .mt-md-2, .my-md-2 { margin-top: 0.5rem !important; }
  .mr-md-2, .mx-md-2 { margin-right: 0.5rem !important; }
  .mb-md-2, .my-md-2 { margin-bottom: 0.5rem !important; }
  .ml-md-2, .mx-md-2 { margin-left: 0.5rem !important; }
  .m-md-3 { margin: 1rem !important; }
  .mt-md-3, .my-md-3 { margin-top: 1rem !important; }
  .mr-md-3, .mx-md-3 { margin-right: 1rem !important; }
  .mb-md-3, .my-md-3 { margin-bottom: 1rem !important; }
  .ml-md-3, .mx-md-3 { margin-left: 1rem !important; }
  .m-md-4 { margin: 1.5rem !important; }
  .mt-md-4, .my-md-4 { margin-top: 1.5rem !important; }
  .mr-md-4, .mx-md-4 { margin-right: 1.5rem !important; }
  .mb-md-4, .my-md-4 { margin-bottom: 1.5rem !important; }
  .ml-md-4, .mx-md-4 { margin-left: 1.5rem !important; }
  .m-md-5 { margin: 3rem !important; }
  .mt-md-5, .my-md-5 { margin-top: 3rem !important; }
  .mr-md-5, .mx-md-5 { margin-right: 3rem !important; }
  .mb-md-5, .my-md-5 { margin-bottom: 3rem !important; }
  .ml-md-5, .mx-md-5 { margin-left: 3rem !important; }
  .p-md-0 { padding: 0px !important; }
  .pt-md-0, .py-md-0 { padding-top: 0px !important; }
  .pr-md-0, .px-md-0 { padding-right: 0px !important; }
  .pb-md-0, .py-md-0 { padding-bottom: 0px !important; }
  .pl-md-0, .px-md-0 { padding-left: 0px !important; }
  .p-md-1 { padding: 0.25rem !important; }
  .pt-md-1, .py-md-1 { padding-top: 0.25rem !important; }
  .pr-md-1, .px-md-1 { padding-right: 0.25rem !important; }
  .pb-md-1, .py-md-1 { padding-bottom: 0.25rem !important; }
  .pl-md-1, .px-md-1 { padding-left: 0.25rem !important; }
  .p-md-2 { padding: 0.5rem !important; }
  .pt-md-2, .py-md-2 { padding-top: 0.5rem !important; }
  .pr-md-2, .px-md-2 { padding-right: 0.5rem !important; }
  .pb-md-2, .py-md-2 { padding-bottom: 0.5rem !important; }
  .pl-md-2, .px-md-2 { padding-left: 0.5rem !important; }
  .p-md-3 { padding: 1rem !important; }
  .pt-md-3, .py-md-3 { padding-top: 1rem !important; }
  .pr-md-3, .px-md-3 { padding-right: 1rem !important; }
  .pb-md-3, .py-md-3 { padding-bottom: 1rem !important; }
  .pl-md-3, .px-md-3 { padding-left: 1rem !important; }
  .p-md-4 { padding: 1.5rem !important; }
  .pt-md-4, .py-md-4 { padding-top: 1.5rem !important; }
  .pr-md-4, .px-md-4 { padding-right: 1.5rem !important; }
  .pb-md-4, .py-md-4 { padding-bottom: 1.5rem !important; }
  .pl-md-4, .px-md-4 { padding-left: 1.5rem !important; }
  .p-md-5 { padding: 3rem !important; }
  .pt-md-5, .py-md-5 { padding-top: 3rem !important; }
  .pr-md-5, .px-md-5 { padding-right: 3rem !important; }
  .pb-md-5, .py-md-5 { padding-bottom: 3rem !important; }
  .pl-md-5, .px-md-5 { padding-left: 3rem !important; }
  .m-md-n1 { margin: -0.25rem !important; }
  .mt-md-n1, .my-md-n1 { margin-top: -0.25rem !important; }
  .mr-md-n1, .mx-md-n1 { margin-right: -0.25rem !important; }
  .mb-md-n1, .my-md-n1 { margin-bottom: -0.25rem !important; }
  .ml-md-n1, .mx-md-n1 { margin-left: -0.25rem !important; }
  .m-md-n2 { margin: -0.5rem !important; }
  .mt-md-n2, .my-md-n2 { margin-top: -0.5rem !important; }
  .mr-md-n2, .mx-md-n2 { margin-right: -0.5rem !important; }
  .mb-md-n2, .my-md-n2 { margin-bottom: -0.5rem !important; }
  .ml-md-n2, .mx-md-n2 { margin-left: -0.5rem !important; }
  .m-md-n3 { margin: -1rem !important; }
  .mt-md-n3, .my-md-n3 { margin-top: -1rem !important; }
  .mr-md-n3, .mx-md-n3 { margin-right: -1rem !important; }
  .mb-md-n3, .my-md-n3 { margin-bottom: -1rem !important; }
  .ml-md-n3, .mx-md-n3 { margin-left: -1rem !important; }
  .m-md-n4 { margin: -1.5rem !important; }
  .mt-md-n4, .my-md-n4 { margin-top: -1.5rem !important; }
  .mr-md-n4, .mx-md-n4 { margin-right: -1.5rem !important; }
  .mb-md-n4, .my-md-n4 { margin-bottom: -1.5rem !important; }
  .ml-md-n4, .mx-md-n4 { margin-left: -1.5rem !important; }
  .m-md-n5 { margin: -3rem !important; }
  .mt-md-n5, .my-md-n5 { margin-top: -3rem !important; }
  .mr-md-n5, .mx-md-n5 { margin-right: -3rem !important; }
  .mb-md-n5, .my-md-n5 { margin-bottom: -3rem !important; }
  .ml-md-n5, .mx-md-n5 { margin-left: -3rem !important; }
  .m-md-auto { margin: auto !important; }
  .mt-md-auto, .my-md-auto { margin-top: auto !important; }
  .mr-md-auto, .mx-md-auto { margin-right: auto !important; }
  .mb-md-auto, .my-md-auto { margin-bottom: auto !important; }
  .ml-md-auto, .mx-md-auto { margin-left: auto !important; }
}

@media (min-width: 992px) {
  .m-lg-0 { margin: 0px !important; }
  .mt-lg-0, .my-lg-0 { margin-top: 0px !important; }
  .mr-lg-0, .mx-lg-0 { margin-right: 0px !important; }
  .mb-lg-0, .my-lg-0 { margin-bottom: 0px !important; }
  .ml-lg-0, .mx-lg-0 { margin-left: 0px !important; }
  .m-lg-1 { margin: 0.25rem !important; }
  .mt-lg-1, .my-lg-1 { margin-top: 0.25rem !important; }
  .mr-lg-1, .mx-lg-1 { margin-right: 0.25rem !important; }
  .mb-lg-1, .my-lg-1 { margin-bottom: 0.25rem !important; }
  .ml-lg-1, .mx-lg-1 { margin-left: 0.25rem !important; }
  .m-lg-2 { margin: 0.5rem !important; }
  .mt-lg-2, .my-lg-2 { margin-top: 0.5rem !important; }
  .mr-lg-2, .mx-lg-2 { margin-right: 0.5rem !important; }
  .mb-lg-2, .my-lg-2 { margin-bottom: 0.5rem !important; }
  .ml-lg-2, .mx-lg-2 { margin-left: 0.5rem !important; }
  .m-lg-3 { margin: 1rem !important; }
  .mt-lg-3, .my-lg-3 { margin-top: 1rem !important; }
  .mr-lg-3, .mx-lg-3 { margin-right: 1rem !important; }
  .mb-lg-3, .my-lg-3 { margin-bottom: 1rem !important; }
  .ml-lg-3, .mx-lg-3 { margin-left: 1rem !important; }
  .m-lg-4 { margin: 1.5rem !important; }
  .mt-lg-4, .my-lg-4 { margin-top: 1.5rem !important; }
  .mr-lg-4, .mx-lg-4 { margin-right: 1.5rem !important; }
  .mb-lg-4, .my-lg-4 { margin-bottom: 1.5rem !important; }
  .ml-lg-4, .mx-lg-4 { margin-left: 1.5rem !important; }
  .m-lg-5 { margin: 3rem !important; }
  .mt-lg-5, .my-lg-5 { margin-top: 3rem !important; }
  .mr-lg-5, .mx-lg-5 { margin-right: 3rem !important; }
  .mb-lg-5, .my-lg-5 { margin-bottom: 3rem !important; }
  .ml-lg-5, .mx-lg-5 { margin-left: 3rem !important; }
  .p-lg-0 { padding: 0px !important; }
  .pt-lg-0, .py-lg-0 { padding-top: 0px !important; }
  .pr-lg-0, .px-lg-0 { padding-right: 0px !important; }
  .pb-lg-0, .py-lg-0 { padding-bottom: 0px !important; }
  .pl-lg-0, .px-lg-0 { padding-left: 0px !important; }
  .p-lg-1 { padding: 0.25rem !important; }
  .pt-lg-1, .py-lg-1 { padding-top: 0.25rem !important; }
  .pr-lg-1, .px-lg-1 { padding-right: 0.25rem !important; }
  .pb-lg-1, .py-lg-1 { padding-bottom: 0.25rem !important; }
  .pl-lg-1, .px-lg-1 { padding-left: 0.25rem !important; }
  .p-lg-2 { padding: 0.5rem !important; }
  .pt-lg-2, .py-lg-2 { padding-top: 0.5rem !important; }
  .pr-lg-2, .px-lg-2 { padding-right: 0.5rem !important; }
  .pb-lg-2, .py-lg-2 { padding-bottom: 0.5rem !important; }
  .pl-lg-2, .px-lg-2 { padding-left: 0.5rem !important; }
  .p-lg-3 { padding: 1rem !important; }
  .pt-lg-3, .py-lg-3 { padding-top: 1rem !important; }
  .pr-lg-3, .px-lg-3 { padding-right: 1rem !important; }
  .pb-lg-3, .py-lg-3 { padding-bottom: 1rem !important; }
  .pl-lg-3, .px-lg-3 { padding-left: 1rem !important; }
  .p-lg-4 { padding: 1.5rem !important; }
  .pt-lg-4, .py-lg-4 { padding-top: 1.5rem !important; }
  .pr-lg-4, .px-lg-4 { padding-right: 1.5rem !important; }
  .pb-lg-4, .py-lg-4 { padding-bottom: 1.5rem !important; }
  .pl-lg-4, .px-lg-4 { padding-left: 1.5rem !important; }
  .p-lg-5 { padding: 3rem !important; }
  .pt-lg-5, .py-lg-5 { padding-top: 3rem !important; }
  .pr-lg-5, .px-lg-5 { padding-right: 3rem !important; }
  .pb-lg-5, .py-lg-5 { padding-bottom: 3rem !important; }
  .pl-lg-5, .px-lg-5 { padding-left: 3rem !important; }
  .m-lg-n1 { margin: -0.25rem !important; }
  .mt-lg-n1, .my-lg-n1 { margin-top: -0.25rem !important; }
  .mr-lg-n1, .mx-lg-n1 { margin-right: -0.25rem !important; }
  .mb-lg-n1, .my-lg-n1 { margin-bottom: -0.25rem !important; }
  .ml-lg-n1, .mx-lg-n1 { margin-left: -0.25rem !important; }
  .m-lg-n2 { margin: -0.5rem !important; }
  .mt-lg-n2, .my-lg-n2 { margin-top: -0.5rem !important; }
  .mr-lg-n2, .mx-lg-n2 { margin-right: -0.5rem !important; }
  .mb-lg-n2, .my-lg-n2 { margin-bottom: -0.5rem !important; }
  .ml-lg-n2, .mx-lg-n2 { margin-left: -0.5rem !important; }
  .m-lg-n3 { margin: -1rem !important; }
  .mt-lg-n3, .my-lg-n3 { margin-top: -1rem !important; }
  .mr-lg-n3, .mx-lg-n3 { margin-right: -1rem !important; }
  .mb-lg-n3, .my-lg-n3 { margin-bottom: -1rem !important; }
  .ml-lg-n3, .mx-lg-n3 { margin-left: -1rem !important; }
  .m-lg-n4 { margin: -1.5rem !important; }
  .mt-lg-n4, .my-lg-n4 { margin-top: -1.5rem !important; }
  .mr-lg-n4, .mx-lg-n4 { margin-right: -1.5rem !important; }
  .mb-lg-n4, .my-lg-n4 { margin-bottom: -1.5rem !important; }
  .ml-lg-n4, .mx-lg-n4 { margin-left: -1.5rem !important; }
  .m-lg-n5 { margin: -3rem !important; }
  .mt-lg-n5, .my-lg-n5 { margin-top: -3rem !important; }
  .mr-lg-n5, .mx-lg-n5 { margin-right: -3rem !important; }
  .mb-lg-n5, .my-lg-n5 { margin-bottom: -3rem !important; }
  .ml-lg-n5, .mx-lg-n5 { margin-left: -3rem !important; }
  .m-lg-auto { margin: auto !important; }
  .mt-lg-auto, .my-lg-auto { margin-top: auto !important; }
  .mr-lg-auto, .mx-lg-auto { margin-right: auto !important; }
  .mb-lg-auto, .my-lg-auto { margin-bottom: auto !important; }
  .ml-lg-auto, .mx-lg-auto { margin-left: auto !important; }
}

@media (min-width: 1200px) {
  .m-xl-0 { margin: 0px !important; }
  .mt-xl-0, .my-xl-0 { margin-top: 0px !important; }
  .mr-xl-0, .mx-xl-0 { margin-right: 0px !important; }
  .mb-xl-0, .my-xl-0 { margin-bottom: 0px !important; }
  .ml-xl-0, .mx-xl-0 { margin-left: 0px !important; }
  .m-xl-1 { margin: 0.25rem !important; }
  .mt-xl-1, .my-xl-1 { margin-top: 0.25rem !important; }
  .mr-xl-1, .mx-xl-1 { margin-right: 0.25rem !important; }
  .mb-xl-1, .my-xl-1 { margin-bottom: 0.25rem !important; }
  .ml-xl-1, .mx-xl-1 { margin-left: 0.25rem !important; }
  .m-xl-2 { margin: 0.5rem !important; }
  .mt-xl-2, .my-xl-2 { margin-top: 0.5rem !important; }
  .mr-xl-2, .mx-xl-2 { margin-right: 0.5rem !important; }
  .mb-xl-2, .my-xl-2 { margin-bottom: 0.5rem !important; }
  .ml-xl-2, .mx-xl-2 { margin-left: 0.5rem !important; }
  .m-xl-3 { margin: 1rem !important; }
  .mt-xl-3, .my-xl-3 { margin-top: 1rem !important; }
  .mr-xl-3, .mx-xl-3 { margin-right: 1rem !important; }
  .mb-xl-3, .my-xl-3 { margin-bottom: 1rem !important; }
  .ml-xl-3, .mx-xl-3 { margin-left: 1rem !important; }
  .m-xl-4 { margin: 1.5rem !important; }
  .mt-xl-4, .my-xl-4 { margin-top: 1.5rem !important; }
  .mr-xl-4, .mx-xl-4 { margin-right: 1.5rem !important; }
  .mb-xl-4, .my-xl-4 { margin-bottom: 1.5rem !important; }
  .ml-xl-4, .mx-xl-4 { margin-left: 1.5rem !important; }
  .m-xl-5 { margin: 3rem !important; }
  .mt-xl-5, .my-xl-5 { margin-top: 3rem !important; }
  .mr-xl-5, .mx-xl-5 { margin-right: 3rem !important; }
  .mb-xl-5, .my-xl-5 { margin-bottom: 3rem !important; }
  .ml-xl-5, .mx-xl-5 { margin-left: 3rem !important; }
  .p-xl-0 { padding: 0px !important; }
  .pt-xl-0, .py-xl-0 { padding-top: 0px !important; }
  .pr-xl-0, .px-xl-0 { padding-right: 0px !important; }
  .pb-xl-0, .py-xl-0 { padding-bottom: 0px !important; }
  .pl-xl-0, .px-xl-0 { padding-left: 0px !important; }
  .p-xl-1 { padding: 0.25rem !important; }
  .pt-xl-1, .py-xl-1 { padding-top: 0.25rem !important; }
  .pr-xl-1, .px-xl-1 { padding-right: 0.25rem !important; }
  .pb-xl-1, .py-xl-1 { padding-bottom: 0.25rem !important; }
  .pl-xl-1, .px-xl-1 { padding-left: 0.25rem !important; }
  .p-xl-2 { padding: 0.5rem !important; }
  .pt-xl-2, .py-xl-2 { padding-top: 0.5rem !important; }
  .pr-xl-2, .px-xl-2 { padding-right: 0.5rem !important; }
  .pb-xl-2, .py-xl-2 { padding-bottom: 0.5rem !important; }
  .pl-xl-2, .px-xl-2 { padding-left: 0.5rem !important; }
  .p-xl-3 { padding: 1rem !important; }
  .pt-xl-3, .py-xl-3 { padding-top: 1rem !important; }
  .pr-xl-3, .px-xl-3 { padding-right: 1rem !important; }
  .pb-xl-3, .py-xl-3 { padding-bottom: 1rem !important; }
  .pl-xl-3, .px-xl-3 { padding-left: 1rem !important; }
  .p-xl-4 { padding: 1.5rem !important; }
  .pt-xl-4, .py-xl-4 { padding-top: 1.5rem !important; }
  .pr-xl-4, .px-xl-4 { padding-right: 1.5rem !important; }
  .pb-xl-4, .py-xl-4 { padding-bottom: 1.5rem !important; }
  .pl-xl-4, .px-xl-4 { padding-left: 1.5rem !important; }
  .p-xl-5 { padding: 3rem !important; }
  .pt-xl-5, .py-xl-5 { padding-top: 3rem !important; }
  .pr-xl-5, .px-xl-5 { padding-right: 3rem !important; }
  .pb-xl-5, .py-xl-5 { padding-bottom: 3rem !important; }
  .pl-xl-5, .px-xl-5 { padding-left: 3rem !important; }
  .m-xl-n1 { margin: -0.25rem !important; }
  .mt-xl-n1, .my-xl-n1 { margin-top: -0.25rem !important; }
  .mr-xl-n1, .mx-xl-n1 { margin-right: -0.25rem !important; }
  .mb-xl-n1, .my-xl-n1 { margin-bottom: -0.25rem !important; }
  .ml-xl-n1, .mx-xl-n1 { margin-left: -0.25rem !important; }
  .m-xl-n2 { margin: -0.5rem !important; }
  .mt-xl-n2, .my-xl-n2 { margin-top: -0.5rem !important; }
  .mr-xl-n2, .mx-xl-n2 { margin-right: -0.5rem !important; }
  .mb-xl-n2, .my-xl-n2 { margin-bottom: -0.5rem !important; }
  .ml-xl-n2, .mx-xl-n2 { margin-left: -0.5rem !important; }
  .m-xl-n3 { margin: -1rem !important; }
  .mt-xl-n3, .my-xl-n3 { margin-top: -1rem !important; }
  .mr-xl-n3, .mx-xl-n3 { margin-right: -1rem !important; }
  .mb-xl-n3, .my-xl-n3 { margin-bottom: -1rem !important; }
  .ml-xl-n3, .mx-xl-n3 { margin-left: -1rem !important; }
  .m-xl-n4 { margin: -1.5rem !important; }
  .mt-xl-n4, .my-xl-n4 { margin-top: -1.5rem !important; }
  .mr-xl-n4, .mx-xl-n4 { margin-right: -1.5rem !important; }
  .mb-xl-n4, .my-xl-n4 { margin-bottom: -1.5rem !important; }
  .ml-xl-n4, .mx-xl-n4 { margin-left: -1.5rem !important; }
  .m-xl-n5 { margin: -3rem !important; }
  .mt-xl-n5, .my-xl-n5 { margin-top: -3rem !important; }
  .mr-xl-n5, .mx-xl-n5 { margin-right: -3rem !important; }
  .mb-xl-n5, .my-xl-n5 { margin-bottom: -3rem !important; }
  .ml-xl-n5, .mx-xl-n5 { margin-left: -3rem !important; }
  .m-xl-auto { margin: auto !important; }
  .mt-xl-auto, .my-xl-auto { margin-top: auto !important; }
  .mr-xl-auto, .mx-xl-auto { margin-right: auto !important; }
  .mb-xl-auto, .my-xl-auto { margin-bottom: auto !important; }
  .ml-xl-auto, .mx-xl-auto { margin-left: auto !important; }
}

.stretched-link::after { position: absolute; inset: 0px; z-index: 1; pointe=
r-events: auto; content: ""; background-color: rgba(0, 0, 0, 0); }

.text-monospace { font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Li=
beration Mono", "Courier New", monospace !important; }

.text-justify { text-align: justify !important; }

.text-wrap { white-space: normal !important; }

.text-nowrap { white-space: nowrap !important; }

.text-truncate { overflow: hidden; text-overflow: ellipsis; white-space: no=
wrap; }

.text-left { text-align: left !important; }

.text-right { text-align: right !important; }

.text-center { text-align: center !important; }

@media (min-width: 576px) {
  .text-sm-left { text-align: left !important; }
  .text-sm-right { text-align: right !important; }
  .text-sm-center { text-align: center !important; }
}

@media (min-width: 768px) {
  .text-md-left { text-align: left !important; }
  .text-md-right { text-align: right !important; }
  .text-md-center { text-align: center !important; }
}

@media (min-width: 992px) {
  .text-lg-left { text-align: left !important; }
  .text-lg-right { text-align: right !important; }
  .text-lg-center { text-align: center !important; }
}

@media (min-width: 1200px) {
  .text-xl-left { text-align: left !important; }
  .text-xl-right { text-align: right !important; }
  .text-xl-center { text-align: center !important; }
}

.text-lowercase { text-transform: lowercase !important; }

.text-uppercase { text-transform: uppercase !important; }

.text-capitalize { text-transform: capitalize !important; }

.font-weight-light { font-weight: 300 !important; }

.font-weight-lighter { font-weight: lighter !important; }

.font-weight-normal { font-weight: 400 !important; }

.font-weight-bold { font-weight: 700 !important; }

.font-weight-bolder { font-weight: bolder !important; }

.font-italic { font-style: italic !important; }

.text-white { color: rgb(255, 255, 255) !important; }

.text-primary { color: rgb(0, 123, 255) !important; }

a.text-primary:focus, a.text-primary:hover { color: rgb(0, 86, 179) !import=
ant; }

.text-secondary { color: rgb(108, 117, 125) !important; }

a.text-secondary:focus, a.text-secondary:hover { color: rgb(73, 79, 84) !im=
portant; }

.text-success { color: rgb(40, 167, 69) !important; }

a.text-success:focus, a.text-success:hover { color: rgb(25, 105, 44) !impor=
tant; }

.text-info { color: rgb(23, 162, 184) !important; }

a.text-info:focus, a.text-info:hover { color: rgb(15, 102, 116) !important;=
 }

.text-warning { color: rgb(255, 193, 7) !important; }

a.text-warning:focus, a.text-warning:hover { color: rgb(186, 139, 0) !impor=
tant; }

.text-danger { color: rgb(220, 53, 69) !important; }

a.text-danger:focus, a.text-danger:hover { color: rgb(167, 29, 42) !importa=
nt; }

.text-light { color: rgb(248, 249, 250) !important; }

a.text-light:focus, a.text-light:hover { color: rgb(203, 211, 218) !importa=
nt; }

.text-dark { color: rgb(52, 58, 64) !important; }

a.text-dark:focus, a.text-dark:hover { color: rgb(18, 20, 22) !important; }

.text-body { color: rgb(33, 37, 41) !important; }

.text-muted { color: rgb(108, 117, 125) !important; }

.text-black-50 { color: rgba(0, 0, 0, 0.5) !important; }

.text-white-50 { color: rgba(255, 255, 255, 0.5) !important; }

.text-hide { font: 0px / 0 a; color: transparent; text-shadow: none; backgr=
ound-color: transparent; border: 0px; }

.text-decoration-none { text-decoration: none !important; }

.text-break { word-break: break-word !important; overflow-wrap: break-word =
!important; }

.text-reset { color: inherit !important; }

.visible { visibility: visible !important; }

.invisible { visibility: hidden !important; }

@media print {
  *, ::after, ::before { text-shadow: none !important; box-shadow: none !im=
portant; }
  a:not(.btn) { text-decoration: underline; }
  abbr[title]::after { content: " (" attr(title) ")"; }
  pre { white-space: pre-wrap !important; }
  blockquote, pre { border: 1px solid rgb(173, 181, 189); break-inside: avo=
id; }
  img, tr { break-inside: avoid; }
  h2, h3, p { orphans: 3; widows: 3; }
  h2, h3 { break-after: avoid; }
  @page { size: a3; }
  body { min-width: 992px !important; }
  .container { min-width: 992px !important; }
  .navbar { display: none; }
  .badge { border: 1px solid rgb(0, 0, 0); }
  .table { border-collapse: collapse !important; }
  .table td, .table th { background-color: rgb(255, 255, 255) !important; }
  .table-bordered td, .table-bordered th { border: 1px solid rgb(222, 226, =
230) !important; }
  .table-dark { color: inherit; }
  .table-dark tbody + tbody, .table-dark td, .table-dark th, .table-dark th=
ead th { border-color: rgb(222, 226, 230); }
  .table .thead-dark th { color: inherit; border-color: rgb(222, 226, 230);=
 }
}
------MultipartBoundary--TVvrKUIs0YsrHrZDdevYX6VzSO2uNL6PjYbdkuk7RH------
