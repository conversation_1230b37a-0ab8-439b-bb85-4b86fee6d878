{"界面模块": [{"名称": "首页导航仪表盘", "功能": "展示GPTs总览、当前激活智能体、消息状态、任务进度", "交互元素": ["当前活跃智能体卡片", "技能调用历史", "新建任务入口", "性能监控图"]}, {"名称": "GPTs配置与编辑器", "功能": "支持身份、指令、知识库、技能、工作流配置与版本管理", "交互元素": ["结构化编辑表单", "上传文档区域", "技能注册按钮", "版本切换下拉菜单"]}, {"名称": "技能管理中心", "功能": "集中管理、授权、继承、搜索与调用各GPT技能", "交互元素": ["技能搜索框", "继承链路图", "技能权限配置", "调用次数统计"]}, {"名称": "联合记忆中心", "功能": "显示并管理跨GPT共享记忆与知识调用历史", "交互元素": ["知识调用热力图", "记忆片段审阅按钮", "共享权限调整"]}, {"名称": "多体协作调度控制台", "功能": "展示GPT之间的任务协作链与执行状态流图", "交互元素": ["流程图可视界面", "节点状态指示器", "手动干预开关", "中断/重试操作"]}], "用户流程": ["进入首页仪表盘 -> 选择/创建智能体 -> 配置身份/知识/技能 -> 测试交互效果 -> 发布或协作调度"], "能力图谱核心维度": {"维度": ["身份类型", "知识域", "技能集合", "工作流复杂度", "记忆授权范围"], "可视化方式": "使用五轴雷达图或能力地图进行展示和比较"}}