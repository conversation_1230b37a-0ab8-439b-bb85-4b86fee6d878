<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人类历史与进化时间线</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --cosmic-color: #8B4FE5;
            --evolution-color: #4CAF50;
            --culture-color: #FF9800;
            --empire-color: #F44336;
            --science-color: #2196F3;
            --industrial-color: #795548;
            --tech-color: #607D8B;
            --sustainability-color: #009688;
            --bg-color: #f8f9fa;
            --text-color: #343a40;
            --border-color: #dee2e6;
        }
        
        body {
            font-family: 'Noto Sans SC', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            overflow-x: hidden;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        header {
            text-align: center;
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
            color: #fff;
            border-radius: 8px;
        }
        
        h1 {
            margin: 0;
            font-size: 1.8rem;
        }
        
        header p {
            margin: 5px 0 0;
            font-size: 0.9rem;
        }
        
        .controls-wrapper {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .search-container {
            flex: 1;
        }
        
        #searchInput {
            width: 100%;
            padding: 8px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            font-size: 0.9rem;
            transition: border-color 0.3s, box-shadow 0.3s;
        }
        #searchInput:focus {
            border-color: var(--science-color);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
            outline: none;
        }
        .scale-control {
            display: flex;
            gap: 5px;
            overflow-x: auto;
            white-space: nowrap;
            padding-bottom: 5px;
        }
        
        .scale-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background-color: #fff;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background-color 0.3s, color 0.3s, border-color 0.3s;
            flex-shrink: 0;
        }
        
        .scale-btn:hover, .scale-btn.active {
            background-color: var(--science-color);
            color: #fff;
            border-color: var(--science-color);
        }
        
        .category-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .category-filter {
            padding: 4px 8px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.75rem;
            font-weight: 500;
            border: none;
            color: #fff;
            transition: opacity 0.3s;
        }
        
        .category-filter.active {
            opacity: 1;
        }
        
        .category-filter:not(.active) {
            opacity: 0.6;
        }
        
        .category-filter[data-category="cosmic"] { background-color: var(--cosmic-color); }
        .category-filter[data-category="evolution"] { background-color: var(--evolution-color); }
        .category-filter[data-category="culture"] { background-color: var(--culture-color); }
        .category-filter[data-category="empire"] { background-color: var(--empire-color); }
        .category-filter[data-category="science"] { background-color: var(--science-color); }
        .category-filter[data-category="industrial"] { background-color: var(--industrial-color); }
        .category-filter[data-category="tech"] { background-color: var(--tech-color); }
        .category-filter[data-category="sustainability"] { background-color: var(--sustainability-color); }
        
        .timeline-container {
            position: relative;
            background-color: #fff;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .timeline-viewport {
            position: relative;
            overflow: hidden;
            height: 400px;
        }
        
        .timeline {
            position: relative;
            height: 400px;
            overflow: hidden;
            cursor: grab;
            will-change: transform; /* 性能优化提示 */
        }
        
        .timeline:active {
            cursor: grabbing;
        }
        
        .timeline-axis {
            position: absolute;
            height: 3px;
            background-color: var(--border-color);
            width: 5000px;
            bottom: 40px;
            left: 0;
            transform-origin: left center;
        }
        
        .time-marker {
            position: absolute;
            bottom: 10px;
            transform: translateX(-50%);
            font-size: 0.7rem;
            color: #666;
            white-space: nowrap;
        }
        
        .time-marker::before {
            content: '';
            position: absolute;
            top: -10px;
            left: 50%;
            width: 1px;
            height: 7px;
            background-color: #999;
        }
        
        .events-container {
            position: absolute;
            height: calc(100% - 60px);
            width: 5000px;
            top: 0;
            left: 0;
            transform-origin: left top;
        }
        
        .event {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s ease-out;
            z-index: 5;
            display: flex;
            align-items: center;
        }

        .event .event-title-preview {
            display: none; /* 默认隐藏 */
            position: absolute;
            left: 15px;
            font-size: 11px;
            white-space: nowrap;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 2px 5px;
            border-radius: 3px;
            backdrop-filter: blur(2px);
            pointer-events: none;
        }
        
        .event:hover {
            transform: scale(1.5);
            z-index: 15;
        }
        
        .event[data-category="cosmic"] { background-color: var(--cosmic-color); }
        .event[data-category="evolution"] { background-color: var(--evolution-color); }
        .event[data-category="culture"] { background-color: var(--culture-color); }
        .event[data-category="empire"] { background-color: var(--empire-color); }
        .event[data-category="science"] { background-color: var(--science-color); }
        .event[data-category="industrial"] { background-color: var(--industrial-color); }
        .event[data-category="tech"] { background-color: var(--tech-color); }
        .event[data-category="sustainability"] { background-color: var(--sustainability-color); }
        
        .event-details {
            position: absolute;
            width: 280px;
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 12px;
            display: none;
            z-index: 20;
            font-size: 0.8rem;
            line-height: 1.4;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
        }

        .event-details::before { /* 小箭头 */
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #fff;
            border: 1px solid var(--border-color);
            border-top: none;
            border-right: none;
            transform: rotate(45deg);
        }
        
        .event-details h3 {
            margin-top: 0;
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            font-size: 1rem;
        }
        
        .event-details .date {
            font-weight: 600;
            margin-bottom: 8px;
            color: #666;
            font-size: 0.75rem;
        }
        
        .controls-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }
        
        .zoom-controls {
            display: flex;
            gap: 5px;
        }
        
        .zoom-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 6px;
            border: 1px solid #ddd;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            font-weight: bold;
            font-size: 1rem;
        }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 0.75rem;
            margin-right: 5px;
        }
        
        .legend-color {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 4px;
            flex-shrink: 0;
        }
        
        .legend-color[data-category="cosmic"] { background-color: var(--cosmic-color); }
        .legend-color[data-category="evolution"] { background-color: var(--evolution-color); }
        .legend-color[data-category="culture"] { background-color: var(--culture-color); }
        .legend-color[data-category="empire"] { background-color: var(--empire-color); }
        .legend-color[data-category="science"] { background-color: var(--science-color); }
        .legend-color[data-category="industrial"] { background-color: var(--industrial-color); }
        .legend-color[data-category="tech"] { background-color: var(--tech-color); }
        .legend-color[data-category="sustainability"] { background-color: var(--sustainability-color); }
        
        .timeline-instructions {
            font-size: 0.75rem;
            color: #666;
            text-align: center;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .controls-wrapper {
                flex-direction: column;
                align-items: stretch;
            }
            .scale-control {
                justify-content: flex-start;
            }
            
            .event-details {
                width: 250px;
                max-height: 250px;
            }
            
            .timeline-viewport {
                height: 350px;
            }
            
            .timeline {
                height: 350px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>人类状况的时间线</h1>
            <p>探索从宇宙起源到现代的进化和历史里程碑</p>
        </header>
        
        <div class="controls-wrapper">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="搜索事件、年份或关键词...">
            </div>
            
            <div class="scale-control" id="scaleControl">
                <button class="scale-btn active" data-scale="all">全部历史</button>
                <button class="scale-btn" data-scale="cosmic">宇宙学</button>
                <button class="scale-btn" data-scale="early">早期生命</button>
                <button class="scale-btn" data-scale="human">人类进化</button>
                <button class="scale-btn" data-scale="civilization">文明</button>
                <button class="scale-btn" data-scale="modern">现代</button>
                <button class="scale-btn" data-scale="recent">近代</button>
            </div>
        </div>
        
        <div class="category-filters">
            <button class="category-filter active" data-category="cosmic">宇宙学前提</button>
            <button class="category-filter active" data-category="evolution">人类祖先与进化</button>
            <button class="category-filter active" data-category="culture">文化发展</button>
            <button class="category-filter active" data-category="empire">帝国与征服</button>
            <button class="category-filter active" data-category="science">科学革命</button>
            <button class="category-filter active" data-category="industrial">工业革命</button>
            <button class="category-filter active" data-category="tech">技术革命</button>
            <button class="category-filter active" data-category="sustainability">可持续性革命</button>
        </div>
        
        <div class="timeline-container">
            <div class="timeline-viewport">
                <div class="timeline" id="timeline">
                    <div class="timeline-axis" id="timelineAxis">
                        <!-- 时间标记将通过JavaScript动态添加 -->
                    </div>
                    
                    <div class="events-container" id="eventsContainer">
                        <!-- 事件将通过JavaScript动态添加 -->
                    </div>
                </div>
            </div>
            
            <div class="controls-panel">
                <div class="timeline-instructions">
                    拖动时间线查看更多事件，点击事件点查看详情
                </div>
                <div class="zoom-controls">
                    <div class="zoom-btn" id="zoomOut">-</div>
                    <div class="zoom-btn" id="zoomIn">+</div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" data-category="cosmic"></div>
                <span>宇宙学前提</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="evolution"></div>
                <span>人类祖先与进化</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="culture"></div>
                <span>文化发展</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="empire"></div>
                <span>帝国与征服</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="science"></div>
                <span>科学革命</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="industrial"></div>
                <span>工业革命</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="tech"></div>
                <span>技术革命</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" data-category="sustainability"></div>
                <span>可持续性革命</span>
            </div>
        </div>
    </div>

    <script>
        // 定义事件数据     
        const events = [
            // 宇宙学前提
            { year: -13800000000, category: 'cosmic', title: '宇宙大爆炸', description: '宇宙大爆炸奇点：宇宙膨胀，物质和反物质粒子产生，物理定律形成；空间膨胀和冷却，第一批元素形成 → 可观测宇宙的形成，包括星系、太阳系、恒星、行星、卫星、小行星和彗星' },
            { year: -13550000000, category: 'cosmic', title: '氢星点燃', description: '氢星点燃，使宇宙沐浴在宇宙黎明的曙光中 → 恒星中的氦融合成碳，导致所有元素的恒星核合成；早期星系和黑洞在大爆炸后2.9亿年和4亿年形成' },
            { year: -13000000000, category: 'cosmic', title: '银河系形成', description: '恒星聚集成银河系：现在是一个扭曲的螺旋盘，拥有1000亿颗恒星，是可观测宇宙中2000亿个星系之一' },
            { year: -12200000000, category: 'cosmic', title: '最早的水', description: '最早的水：星际水蒸气，氧气的储存库' },
            { year: -4570000000, category: 'cosmic', title: '太阳系形成', description: '太阳和太阳系在银河系内形成，围绕银河系中心的超大质量黑洞每2.2亿年公转一次' },
            { year: -4510000000, category: 'cosmic', title: '月球形成', description: '月球形成于原行星忒伊亚与原地球的巨大碰撞，发生在巨行星混沌不稳定期间' },
            { year: -4500000000, category: 'cosmic', title: '地球形成', description: '地球形成，表面积5.1亿平方公里，以年为周期绕太阳公转，以昼夜为周期绕倾斜轴向东自转，使极地季节交替' },
            { year: -4400000000, category: 'cosmic', title: '地球海洋和大气形成', description: '地球海洋和湿润大气形成，由地球铁核产生的磁场保护，抵御太阳风和宇宙射线' },
            { year: -4400000000, category: 'cosmic', title: '地壳俯冲开始', description: '地球地壳俯冲开始 → 至少在35亿年前就有大陆板块构造，这在太阳系中是独一无二的；板块边界不断移动' },
            { year: -4300000000, category: 'cosmic', title: 'RNA形成', description: '玄武岩玻璃催化RNA形成（冥古宙）：长链分子携带信息，通过自我复制的世代并合成肽 → 生物前的RNA世界？' },
            // 人类祖先与进化
            { year: -4000000000, category: 'evolution', title: '地球上最早的生命', description: '地球上最早的生命：单细胞原核生物古菌（冥古宙，3.7-4.2亿年前），每个细胞都是一个可遗传基因的囊泡，由稳定的DNA组成，指导核糖体中RNA翻译成蛋白质，催化代谢反应，驱动繁殖 → 地球是宇宙中唯一有生命的行星吗？' },
            { year: -3500000000, category: 'evolution', title: '光合细菌', description: '光合细菌出现在古菌中（太古宙），将阳光转化为化学能以驱动细胞活动 → 蓝藻成为地球上曾经存在过的最丰富的生物体' },
            { year: -3400000000, category: 'evolution', title: '最早的大气氧', description: '最早的大气氧，以低浓度存在（太古宙）→ 好氧和厌氧微生物演化出驱动地球生物地球化学元素循环的代谢途径，这些元素包括H、C、N、O、S，对地球上所有生命至关重要' },
            { year: -3260000000, category: 'evolution', title: '巨大的陨石撞击地球', description: '一颗巨大的陨石撞击地球（太古宙），引发全球海啸，使世界海洋沸腾，降低海平面，使天空变暗，搅动营养物质供铁循环微生物使用' },
            { year: -3200000000, category: 'evolution', title: '地球第一块大陆', description: '地球第一块大陆从海洋中出现（太古宙，3.3至3.2亿年前），支持地球第一个陆地生态系统中的微生物席' },
            { year: -2330000000, category: 'evolution', title: '大氧化事件', description: '大氧化事件：约100万年的大气氧气快速积累（元古宙），是光合作用的产物，为复杂生命提供能量' },
            { year: -2100000000, category: 'evolution', title: '早期多细胞生命', description: '早期多细胞生命，具有细胞间信号传递和协调反应（元古宙）→ 人体包含28-36万亿个细胞，分布在数百种相互依赖的类型中，外加数量相似的共生微生物，没有它们我们就会死亡' },
            { year: -1640000000, category: 'evolution', title: '最早的真核生物', description: '最早的真核生物出现在原核生物中（元古宙），起源于古菌与细菌的合并：单细胞和多细胞；有减数分裂和父母双方遗传物质重组的有性生殖' },
            { year: -1500000000, category: 'evolution', title: '巨型病毒', description: '巨型病毒与红藻相关（元古宙）：病毒从细胞生命诞生之初就存在？' },
            { year: -1000000000, category: 'evolution', title: '最早的真菌', description: '最早的真菌出现在真核生物中（元古宙）：浅水河口Ourasphaira giraldae' },
            { year: -890000000, category: 'evolution', title: '最早的后生动物', description: '最早的后生动物（动物）出现在真核生物中：有神经元和肌肉的栉水母，但没有自我意识，从真菌谱系中分离出来（元古宙？）' },
            { year: -717000000, category: 'evolution', title: '雪球地球冰期', description: '大规模火山活动和气体排放引发持续5600万年的雪球地球冰期：地球历史上最极端的冰室气候间隔 → 之后短暂的雪球事件' },
            { year: -700000000, category: 'evolution', title: '新元古代氧化事件', description: '新元古代氧化事件：1亿年的光合作用增加，随着地球自转速度减缓，白天延长，改善了复杂生命的条件' },
            { year: -635000000, category: 'evolution', title: '最早的茎部刺胞动物', description: '最早的茎部刺胞动物出现在动物中（早期埃迪卡拉纪），水母和不死水螅的祖先：神经系统和睡眠/觉醒周期 → 没有睡眠我们就会死亡' },
            { year: -550000000, category: 'evolution', title: '最早的双侧对称动物', description: '最早的双侧对称动物，具有左右对称性（埃迪卡拉纪）：有嘴和肠的穴居Ikaria；有成对腿和肌肉的分段Yilingia；蛔虫Uncus，现在数量最多的动物群的最早祖先' },
            { year: -540000000, category: 'evolution', title: '动物多样化爆发', description: '动物多样化在2000万年内爆发（早寒武纪）；现代身体计划的出现，在4000万年内解析为门' },
            { year: -535000000, category: 'evolution', title: '最早的脊索动物', description: '最早的脊索动物出现在双侧动物中（早寒武纪）：咽鳃裂、脊索和大脑 → 人类大脑独特的认知能力' },
            { year: -520000000, category: 'evolution', title: '祖先脊椎动物', description: '祖先脊椎动物出现在脊索动物中（寒武纪），有胃、鳃弓、相机型眼睛和嗅觉器官' },
            { year: -520000000, category: 'evolution', title: '最早的敏锐视觉感知', description: '最早的敏锐视觉感知：干节肢动物的复眼和有柄眼（寒武纪）→ 视觉催化动物多样化,' },
            { year: -500000000, category: 'evolution', title: '植物首次在陆地上定居', description: '植物首次在陆地上定居：中寒武纪的绿藻，可能由真菌促进 → 大陆绿化，创造了土壤，地球上生物多样性最高的栖息地，并使河流蜿蜒' },
            { year: -500000000, category: 'evolution', title: '动物最早进入陆地', description: '动物最早进入陆地（中寒武纪）：潮汐沙滩上的蜗牛，比包括千足虫在内的无脊椎动物完全陆地化早 1 亿年' },
            { year: -480000000, category: 'evolution', title: '脊椎动物的辐射', description: '脊椎动物的辐射（奥陶纪早期），可能由基因组复制促进：水生，具有矿化的骨骼、甲壳和鳞片' },
            { year: -479000000, category: 'evolution', title: '最早的昆虫', description: '节肢动物中最早的昆虫（奥陶纪早期）→ 在 4.06 亿年前飞行；现代昆虫表现出主观体验的能力，这是意识的标志' },
            { year: -460000000, category: 'evolution', title: '最早的具有活骨的脊椎动物骨骼', description: '最早的具有活骨的脊椎动物骨骼：*Astraspis* 无颌鱼（奥陶纪）→ 自我修复的骨骼,' },
            { year: -445000000, category: 'evolution', title: '大规模灭绝', description: '在 100 万年内发生两次大规模灭绝，消灭了超过四分之三的物种（奥陶纪晚期），与火山活动有关' },
            { year: -430000000, category: 'evolution', title: '鱼类中成对鼻孔的进化', description: '鱼类中成对鼻孔的进化（志留纪），这是颌骨发育的先决条件 → 人类鼻子可以检测到超过 1 万亿种气味，并根据相关的记忆进行分类' },
            { year: -420000000, category: 'evolution', title: '最早的有颌脊椎动物', description: '鱼类中最早的有颌脊椎动物（志留纪晚期）→ 摄食生态位的多样化；打哈欠和同理心的能力，在不同的现代谱系中普遍存在；滑膜关节' },
            { year: -407000000, category: 'evolution', title: '最早的声音交流', description: '最早的声音交流，由水生脊椎动物（泥盆纪早期）：用于发信号、展示和监视的声音产生和听觉' },
            { year: -407000000, category: 'evolution', title: '维管植物最早的木质茎', description: '维管植物最早的木质茎（泥盆纪早期，法国，冈瓦纳大陆）→ 由水力限制驱动的进化，使植物预先适应更高的形态' },
            { year: -395000000, category: 'evolution', title: '最早的四足动物', description: '脊椎动物中最早的四足动物（泥盆纪，波兰，欧亚浅海泻湖）：四肢取代成对的鳍；仍然完全水生' },
            { year: -390000000, category: 'evolution', title: '最早的森林', description: '最早的森林（泥盆纪，英国，欧亚大陆）→ 三维陆地栖息地，树木通过菌根真菌连接；大气中 O₂ 含量上升，CO₂ 含量下降' },
            { year: -380000000, category: 'evolution', title: '最早的脊椎动物多腔心脏', description: '最早的脊椎动物多腔心脏（盾皮鱼，泥盆纪，澳大利亚，冈瓦纳近岸礁）→ 人类心跳是一种健康的、无情或由衷的节奏，会被情感创伤打破' },
            { year: -375000000, category: 'evolution', title: '一系列大规模灭绝', description: '在 2000 万年内发生一系列大规模灭绝，消灭了超过三分之二的物种（泥盆纪晚期），与气候变冷有关' },
            { year: -350000000, category: 'evolution', title: '最早的陆地脊椎动物', description: '最早的陆地脊椎动物（石炭纪早期，英国，欧亚大陆）：半水生两栖四足动物，有眨眼的眼睛和舌头 → 人类舌头可以检测到甜、酸、咸、苦和鲜味' },
            { year: -340000000, category: 'evolution', title: '最早的完全陆地四足脊椎动物', description: '最早的完全陆地四足脊椎动物，产羊膜卵（石炭纪，英国，欧亚大陆）' },
            { year: -305000000, category: 'evolution', title: '四足动物照顾后代', description: '四足动物照顾后代的最早证据：一种蜥螈类合弓动物（石炭纪，新斯科舍省，盘古大陆）' },
            { year: -289000000, category: 'evolution', title: '最早的皮肤记录', description: '最早的皮肤记录（爬行动物 *Captorhinus aguti*，二叠纪，北美洲，盘古大陆），现存有肢脊椎动物中最大的器官；保护、防护和隔离身体，感知纹理和温度' },
            { year: -251900000, category: 'evolution', title: '地球上最大的大规模灭绝', description: '地球上最大的大规模灭绝，在 61,000 年内消灭了五分之四的物种（二叠纪-三叠纪过渡期），由西伯利亚火山陷阱释放的热和酸化 CO₂ 排放引起，并被特大厄尔尼诺现象放大 → 快速的生物恢复' },
            { year: -233000000, category: 'evolution', title: '现代世界的曙光', description: '现代世界的曙光：与火山活动相关的重大生物更替（三叠纪晚期）→ 针叶树、昆虫、恐龙、爬行动物和干哺乳动物的快速多样化和起源' },
            { year: -210000000, category: 'evolution', title: '最早的温血干哺乳动物', description: '最早的温血干哺乳动物（三叠纪晚期）：更快的新陈代谢在较
        ];

        // 定义时间标记
        const timeMarkers = [
            { year: -13800000000, label: "138亿年前" },
            { year: -4500000000, label: "45亿年前" },
            { year: -1000000000, label: "10亿年前" },
            { year: -100000000, label: "1亿年前" },
            { year: -10000000, label: "1000万年前" },
            { year: -1000000, label: "100万年前" },
            { year: -100000, label: "10万年前" },
            { year: -10000, label: "1万年前" },
            { year: -1000, label: "1000年前" },
            { year: 0, label: "公元元年" },
            { year: 1000, label: "公元1000年" },
            { year: 1500, label: "公元1500年" },
            { year: 1800, label: "公元1800年" },
            { year: 1900, label: "公元1900年" },
            { year: 2000, label: "公元2000年" },
            { year: 2025, label: "现在" }
        ];

 // 功能：将年份格式化为显示友好的格式
    function formatYear(year) {
        if (year < 0) {
            // BCE格式
            if (year <= -1000000000) { // 10亿及以上
                return Math.abs(year / 1000000000).toFixed(0) + "亿年前";
            } else if (year <= -1000000) { // 100万及以上
                return Math.abs(year / 1000000).toFixed(0) + "百万年前";
            } else if (year <= -1000) {
                return Math.abs(year / 1000).toFixed(0) + "千年前";
            } else {
                return "公元前" + Math.abs(year) + "年";
            }
        } else {
            // CE格式
            return "公元" + year + "年";
        }
    }

    // 非线性时间映射函数,修改了
   function mapYearToPosition(year, timelineWidth) {
        const effectiveWidth = timelineWidth || 5000;
        const minYear = -13800000000; // 138亿年前
        const maxYear = 2025; // 现在

        // 使用对称对数 (Symmetric Log) 变换，以更好地处理跨越0的巨大范围
        // symlog(x) = sign(x) * log(abs(x) + 1)
        const symlog = (val) => Math.sign(val) * Math.log(Math.abs(val) + 1);

        const symlogMin = symlog(minYear);
        const symlogMax = symlog(maxYear);
        const symlogYear = symlog(year);

        const symlogRange = symlogMax - symlogMin;

        let position = ((symlogYear - symlogMin) / symlogRange) * effectiveWidth;

        return Math.max(0, Math.min(position, effectiveWidth));
    }
        // 设置时间标记
        function setupTimeMarkers() {
            const axis = document.getElementById('timelineAxis');
            axis.innerHTML = '';
            const timelineWidth = parseFloat(document.getElementById('eventsContainer').style.width) || 5000;
            timeMarkers.forEach(marker => {
                const position = mapYearToPosition(marker.year, timelineWidth);
                const markerEl = document.createElement('div');
                markerEl.className = 'time-marker';
                markerEl.textContent = marker.label;
                markerEl.style.left = `${position}px`;
                axis.appendChild(markerEl);
            });
        }

 // 设置事件点,使用了事件委托
        function renderEvents() {
            const container = document.getElementById('eventsContainer');
            container.innerHTML = '';  // Clear previous events
            const timelineWidth = parseFloat(container.style.width) || 5000;

            const activeCategories = Array.from(document.querySelectorAll('.category-filter.active'))
                .map(el => el.getAttribute('data-category'));
            const searchQuery = document.getElementById('searchInput').value.toLowerCase();

            // 使用 Map 来按年份对事件分组
            const eventsByYear = new Map();
            events.forEach(event => {
                if (!activeCategories.includes(event.category)) return;  // 过滤类别

                // 搜索过滤
                if (searchQuery &&
                    !event.title.toLowerCase().includes(searchQuery) &&
                    !event.description.toLowerCase().includes(searchQuery) &&
                    !formatYear(event.year).toLowerCase().includes(searchQuery)) {
                    return;
                }

                if (!eventsByYear.has(event.year)) {
                    eventsByYear.set(event.year, []);
                }
                eventsByYear.get(event.year).push(event);
            });

            // 遍历每个年份的事件组
            eventsByYear.forEach((eventsInYear, year) => {
                const xPosition = mapYearToPosition(year, timelineWidth);

                eventsInYear.forEach((event, index) => {
                    const categoryOrder = {
                        'cosmic': 0, 'evolution': 1, 'culture': 2, 'empire': 3,
                        'science': 4, 'industrial': 5, 'tech': 6, 'sustainability': 7
                    };
                    const categoryIndex = categoryOrder[event.category] || 0;
                    const categoryOffset = categoryIndex * 40; // 垂直间距
                    const eventSpacing = 15; // 同一年份事件之间的间距
                    const yPosition = 30 + categoryOffset + (index * eventSpacing);

                    const eventEl = document.createElement('div');
                    eventEl.className = 'event';
                    eventEl.setAttribute('data-category', event.category);
                    eventEl.setAttribute('data-title', event.title);
                    eventEl.setAttribute('data-year', event.year);
                    eventEl.setAttribute('data-description', event.description);
                    eventEl.style.left = `${xPosition}px`;
                    eventEl.style.top = `${yPosition}px`;

                    const detailsEl = document.createElement('div');
                    detailsEl.className = 'event-details';
                    detailsEl.innerHTML = `
                        <h3>${event.title}</h3>
                        <div class="date">${formatYear(event.year)}</div>
                        <p>${event.description}</p>
                    `;
                    
                    const titlePreview = document.createElement('span');
                    titlePreview.className = 'event-title-preview';
                    titlePreview.textContent = event.title;

                    eventEl.appendChild(titlePreview);
                    eventEl.appendChild(detailsEl);
                    container.appendChild(eventEl);
                });
            });
            updateEventVisibility();
        }

        function positionDetailsPopup(detailsEl, eventEl) {
            const timelineViewport = document.getElementById('timeline-viewport');
            const viewportRect = timelineViewport.getBoundingClientRect();
            const eventRect = eventEl.getBoundingClientRect();

            // 为了正确获取尺寸，先让它可见但“隐身”
            detailsEl.style.visibility = 'hidden';
            detailsEl.style.display = 'block';
            const detailsRect = detailsEl.getBoundingClientRect(); // 现在可以获取到真实尺寸

            // 默认在右侧
            let top = eventRect.top - viewportRect.top - (detailsRect.height / 2) + (eventRect.height / 2);
            let left = eventRect.right - viewportRect.left + 15;
            
            // 检查是否超出右边界
            if (left + detailsRect.width > viewportRect.width - 10) { // 增加一点边距
                left = eventRect.left - viewportRect.left - detailsRect.width - 15;
            }

            // 检查是否超出上/下边界并调整
            if (top < 0) {
                top = 5;
            } else if (top + detailsRect.height > viewportRect.height - 5) {
                top = viewportRect.height - detailsRect.height - 5;
            }

            detailsEl.style.left = `${left}px`;
            detailsEl.style.top = `${top}px`;

            // 在正确的位置上让它真正可见
            detailsEl.style.visibility = 'visible';
        }

        // 初始化拖动和缩放功能
        function initDragAndZoom() {
            const timeline = document.getElementById('timeline');
            const eventsContainer = document.getElementById('eventsContainer');
            const timelineAxis = document.getElementById('timelineAxis');

            let isDragging = false;
            let startX;
            let scrollLeft;
            let scale = 1.0;
            const minScale = 1.0;
            const maxScale = 50.0;
            
            // 鼠标事件
            timeline.addEventListener('mousedown', (e) => {
                isDragging = true;
                timeline.style.cursor = 'grabbing';
                startX = e.pageX;
                scrollLeft = timeline.scrollLeft;
                e.preventDefault();
            });
            
            window.addEventListener('mousemove', (e) => {
                if (!isDragging) return;
                const x = e.pageX;
                const walk = (x - startX) * 2; // 滚动速度增加
                requestAnimationFrame(() => {
                    timeline.scrollLeft = scrollLeft - walk;
                });
            });
            
            window.addEventListener('mouseup', () => {
                isDragging = false;
                timeline.style.cursor = 'grab';
            });
            
            // 触摸事件
            timeline.addEventListener('touchstart', (e) => {
                isDragging = true;
                startX = e.touches[0].pageX;
                scrollLeft = timeline.scrollLeft;
            }, { passive: true });
            
            timeline.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                const x = e.touches[0].pageX - timeline.offsetLeft;
                const walk = (x - startX) * 2; // 滚动速度增加
                requestAnimationFrame(() => {
                    timeline.scrollLeft = scrollLeft - walk;
                });
            }, { passive: true });
            
            timeline.addEventListener('touchend', () => {
                isDragging = false;
            });
            
            // 缩放功能
            const zoomIn = document.getElementById('zoomIn');
            const zoomOut = document.getElementById('zoomOut');
            
            zoomIn.addEventListener('click', () => {
                const eventsContainer = document.getElementById('eventsContainer');
                const currentWidth = parseInt(eventsContainer.style.width || '5000');
                const newWidth = currentWidth * 1.2;
                eventsContainer.style.width = `${newWidth}px`;
                timelineAxis.style.width = `${newWidth}px`;
                setupTimeMarkers();
                renderEvents();
            });
            
            zoomOut.addEventListener('click', () => {
                const eventsContainer = document.getElementById('eventsContainer');
                const currentWidth = parseInt(eventsContainer.style.width || '5000');
                const newWidth = Math.max(1000, currentWidth / 1.2);
                eventsContainer.style.width = `${newWidth}px`;
                timelineAxis.style.width = `${newWidth}px`;
                setupTimeMarkers();
                renderEvents();
            });
        }

        function updateEventVisibility() {
            const timelineWidth = parseFloat(document.getElementById('eventsContainer').style.width) || 5000;
            const events = document.querySelectorAll('.event');
            // 当时间线宽度大于15000px时显示标题预览
            const showPreview = timelineWidth > 15000; 

            events.forEach(eventEl => {
                const preview = eventEl.querySelector('.event-title-preview');
                if (preview) {
                    preview.style.display = showPreview ? 'block' : 'none';
                }
            });
        }

 // 初始化点击事件监听器
        function initEventHandlers() {
            const container = document.getElementById('eventsContainer');
            const timeline = document.getElementById('timeline');

            // --- 改为鼠标悬停显示详情 ---
            let activeDetails = null;

            container.addEventListener('mouseover', (e) => {
                const eventEl = e.target.closest('.event');
                if (eventEl) {
                    // 隐藏其他可能可见的弹窗
                    if (activeDetails && activeDetails.parentElement !== eventEl) {
                        activeDetails.style.display = 'none';
                        activeDetails = null;
                    }
                    
                    const detailsEl = eventEl.querySelector('.event-details');
                    if (detailsEl && detailsEl.style.display !== 'block') {
                        positionDetailsPopup(detailsEl, eventEl);
                        activeDetails = detailsEl;
                    }
                }
            });

            container.addEventListener('mouseout', (e) => {
                const eventEl = e.target.closest('.event');
                if (eventEl && !eventEl.contains(e.relatedTarget)) {
                    const detailsEl = eventEl.querySelector('.event-details');
                    if (detailsEl) {
                        detailsEl.style.display = 'none';
                    }
                }
            });

            // 类别过滤器
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.addEventListener('click', () => {
                    btn.classList.toggle('active');
                    renderEvents();
                });
            });
            
            // 时间尺度按钮
            document.getElementById('scaleControl').addEventListener('click', (e) => {
                if (!e.target.classList.contains('scale-btn')) return;
                const btn = e.target;

                document.querySelectorAll('.scale-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const scale = btn.getAttribute('data-scale');
                const timelineWidth = parseFloat(document.getElementById('eventsContainer').style.width) || 5000;

                const scaleTargets = {
                    'all': { year: -6000000000, width: 5000 },
                    'cosmic': { year: -13000000000, width: 8000 },
                    'early': { year: -2000000000, width: 12000 },
                    'human': { year: -2000000, width: 20000 },
                    'civilization': { year: -3000, width: 30000 },
                    'modern': { year: 1800, width: 40000 },
                    'recent': { year: 1990, width: 50000 }
                };

                const target = scaleTargets[scale];
                if (target) {
                    document.getElementById('eventsContainer').style.width = `${target.width}px`;
                    document.getElementById('timelineAxis').style.width = `${target.width}px`;
                    setupTimeMarkers();
                    renderEvents();

                    const targetX = mapYearToPosition(target.year, target.width);
                    const viewportWidth = timeline.offsetWidth;
                    timeline.scrollTo({
                        left: targetX - (viewportWidth / 2),
                        behavior: 'smooth'
                    });
                }
            });
            
            // 搜索框
            document.getElementById('searchInput').addEventListener('input', renderEvents);
            
            // 点击其他区域关闭详情
            timeline.addEventListener('click', (e) => {
                if (e.target.id === 'timeline' || e.target.id === 'eventsContainer' || e.target.id === 'timelineAxis') {
                    document.querySelectorAll('.event-details').forEach(detail => {
                        detail.style.display = 'none';
                    });
                }
            });
        }

        // 初始化页面
       window.addEventListener('DOMContentLoaded', () => {
            setupTimeMarkers();
            renderEvents();
            initDragAndZoom();
            initEventHandlers();

            // 初始滚动到近代
            setTimeout(() => {
                document.querySelector('.scale-btn[data-scale="recent"]').click();
            }, 100);
        });
    </script>
</body>
</html>
