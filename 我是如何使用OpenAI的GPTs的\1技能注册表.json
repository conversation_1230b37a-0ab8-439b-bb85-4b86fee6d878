[{"skill_id": "prompt_5_stage_builder", "name": "五轮引导构建器", "type": "prompt", "owner": "GPT构建引导助手", "description": "引导用户分五步清晰构建一个GPT智能体", "gpts_available": ["gpt构建引导助手"], "reusable": true, "input_requirements": "用户初始目标意图", "output_schema": ["gpt_task_goal", "persona_archetype", "knowledge_needed", "gpt_abilities", "output_format"], "notes": "强依赖结构化引导和样例展示"}, {"skill_id": "risk_labeling_checker", "name": "风险标注助手", "type": "action", "owner": "通用分析GPT", "description": "对文本中潜在风险进行评级和分类", "gpts_available": ["报告助手", "合同审校GPT"], "reusable": true, "action_api": "/openapi/risk_labeling.json", "dependencies": null, "notes": "建议与“摘要”动作联用提升效果"}, {"skill_id": "zhangwu_analyzer", "name": "张五常制度分析引擎", "type": "knowledge + prompt", "owner": "张五常GPT", "description": "使用张五常的产权与交易费用理论分析现实制度", "gpts_available": ["张五常GPT", "制度比较GPT"], "reusable": true, "knowledge_source": "/vector-db/zhangwu-index", "output_form": "markdown结构输出 + 制度对照逻辑", "notes": "高适配制度分析类任务，可嵌入分析工作流中"}]