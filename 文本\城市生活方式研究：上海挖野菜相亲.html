<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市生活方式研究：上海挖野菜相亲</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        html, body { /* 确保 html 和 body 高度为 100% */
             height: 100%;
             overflow: hidden; /* 防止 body 滚动 */
        }

        body {
            background-color: #EFEAD8;
            color: #2E4057;
        }

        /* 页面容器 */
        #book-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            overflow: hidden; /* 隐藏非活动页面 */
        }

        /* 页面样式 */
        .page {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            display: flex; /* 使用 flex 布局 */
            flex-direction: column;
            padding: 40px; /* 标准内边距 */
            background-color: #EFEAD8;
            opacity: 0; /* 初始隐藏 */
            visibility: hidden; /* 初始不可见 */
            transition: opacity 0.5s ease, visibility 0.5s ease; /* 添加过渡效果 */
            overflow: hidden; /* <--- 修改: 防止页面本身滚动 */
            box-sizing: border-box; /* 确保 padding 包含在内 */
        }

        /* 为需要页脚的页面增加底部内边距，给绝对定位的页脚留出空间 */
        .page:not(#page-1):not(#page-12) {
             padding-bottom: 80px; /* <--- 新增 */
        }


        .page.active {
             opacity: 1; /* 活动页面可见 */
             visibility: visible;
             z-index: 1; /* 确保活动页面在上方 */
        }

        /* 页面内容样式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-shrink: 0; /* 防止页头被压缩 */
        }

        .page-number {
            font-size: 18px;
            color: #90B77D;
            font-weight: bold;
        }

        .page-title {
            font-size: 18px;
            color: #2E4057;
            text-align: right;
        }

        .page-content {
            flex: 1; /* 占据剩余空间 */
            display: flex;
            flex-direction: column;
            /* padding-bottom: 20px; */ /* <--- 移除或调整，因为外部 page 已有 padding-bottom */
            min-height: 0; /* 确保在flex容器中可以收缩 */
            overflow-y: auto; /* <--- 修改: 内容区负责滚动 */
            -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 for content */
            margin-right: -18px; /* 尝试隐藏滚动条视觉效果 (可能需要调整) */
            padding-right: 18px; /* 补偿滚动条宽度 */
        }

        .page-subtitle {
            font-size: 28px;
            color: #D95D39;
            margin-bottom: 30px;
            font-weight: normal;
        }
      
        .content-columns {
            display: flex;
            flex: 1; /* 使其填充 page-content 的剩余空间 */
            gap: 30px;
            min-height: 0; /* 防止在flex容器中无限增长 */
            align-items: flex-start; /* <--- 添加在这里 */
        }

        .column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
             min-height: 0; /* 添加 min-height 尝试解决flex子项高度问题 */
             min-width: 0; /* 添加 min-width 解决 flex 压缩问题 */
        }

        .info-box {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            /* flex-shrink: 0; */ /* <--- 移除尝试 */
        }

        .info-box h3 {
            color: #2E4057;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .info-box p {
            line-height: 1.6;
            margin-bottom: 10px;
        }
         .info-box ul {
             margin: 10px 0 10px 20px;
             padding-left: 1.2em;
             list-style: disc;
         }
         .info-box ul li {
             margin-bottom: 5px;
             line-height: 1.6;
         }
         .info-box blockquote {
            margin: 10px 0;
            padding: 10px 15px;
            border-left: 4px solid #ccc;
            background-color: rgba(0,0,0,0.02);
            color: #555;
         }
         .info-box blockquote p {
             margin-bottom: 5px;
         }
         .info-box blockquote footer {
            font-size: 13px;
            font-style: italic;
            color: #777;
            text-align: right; /* 确保引用来源靠右 */
         }

        /* 图表样式 */
        .chart {
            background-color: white;
            border-radius: 10px;
            overflow: hidden; /* 保持内部裁剪 */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            min-height: 0; /* <--- 添加 */
            /* flex-shrink: 0; */ /* <--- 移除尝试 */
        }

        .chart-title {
            background-color: #90B77D;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
            flex-shrink: 0;
        }
        .chart > div:not(.chart-title) { /* 图表内容区域 */
           flex-grow: 1;
           /* overflow: visible; */ /* <--- 改回 hidden 或 auto 试试 */
           overflow: auto; /* 让图表内部自己处理溢出 */
           min-height: 0; /* <--- 添加 */
        }
        /* 修正柱状图显示 */
         .chart div[style*="display: flex"][style*="justify-content: space-around"] {
             align-items: flex-end !important;
             padding: 20px !important;
         }
         .chart div[style*="display: flex"][style*="flex-direction: column"] {
             text-align: center;
         }


        /* 导航按钮 */
        .nav-buttons {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 100;
        }
        .nav-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #90B77D;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            border: none;
            font-size: 24px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: transform 0.2s ease, background-color 0.2s ease;
        }
        .nav-button:hover {
            transform: scale(1.1);
            background-color: #D95D39;
        }
        .nav-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 页面指示器 */
        #page-indicator {
            position: fixed;
            bottom: 40px;
            left: 30px;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            color: #2E4057;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 封面页特殊样式 */
        #page-1 { /* ID 应用于 .page 元素 */
            background: linear-gradient(135deg, #EFEAD8, #90B77D);
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px; /* 保持内边距 */
            overflow-y: auto; /* 封面允许自己滚动 */
        }
        .cover-title {
            font-size: 40px;
            color: #2E4057;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .cover-subtitle {
            font-size: 24px;
            color: #D95D39;
            margin-bottom: 50px;
        }
        .cover-icon {
            width: 150px;
            height: 150px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 50px auto;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        .cover-icon span {
            font-size: 80px;
            color: #90B77D;
        }
        .cover-footer {
            font-size: 16px;
            color: #2E4057;
            margin-top: 50px;
        }

        /* 进度流程图 */
        .process-flow {
            display: flex;
            flex-direction: column;
            gap: 10px; /* 减小 gap 试试 */
        }
        .process-step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        .step-number {
            width: 35px; /* 减小尺寸 */
            height: 35px;
            border-radius: 50%;
            background-color: #90B77D;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            flex-shrink: 0;
            margin-top: 5px;
        }
        .step-content {
            flex: 1;
            background-color: white;
            padding: 12px; /* 减小内边距 */
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .step-content p {
            font-size: 14px;
            line-height: 1.5;
            margin: 0; /* 移除默认 p 边距 */
        }
         .step-content p + p { /* 段落间距 */
             margin-top: 5px;
         }
        .step-content strong {
             display: block;
             margin-bottom: 5px;
             font-size: 15px; /* 减小标题字号 */
        }
        .process-arrow {
            width: 2px;
            height: 15px; /* 减小箭头高度 */
            background-color: #90B77D;
            margin: 0 0 0 16.5px; /* 调整位置 */
        }
        .process-flow > .process-step:last-child + .process-arrow,
        .process-flow > .process-arrow:last-child {
             display: none;
        }

        /* 比较表格 */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .comparison-table th, .comparison-table td {
            padding: 10px 12px; /* 调整内边距 */
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            line-height: 1.5;
            vertical-align: top;
        }
        .comparison-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }
         .comparison-table tr:last-child td {
             border-bottom: none;
         }

        /* 野菜图鉴 */
        .plant-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .plant-card {
            width: calc(50% - 7.5px);
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
        }
        .plant-image {
            height: 120px;
            background-color: #E8F5E9;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 40px;
            color: #90B77D;
            flex-shrink: 0;
        }
        .plant-info {
            padding: 15px;
            flex-grow: 1;
        }
        .plant-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2E4057;
        }
        .plant-description p {
             font-size: 13px;
             color: #555;
             margin-bottom: 5px;
             line-height: 1.4;
         }

      /* 地图 */
        .location-map {
            background-color: #E8F5E9;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            height: 550px; /* <--- 修改为一个更大的值 */
            flex-shrink: 0;
        }

        .map-marker {
            position: absolute;
            width: 24px;
            height: 24px;
            background-color: #D95D39;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            border: 2px solid white;
            cursor: pointer;
            z-index: 2;
        }
        .map-marker::after {
             content: '';
             position: absolute;
             left: 50%;
             top: 90%;
             transform: translateX(-50%) rotate(45deg);
             width: 12px;
             height: 12px;
             background-color: #D95D39;
             border-bottom-right-radius: 5px;
             z-index: -1;
         }
         .map-label {
            position: absolute;
            font-size: 12px;
            font-weight: bold;
            color: #333;
            background-color: rgba(255, 255, 255, 0.7);
            padding: 2px 5px;
            border-radius: 3px;
            white-space: nowrap;
            transform: translate(15px, -50%);
            z-index: 3;
         }

        /* 装备清单 */
        .equipment-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .equipment-item {
            background-color: white;
            border-radius: 5px;
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            width: calc(50% - 5px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .equipment-icon {
            width: 40px;
            height: 40px;
            background-color: #E8F5E9;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            color: #90B77D;
            flex-shrink: 0;
        }
        .equipment-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
        }
         .equipment-item > div:last-child {
             flex: 1;
             min-width: 0;
         }

        /* 结语页 */
        #page-12 { /* 结语页的 .page 元素 */
            padding: 0 !important; /* 覆盖 .page 的默认 padding */
            justify-content: flex-start; /* 内容从顶部开始 */
            align-items: center; /* 水平居中 */
             overflow-y: auto; /* 结语页允许自己滚动 */
        }
        .conclusion-page {
            display: flex;
            flex-direction: column;
            /* justify-content: center; */ /* 移除，让内容自然排列 */
            align-items: center;
            text-align: center;
            padding: 60px 40px; /* 调整内边距 */
            width: 100%;
            max-width: 800px; /* 限制内容最大宽度 */
            margin: 0 auto; /* 配合 max-width 居中 */
            box-sizing: border-box;
            flex-shrink: 0; /* 防止结语内容被压缩 */
        }
        .conclusion-title {
            font-size: 32px;
            color: #D95D39;
            margin-bottom: 30px;
        }
        .conclusion-text {
            font-size: 18px;
            line-height: 1.8;
            max-width: 700px; /* 可适当调整 */
            margin-bottom: 50px;
            text-align: justify; /* 两端对齐可能更好看 */
        }
        .conclusion-icon {
            font-size: 60px;
            color: #90B77D;
            margin-bottom: 40px; /* 调整间距 */
        }
         /* 结语页内的 info-box 特殊调整 */
         .conclusion-page .info-box {
             max-width: 700px;
             width: 95%;
             margin-bottom: 40px;
             text-align: left;
             background-color: rgba(255,255,255,0.8);
         }
         .conclusion-page .cover-footer {
             margin-top: 30px; /* 调整页脚上边距 */
             color: #666;
         }

        /* 页脚 */
        .page-footer {
            text-align: center;
            font-size: 12px;
            color: #777;
            /* margin-top: auto; */ /* <--- 移除，由绝对定位控制 */
            padding: 15px 40px 10px 40px; /* 调整内边距 */
            border-top: 1px solid #ddd;
            flex-shrink: 0;
            width: 100%; /* 确保宽度 */
            background-color: #EFEAD8; /* 确保背景色一致 */
            position: absolute; /* <--- 修改为绝对定位 */
            bottom: 0;
            left: 0;
            box-sizing: border-box; /* 确保padding包含在width内 */
            z-index: 5; /* 确保在 page-content 上方 */
        }


        /* 目录样式 */
        #toc-btn {
            position: fixed;
            bottom: 100px; /* 稍微上移一点，避开导航按钮 */
            right: 30px;
            width: auto;
            padding: 0 20px;
            height: 50px;
            border-radius: 25px;
            background-color: #D95D39;
            z-index: 100;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            border: none;
            font-size: 16px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: transform 0.2s ease, background-color 0.2s ease;
        }
        #toc-btn:hover {
            transform: scale(1.1);
            background-color: #b34a2d;
        }
        #toc-panel {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            max-width: 90vw;
            height: 100%;
            background-color: rgba(239, 234, 216, 0.98);
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.15);
            z-index: 200;
            transition: right 0.4s ease-in-out;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        #toc-panel.open {
            right: 0;
        }
        #toc-header {
            padding: 20px;
            font-size: 24px;
            font-weight: bold;
            color: #2E4057;
            border-bottom: 1px solid #ccc;
            flex-shrink: 0;
            background-color: #EFEAD8;
        }
        #toc-content {
            flex: 1;
            padding: 10px 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
        .toc-item {
            padding: 8px 20px; /* 减小垂直padding */
            font-size: 14px;
            color: #555;
            cursor: pointer;
            transition: background-color 0.2s ease, color 0.2s ease;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .toc-item div:last-child { /* 副标题行 */
             padding-left: 1em; /* 增加缩进 */
             font-size: 0.9em;
             opacity: 0.7;
        }
         .toc-item:last-child {
             border-bottom: none;
         }
        .toc-item:hover {
            background-color: rgba(144, 183, 125, 0.3);
            color: #2E4057;
        }
        .toc-item.active {
            background-color: #90B77D;
            color: white;
            font-weight: bold;
        }
         .toc-item.active div { /* 激活项的副标题颜色 */
             color: white !important;
             opacity: 0.9;
         }

        /* 食谱卡片 */
        .recipe-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            flex-shrink: 0; /* 防止食谱卡片被压缩 */
        }
        .recipe-title {
            background-color: #D95D39;
            color: white;
            padding: 12px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        .recipe-content {
            padding: 20px;
        }
        .recipe-section {
            margin-bottom: 15px;
        }
        .recipe-section:last-child {
            margin-bottom: 0;
        }
        .recipe-section-title {
            font-weight: bold;
            color: #2E4057;
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .recipe-content ul, .recipe-content ol {
            padding-left: 20px;
            margin-top: 5px;
        }
        .recipe-content li {
            margin-bottom: 5px;
            line-height: 1.6;
            font-size: 14px;
        }
        .recipe-content p {
             font-size: 14px;
             line-height: 1.6;
             color: #555;
        }

    </style>
</head>
<body>
    <div id="book-container">
        <!-- 页面内容将由 JavaScript 动态生成 -->
    </div>

    <!-- 导航按钮 -->
    <div class="nav-buttons">
        <button id="prev-button" class="nav-button" title="上一页">←</button>
        <button id="next-button" class="nav-button" title="下一页">→</button>
    </div>

    <!-- 页面指示器 -->
    <div id="page-indicator">1 / N</div> <!-- Placeholder -->

    <!-- 目录按钮 -->
    <button id="toc-btn" title="目录">目录</button>

    <!-- 目录面板 (初始隐藏) -->
    <div id="toc-panel">
        <div id="toc-header">目录</div>
        <div id="toc-content">
            <!-- TOC items will be generated here by JS -->
        </div>
    </div>

    <script>
        // 页面数据 (保持不变, 只需确保 Page 3 的图表 div 样式已移除 height)
        const pages = [
             // 第1页：封面
            {
                title: "封面",
                subtitle: "",
                content: `
                    <div class="cover-title">城市生活方式研究：上海挖野菜相亲</div>
                    <div class="cover-subtitle">都市社交的自然回归</div>
                    <div class="cover-icon">
                        <span style="transform: rotate(45deg);">✿</span>
                    </div>
                    <div class="cover-footer">作者：祝韬 2025年3月29日</div>
                `
            },
            // 第2页：为什么是"挖野菜"？
            {
                title: "现象起源",
                subtitle: "为什么是\"挖野菜\"？",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>现象描述</h3>
                                <p>2023年春季，上海年轻人中兴起了一种新型社交方式——"挖野菜约会"。这种约会形式通常发生在城市郊区或公园绿地，参与者一起寻找、辨识和采集可食用的野生植物，随后可能一起烹饪分享。</p>
                                <p>从最初的小众活动，迅速发展为社交媒体热门话题，小红书、抖音等平台上相关内容获得大量点赞和分享，成为都市年轻人社交生活的新选择。</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">挖野菜约会活动特点</div>
                                <div style="padding: 20px;">
                                    <div class="process-flow">
                                        <div class="process-step">
                                            <div class="step-number">1</div>
                                            <div class="step-content">
                                                <strong>低消费</strong>
                                                <p>几乎不需要花费，只需准备简单工具</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">2</div>
                                            <div class="step-content">
                                                <strong>互动性强</strong>
                                                <p>需要共同探索、辨识、采集，互动频繁</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">3</div>
                                            <div class="step-content">
                                                <strong>知识型</strong>
                                                <p>涉及植物辨识、传统饮食文化等知识</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">4</div>
                                            <div class="step-content">
                                                <strong>自然体验</strong>
                                                <p>远离城市喧嚣，回归自然环境</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">5</div>
                                            <div class="step-content">
                                                <strong>延展性强</strong>
                                                <p>可延伸至共同烹饪、分享等后续活动</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>现象起源</h3>
                                <p>挖野菜约会现象的兴起可以追溯到几个关键因素的交汇：</p>
                                <ul>
                                    <li><strong>疫情后社交需求</strong>：疫情后人们渴望户外社交活动</li>
                                    <li><strong>社交媒体传播</strong>：几位意见领袖的分享引发模仿效应</li>
                                    <li><strong>传统文化复兴</strong>：年轻人对传统知识的重新关注</li>
                                    <li><strong>消费主义反思</strong>：对高消费约会模式的反思</li>
                                    <li><strong>自然疗愈需求</strong>：都市生活压力下对自然的向往</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">社交媒体热度变化 (示意)</div>
                                <div style="width: 100%; height: 200px; display: flex; justify-content: space-around; align-items: flex-end; padding: 20px;">
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 50px;">
                                        <div style="width: 40px; height: 30px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">2022年<br>冬季</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 50px;">
                                        <div style="width: 40px; height: 60px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">2023年<br>1-2月</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 50px;">
                                        <div style="width: 40px; height: 150px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">2023年<br>3-4月</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 50px;">
                                        <div style="width: 40px; height: 180px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">2023年<br>5月</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 50px;">
                                        <div style="width: 40px; height: 120px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">2023年<br>夏季</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="border-left: 4px solid #D95D39;">
                                <h3>用户评论</h3>
                                <blockquote>
                                <p>"第一次挖野菜约会，比去咖啡厅聊天有意思多了！一起弯腰寻找马兰头的过程中，不知不觉就熟悉起来，没有尴尬的沉默，反而有很多共同话题。"</p>
                                <footer>——小红书用户 @城市野趣</footer>
                                </blockquote>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第3页：城市青年为什么会被吸引？
            {
                title: "现象起源",
                subtitle: "城市青年为什么会被吸引？",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>吸引力来源</h3>
                                <p>挖野菜约会对都市年轻人的吸引力来源于多个层面：</p>
                                <ul>
                                    <li><strong>新奇感</strong>：打破常规约会模式的新鲜体验</li>
                                    <li><strong>真实互动</strong>：提供真实、不做作的社交场景</li>
                                    <li><strong>技能展示</strong>：展示植物知识、野外生存技能的机会</li>
                                    <li><strong>情感共鸣</strong>：唤起对童年或乡村生活的集体记忆</li>
                                    <li><strong>社交媒体价值</strong>：提供独特的内容创作素材</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">参与者动机分析 (%) (示意)</div>
                                <div style="width: 100%; height: 200px; display: flex; justify-content: space-around; align-items: flex-end; padding: 20px;">
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 170px; background: linear-gradient(to top, #90B77D, #BCAAA4); border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">新型社交<br>体验 85</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 150px; background: linear-gradient(to top, #90B77D, #BCAAA4); border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">亲近<br>自然 75</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 120px; background: linear-gradient(to top, #90B77D, #BCAAA4); border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">学习传统<br>知识 60</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 100px; background: linear-gradient(to top, #90B77D, #BCAAA4); border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">社交媒体<br>分享 50</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 70px; background: linear-gradient(to top, #90B77D, #BCAAA4); border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">节省<br>成本 35</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="border-left: 4px solid #D95D39;">
                                <h3>参与者心声</h3>
                                <blockquote>
                                <p>"在上海这样的大都市生活久了，总觉得社交活动被固定在咖啡厅、餐厅、电影院这几个场景。挖野菜约会让我感受到了不一样的互动方式，也让我重新连接到了小时候在农村的记忆。"</p>
                                <footer>——李先生，28岁，IT工程师</footer>
                                </blockquote>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>都市青年的心理需求</h3>
                                <p>挖野菜约会满足了当代都市青年的几个核心心理需求：</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">心理需求满足机制</div>
                                <div style="padding: 0;">
                                    <table class="comparison-table">
                                        <thead>
                                            <tr>
                                                <th>心理需求</th>
                                                <th>挖野菜约会的满足方式</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>真实感缺失</td>
                                                <td>提供不依赖滤镜和包装的真实互动体验</td>
                                            </tr>
                                            <tr>
                                                <td>自然疏离感</td>
                                                <td>重建与自然环境的直接接触和连接</td>
                                            </tr>
                                            <tr>
                                                <td>社交焦虑</td>
                                                <td>活动本身提供天然话题，减轻社交压力</td>
                                            </tr>
                                            <tr>
                                                <td>意义感缺失</td>
                                                <td>通过学习传统知识，获得文化连接感</td>
                                            </tr>
                                            <tr>
                                                <td>消费主义疲劳</td>
                                                <td>提供低消费但高质量的社交选择</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="info-box">
                                <h3>代际差异与文化传承</h3>
                                <p>挖野菜约会现象也反映了代际之间的文化传承与变革：</p>
                                <ul>
                                    <li><strong>知识反向流动</strong>：年轻人向长辈学习传统知识</li>
                                    <li><strong>传统再创造</strong>：赋予传统活动新的社交功能</li>
                                    <li><strong>文化记忆激活</strong>：唤醒集体记忆中的生活技能</li>
                                    <li><strong>城乡文化融合</strong>：城市青年对乡村生活元素的重新诠释</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">参与者年龄分布 (%) (示意)</div>
                                <!-- 之前已修改: 移除固定 height -->
                                <div style="width: 100%; min-height: 200px; height: auto; display: flex; justify-content: space-around; align-items: flex-end; padding: 20px;">
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 60px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">18-22岁<br>15</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 160px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">23-28岁<br>40</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 120px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">29-35岁<br>30</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 60px; background-color: #90B77D; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">36-45岁<br>15</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `
            },
            // ... (其他页面内容保持不变) ...
             // 第4页：这不是"干农活"，这是"生活共谋"
            {
                title: "现象分析",
                subtitle: "这不是\"干农活\"，这是\"生活共谋\"",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>从娱乐到情感机制</h3>
                                <p>挖野菜约会不仅仅是一种娱乐活动，更是一种独特的情感连接机制：</p>
                                <ul>
                                    <li><strong>共同目标</strong>：寻找野菜这一明确目标促进合作。</li>
                                    <li><strong>能力互补</strong>：不同知识背景的人可以互相学习。</li>
                                    <li><strong>情绪共享</strong>：发现野菜时的惊喜感是共同体验。</li>
                                    <li><strong>脆弱性展示</strong>：在不熟悉环境中展现真实自我，而非完美形象。</li>
                                    <li><strong>故事共创</strong>：共同经历成为独特的关系叙事基础。</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">挖野菜约会的情感机制</div>
                                <div style="width: 100%; padding: 20px;">
                                    <div class="process-flow">
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #D95D39;">1</div>
                                            <div class="step-content" style="background-color: #FFF3E0;">
                                                <strong>共同探索</strong>
                                                <p>在未知环境中共同探索建立初步信任感</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #D95D39;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #D95D39;">2</div>
                                            <div class="step-content" style="background-color: #FFF3E0;">
                                                <strong>知识分享</strong>
                                                <p>交流野菜知识展示个人价值观和背景，相互学习</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #D95D39;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #D95D39;">3</div>
                                            <div class="step-content" style="background-color: #FFF3E0;">
                                                <strong>共同收获</strong>
                                                <p>一起采集、分享成果带来成就感和团队归属感</p>
                                            </div>
                                        </div>
                                         <div class="process-arrow" style="background-color: #D95D39;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #D95D39;">4</div>
                                            <div class="step-content" style="background-color: #FFF3E0;">
                                                <strong>后续延展</strong>
                                                <p>烹饪分享延续互动，创造更多了解机会，深化关系</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="border-left: 4px solid #D95D39;">
                                <h3>关系发展案例</h3>
                                <blockquote>
                                <p>"我们第一次约会是去崇明岛挖野菜，那天他教我辨认荠菜和马兰头，我则分享了小时候奶奶教我的野菜烹饪方法。回城后我们一起做了一顿晚餐，那种共同劳动和分享的感觉特别亲密自然。三个月后我们确定了关系，现在每个季节都会安排一次野菜之旅。"</p>
                                <footer>——张女士，26岁，设计师</footer>
                                </blockquote>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>"生活共谋"的概念</h3>
                                <p>"生活共谋"（Doing-togetherness）指的是通过共同参与日常生活实践活动（而非仅仅消费或观赏），在互动中测试、建立和维系亲密关系的过程。挖野菜约会正是这种模式的典型体现。</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">"生活共谋" vs 传统约会</div>
                                <div style="padding:0;">
                                <table class="comparison-table">
                                     <thead>
                                        <tr>
                                            <th>维度</th>
                                            <th>传统消费型约会</th>
                                            <th>挖野菜"生活共谋"</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>互动焦点</td>
                                            <td>对话、观赏、服务体验</td>
                                            <td>行动、协作、问题解决</td>
                                        </tr>
                                        <tr>
                                            <td>互动方式</td>
                                            <td>面对面 (为主)</td>
                                            <td>并肩式 (为主)</td>
                                        </tr>
                                        <tr>
                                            <td>关系测试</td>
                                            <td>言谈举止、消费品味、价值观表达</td>
                                            <td>实际能力、耐心、合作精神、应变能力</td>
                                        </tr>
                                        <tr>
                                            <td>情感建立</td>
                                            <td>通过语言交流、氛围烘托</td>
                                            <td>通过共同经历、协作成果、情绪共享</td>
                                        </tr>
                                        <tr>
                                            <td>真实性</td>
                                            <td>易受场景影响，可能存在表演性</td>
                                            <td>行动中难以长时间维持伪装，更易展现本真</td>
                                        </tr>
                                        <tr>
                                            <td>延续性</td>
                                            <td>活动结束即分离 (通常)</td>
                                            <td>成果 (野菜) 可带回，易于延续至烹饪分享</td>
                                        </tr>
                                    </tbody>
                                </table>
                                </div>
                            </div>

                            <div class="info-box">
                                <h3>关系测试功能</h3>
                                <p>挖野菜约会像一个微型的生活场景模拟，能在短时间内观察到对方多维度的特质：</p>
                                <ul>
                                    <li><strong>耐心与细心</strong>：寻找细小野菜需要专注。</li>
                                    <li><strong>知识储备与分享态度</strong>：是否了解或愿意学习、分享。</li>
                                    <li><strong>应变与解决问题能力</strong>：面对找不到、认错、天气变化等。</li>
                                    <li><strong>身体语言与情绪状态</strong>：在自然放松环境下的表现。</li>
                                    <li><strong>价值观体现</strong>：对自然、劳动、传统的态度。</li>
                                    <li><strong>合作与体贴精神</strong>：是否主动协作、关心对方。</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">挖野菜约会的关系发展路径</div>
                                <div style="width: 100%; padding: 20px;">
                                    <div style="display: flex; flex-direction: column; gap: 10px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div style="width: 80px; text-align: right; font-weight: bold; font-size: 14px; flex-shrink: 0;">初次约会</div>
                                            <div style="width: 20px; text-align: center; flex-shrink: 0;">→</div>
                                            <div style="flex-grow: 1; background-color: #FFF3E0; padding: 8px; border-radius: 5px; font-size: 14px;">
                                                共同探索，初步了解，基础互动
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div style="width: 80px; text-align: right; font-weight: bold; font-size: 14px; flex-shrink: 0;">后续互动</div>
                                            <div style="width: 20px; text-align: center; flex-shrink: 0;">→</div>
                                            <div style="flex-grow: 1; background-color: #FFF3E0; padding: 8px; border-radius: 5px; font-size: 14px;">
                                                分享成果 (照片/实物)，共同烹饪 (可能)
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div style="width: 80px; text-align: right; font-weight: bold; font-size: 14px; flex-shrink: 0;">关系深化</div>
                                            <div style="width: 20px; text-align: center; flex-shrink: 0;">→</div>
                                            <div style="flex-grow: 1; background-color: #FFF3E0; padding: 8px; border-radius: 5px; font-size: 14px;">
                                                探索其他"生活共谋"活动，增加相处场景
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div style="width: 80px; text-align: right; font-weight: bold; font-size: 14px; flex-shrink: 0;">融入生活</div>
                                            <div style="width: 20px; text-align: center; flex-shrink: 0;">→</div>
                                            <div style="flex-grow: 1; background-color: #FFF3E0; padding: 8px; border-radius: 5px; font-size: 14px;">
                                                可能形成季节性活动习惯，成为共同生活方式一部分
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第5页：亲密关系的"都市异化"背景
            {
                title: "社会结构洞察",
                subtitle: "亲密关系的\"都市异化\"背景",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>都市亲密关系的困境</h3>
                                <p>当代上海等大都市的年轻人在亲密关系建立过程中面临多重挑战：</p>
                                <ul>
                                    <li><strong>社交空间商业化</strong>：多数社交场所与消费挂钩，缺乏免费、自然的公共交往空间。</li>
                                    <li><strong>交往效率化与快餐化</strong>：快节奏生活压缩了关系发展的自然时间，追求快速匹配。</li>
                                    <li><strong>关系表演化与滤镜化</strong>：社交媒体影响下，过度注重自我形象包装，隐藏真实。</li>
                                    <li><strong>情感连接浅表化</strong>：互动模式固化，缺乏深度、走心的交流机会。</li>
                                    <li><strong>期望标准化与功利化</strong>：受外界标准影响，择偶条件趋同，情感需求被物化。</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">都市亲密关系的"异化"表现</div>
                                <div style="width: 100%; padding: 20px;">
                                    <div class="process-flow">
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">1</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>展示性社交</strong>
                                                <p>约会成为社交媒体素材，"打卡"重于体验，为"看客"表演。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #2E4057;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">2</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>消费型互动</strong>
                                                <p>关系建立依赖于餐厅、电影院等消费场景，缺乏非消费性连接。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #2E4057;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">3</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>数字化依赖</strong>
                                                <p>过度依赖线上沟通，线下互动能力减弱，缺乏身体在场的共情。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #2E4057;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">4</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>条件匹配优先</strong>
                                                <p>过度关注房、车、学历等外在条件，情感和性格匹配度被忽视。</p>
                                            </div>
                                        </div>
                                     </div>
                                </div>
                            </div>

                            <div class="info-box" style="border-left: 4px solid #2E4057;">
                                <h3>社会学视角</h3>
                                <blockquote>
                                <p>"当代都市亲密关系的异化，是社会加速、消费主义和技术中介共同作用的结果。'挖野菜约会'等反潮流实践，体现了青年对本真性、具身性和非功利性交往的内在需求，是对过度理性化和商品化社会的一种感性抵抗。"</p>
                                <footer>——王教授，复旦大学社会学系</footer>
                                </blockquote>
                            </div>
                        </div>

                        <div class="column">
                             <div class="info-box">
                                <h3>相关调查数据 (示意)</h3>
                                <p>某项针对上海单身青年的调查（2023）显示：</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">上海青年对约会与关系的看法 (%)</div>
                                <div style="width: 100%; height: 200px; display: flex; justify-content: space-around; align-items: flex-end; padding: 20px;">
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 156px; background-color: #2E4057; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">感到社交<br>有压力 78</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 130px; background-color: #2E4057; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">对程式化<br>约会倦怠 65</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 164px; background-color: #2E4057; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">渴望更<br>真实互动 82</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 90px; background-color: #2E4057; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">尝试非<br>消费约会 45</div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
                                        <div style="width: 40px; height: 70px; background-color: #2E4057; border-radius: 5px 5px 0 0;"></div>
                                        <div style="margin-top: 10px; text-align: center; font-size: 12px; line-height: 1.2;">对现有<br>约会满意 35</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box">
                                <h3>都市亲密关系的结构性挑战</h3>
                                <p>挖野菜约会等现象的出现，侧面反映了几个结构性挑战：</p>
                                <ul>
                                    <li><strong>公共空间不足/商业化</strong>：缺乏适合深度交往的非盈利性公共空间。</li>
                                    <li><strong>时间贫困</strong>：高强度工作压缩了经营关系所需的时间和精力。</li>
                                    <li><strong>互动模式单一</strong>：约会场景和活动同质化，难以展现多元特质。</li>
                                    <li><strong>评价体系固化</strong>：社会压力导致过度关注外在硬性条件。</li>
                                    <li><strong>情感教育缺失</strong>：缺乏学习如何建立和维系健康亲密关系的机会。</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">挖野菜约会如何尝试回应挑战</div>
                                 <div style="padding: 0;">
                                <table class="comparison-table">
                                    <thead>
                                        <tr>
                                            <th>结构性挑战</th>
                                            <th>挖野菜约会的尝试与回应</th>
                                        </tr>
                                    </thead>
                                     <tbody>
                                        <tr>
                                            <td>空间商业化</td>
                                            <td>利用免费的自然/公共空间，脱离消费逻辑</td>
                                        </tr>
                                        <tr>
                                            <td>时间压缩</td>
                                            <td>活动本身需要投入时间与耐心，放慢节奏</td>
                                        </tr>
                                        <tr>
                                            <td>互动单一</td>
                                            <td>融合探索、学习、协作、分享等多维互动</td>
                                        </tr>
                                        <tr>
                                            <td>表演压力</td>
                                            <td>自然环境和体力活动使维持表演更困难</td>
                                        </tr>
                                        <tr>
                                            <td>功利评价</td>
                                            <td>更易观察耐心、知识、动手能力、合作精神等内在特质</td>
                                        </tr>
                                    </tbody>
                                </table>
                                </div>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第6页：从"精致消费型约会"走向"行动交互型约会"
            {
                title: "约会模式变迁",
                subtitle: "从\"精致消费\"到\"行动交互\"",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>约会范式的转变</h3>
                                <p>上海年轻人的约会选择正在呈现一种趋势，从侧重场所和消费的"精致消费型"向侧重过程和互动的"行动交互型"拓展：</p>
                                <ul>
                                    <li><strong>精致消费型约会</strong>：核心是"去哪里消费"，通过选择特定的、有格调的消费场所（如网红餐厅、咖啡馆、酒吧、展览）来构建约会体验和展示品味。</li>
                                    <li><strong>行动交互型约会</strong>：核心是"一起做什么"，通过共同参与一项活动（如挖野菜、做饭、徒步、手工、志愿者）来创造互动机会和共享经历。</li>
                                </ul>
                                <p>这并非完全取代，而是一种重要的补充和价值取向的转变。</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">约会模式对比（典型活动示例）</div>
                                <div style="padding:0;">
                                <table class="comparison-table">
                                    <thead>
                                        <tr>
                                            <th>精致消费型</th>
                                            <th>行动交互型</th>
                                        </tr>
                                    </thead>
                                     <tbody>
                                        <tr>
                                            <td>探店网红咖啡馆/餐厅</td>
                                            <td>一起逛菜场买菜做饭</td>
                                        </tr>
                                        <tr>
                                            <td>看电影/演出/展览</td>
                                            <td>户外徒步/骑行/City Walk</td>
                                        </tr>
                                        <tr>
                                            <td>酒吧小酌/Live House</td>
                                            <td>公园野餐/放风筝/飞盘</td>
                                        </tr>
                                        <tr>
                                            <td>购物中心逛街</td>
                                            <td>逛旧书店/跳蚤市场/古镇</td>
                                        </tr>
                                         <tr>
                                            <td>高级SPA/美容体验</td>
                                            <td>一起做手工/陶艺/画画</td>
                                        </tr>
                                         <tr>
                                            <td>主题乐园/密室逃脱</td>
                                            <td>一起做志愿者/参加公益活动</td>
                                        </tr>
                                    </tbody>
                                </table>
                                </div>
                            </div>

                            <div class="info-box" style="border-left: 4px solid #2E4057;">
                                <h3>消费社会学视角</h3>
                                <blockquote>
                                <p>"精致消费型约会强调通过消费符号来构建和沟通身份认同与社会区隔。而行动交互型约会则将重心放在共同实践所产生的'过程价值'和'关系价值'，更强调体验的本真性和情感的深度连接，可视为对消费文化逻辑的一种反思性实践。"</p>
                                <footer>——陈教授，华东师范大学社会学系</footer>
                                </blockquote>
                            </div>

                            <div class="chart">
                                <div class="chart-title">约会模式转变的驱动因素</div>
                                 <div style="width: 100%; padding: 20px;">
                                    <div class="process-flow">
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">1</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>经济压力与理性消费</strong>
                                                <p>年轻人消费趋于理性，寻求性价比高或免费的社交方式。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #2E4057;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">2</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>价值观多元化</strong>
                                                <p>从追求物质符号转向追求体验、成长和真实感。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow" style="background-color: #2E4057;"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">3</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>社交媒体的反作用</strong>
                                                <p>对过度表演和"滤镜生活"产生疲劳，寻求线下真实互动。</p>
                                            </div>
                                        </div>
                                         <div class="process-arrow" style="background-color: #2E4057;"></div>
                                         <div class="process-step">
                                            <div class="step-number" style="background-color: #2E4057;">4</div>
                                            <div class="step-content" style="background-color: #E8EAF6;">
                                                <strong>疫情影响的延续</strong>
                                                <p>提升了对户外活动、健康生活和身边人关系的重视。</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>行动交互型约会的心理优势</h3>
                                <p>为何"一起做事"比"一起消费"更能促进关系？</p>
                                <ul>
                                    <li><strong>并肩效应 (Side-by-Side Effect)</strong>：相比面对面的审视感，并肩协作更易产生放松和亲近感。</li>
                                    <li><strong>共同目标导向</strong>：合力完成任务能自然产生团队感和成就感。</li>
                                    <li><strong>非语言信息丰富</strong>：通过行动观察对方的习惯、能力、情绪处理方式。</li>
                                    <li><strong>情境性问题解决</strong>：共同应对小挑战（如迷路、工具坏了）能增进默契和信任。</li>
                                    <li><strong>独特性与记忆锚点</strong>：非标准化的共同经历更容易形成独特而深刻的记忆。</li>
                                    <li><strong>降低初始社交压力</strong>：活动本身提供了焦点，减少了"没话找话"的尴尬。</li>
                                </ul>
                            </div>

                            <div class="chart">
                                <div class="chart-title">约会模式与关系促进维度 (示意比较)</div>
                                 <div style="width: 100%; padding: 20px;">
                                    <p style="font-size: 13px; margin-bottom: 15px; color: #555;">比较两种模式在促进关系不同维度的相对效果（示意）：</p>
                                    <div style="display: flex; flex-direction: column; gap: 15px; font-size: 14px;">
                                        <div>
                                            <div><strong>初印象营造 (表面)</strong></div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">精致消费型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #90B77D 80%, #eee 80%); border-radius: 8px;"></div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">行动交互型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #2E4057 60%, #eee 60%); border-radius: 8px;"></div>
                                            </div>
                                        </div>
                                         <div>
                                            <div><strong>舒适感与放松度</strong></div>
                                             <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">精致消费型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #90B77D 60%, #eee 60%); border-radius: 8px;"></div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">行动交互型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #2E4057 85%, #eee 85%); border-radius: 8px;"></div>
                                            </div>
                                        </div>
                                         <div>
                                            <div><strong>真实性展现</strong></div>
                                             <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">精致消费型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #90B77D 40%, #eee 40%); border-radius: 8px;"></div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">行动交互型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #2E4057 90%, #eee 90%); border-radius: 8px;"></div>
                                            </div>
                                        </div>
                                         <div>
                                            <div><strong>深度情感连接</strong></div>
                                             <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">精致消费型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #90B77D 50%, #eee 50%); border-radius: 8px;"></div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">行动交互型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #2E4057 95%, #eee 95%); border-radius: 8px;"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div><strong>独特性与记忆点</strong></div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">精致消费型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #90B77D 65%, #eee 65%); border-radius: 8px;"></div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <span style="width: 100px;">行动交互型:</span>
                                                <div style="flex: 1; height: 15px; background: linear-gradient(to right, #2E4057 90%, #eee 90%); border-radius: 8px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; padding: 15px 0 0 0; font-size: 12px; color: #666;">
                                        <span style="display: inline-block; width: 12px; height: 12px; background-color: #90B77D; margin-right: 5px; vertical-align: middle;"></span> 精致消费型 (相对效果)
                                        <span style="display: inline-block; width: 12px; height: 12px; background-color: #2E4057; margin-right: 5px; margin-left: 15px; vertical-align: middle;"></span> 行动交互型 (相对效果)
                                    </div>
                                </div>
                            </div>

                            <div class="info-box">
                                <h3>行动交互型约会的社会文化意义</h3>
                                <p>这类约会的兴起，不止关乎个体情感，也折射出更广泛的社会文化转向：</p>
                                <ul>
                                    <li><strong>对消费主义的反思</strong>：质疑"用消费定义关系"的模式。</li>
                                    <li><strong>对实用技能的重估</strong>：动手能力、生活知识重新获得社交价值。</li>
                                    <li><strong>对本土与传统的再连接</strong>：重新发现身边环境和传统文化的魅力。</li>
                                    <li><strong>对生态与可持续的关注</strong>：亲近自然可能引发更强的环保意识。</li>
                                    <li><strong>对社群与合作的向往</strong>：在原子化社会中寻求真实的协作与归属。</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `
            },
           // 第7页：上海挖野菜地图
            {
                title: "实践指南",
                subtitle: "上海挖野菜地图 (示意)",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>上海挖野菜热门地点 (仅供参考)</h3>
                                <p>上海及周边地区适合挖野菜的地点众多，但请务必注意安全、遵守规定、保护环境。以下地点仅为网络信息和经验分享，出行前请自行核实确认。</p>
                                 <p style="color: red; font-weight: bold;">重要提示：公园绿地通常禁止采挖植物！农田和私人土地未经允许不得进入！保护区严禁任何形式的采集！请优先选择允许采集的郊野公园指定区域或跟随有组织的活动。</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">上海挖野菜地点分布 (示意图)</div>
                                <div class="location-map">
                                    <!-- 简化的上海地图背景 (仅示意) -->
                                    <div style="position: absolute; top: 50%; left: 50%; width: 90%; height: 90%; border: 2px dashed #90B77D; border-radius: 10%; transform: translate(-50%, -50%); opacity: 0.5;"></div>
                                    <div style="position: absolute; top: 50%; left: 50%; width: 60%; height: 60%; border: 2px dashed #90B77D; border-radius: 10%; transform: translate(-50%, -50%); opacity: 0.5;"></div>
                                    <div style="position: absolute; top: 50%; left: 50%; width: 30%; height: 30%; border: 2px dashed #90B77D; border-radius: 10%; transform: translate(-50%, -50%); opacity: 0.5;"></div>
                                    <div style="position: absolute; top: 50%; left: 50%; font-size: 14px; color: #aaa; transform: translate(-50%, -50%);">市中心</div>

                                    <!-- 地点标记与标签 -->
                                    <div class="map-marker" style="top: 20%; left: 30%;" title="崇明岛 (东滩周边/乡间田埂需注意)"></div>
                                    <div class="map-label" style="top: 20%; left: 30%;">崇明岛区域</div>

                                    <div class="map-marker" style="top: 45%; left: 75%;" title="浦东 (世纪公园通常禁止/滨江绿地需留意规定)"></div>
                                     <div class="map-label" style="top: 45%; left: 75%;">浦东部分区域</div>

                                    <div class="map-marker" style="top: 70%; left: 25%;" title="松江 (泖港/佘山周边乡野需谨慎)"></div>
                                    <div class="map-label" style="top: 70%; left: 25%;">松江区域</div>

                                    <div class="map-marker" style="top: 85%; left: 50%;" title="金山 (廊下郊野公园可能有指定区域)"></div>
                                     <div class="map-label" style="top: 85%; left: 50%;">金山区域</div>

                                    <div class="map-marker" style="top: 55%; left: 15%;" title="青浦 (朱家角/淀山湖周边需注意)"></div>
                                    <div class="map-label" style="top: 55%; left: 15%;">青浦区域</div>

                                     <div class="map-marker" style="top: 30%; left: 55%;" title="宝山 (顾村公园等通常禁止/外围绿地?)"></div>
                                    <div class="map-label" style="top: 30%; left: 55%;">宝山区域</div>

                                    <div class="map-marker" style="top: 75%; left: 70%;" title="奉贤 (海湾森林公园等需查规定)"></div>
                                    <div class="map-label" style="top: 75%; left: 70%;">奉贤区域</div>


                                    <!-- 图例 -->
                                    <div style="position: absolute; bottom: 15px; right: 15px; background-color: rgba(255, 255, 255, 0.8); padding: 10px; border-radius: 5px; font-size: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                            <div style="width: 12px; height: 12px; background-color: #D95D39; border-radius: 50%; margin-right: 5px; border: 1px solid white;"></div>
                                            <span>可能存在野菜的区域 (示意)</span>
                                        </div>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 30px; height: 2px; border-top: 2px dashed #90B77D; margin-right: 5px;"></div>
                                            <span>距离市中心范围 (示意)</span>
                                        </div>
                                        <div style="margin-top: 5px; color: red;">*务必遵守当地规定*</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>地点选择考量因素</h3>
                                <p>选择挖野菜约会地点时，需要综合考虑：</p>
                                <ul>
                                    <li><strong>合法性与规定:</strong> 是否允许采集？有无特定区域？公园、保护区、农田通常禁止。</li>
                                    <li><strong>安全性:</strong> 交通是否便利？地形是否安全？有无污染源（工厂、公路、垃圾场）？</li>
                                    <li><strong>野菜种类与季节:</strong> 不同地点、季节野菜不同，提前做功课。</li>
                                    <li><strong>配套设施:</strong> 卫生间、休息区、小卖部等（尤其是初次约会）。</li>
                                    <li><strong>约会目标:</strong> 侧重体验、拍照还是真的想采很多？</li>
                                    <li><strong>交通时间:</strong> 往返时间是否过长？</li>
                                </ul>
                            </div>

                             <div style="display: flex; flex-direction: column; gap: 15px;">
                                <div style="background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);">
                                    <div style="background-color: #90B77D; color: white; padding: 10px 15px; font-weight: bold;">郊野公园 (可能选择)</div>
                                    <div style="padding: 15px; font-size: 14px; line-height: 1.6;">
                                        <p><strong>优点:</strong> 可能有指定允许区域，环境相对规整，设施较全。</p>
                                        <p><strong>缺点:</strong> 可能需要门票，允许区域野菜可能不多，仍需确认规定。</p>
                                        <p><strong>示例:</strong> 廊下、浦江、长兴等郊野公园（需查询最新规定）。</p>
                                    </div>
                                </div>

                                <div style="background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);">
                                    <div style="background-color: #90B77D; color: white; padding: 10px 15px; font-weight: bold;">乡间田埂/河边 (需极谨慎)</div>
                                    <div style="padding: 15px; font-size: 14px; line-height: 1.6;">
                                        <p><strong>优点:</strong> 野菜种类可能更丰富。</p>
                                        <p><strong>缺点:</strong> <span style="color:red;">极易误入私人土地或农田！可能存在农药污染！安全风险高！</span></p>
                                        <p><strong>建议:</strong> 除非非常熟悉当地情况且确认安全、合法，否则不推荐。</p>
                                    </div>
                                </div>

                                 <div style="background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);">
                                    <div style="background-color: #90B77D; color: white; padding: 10px 15px; font-weight: bold;">有组织的活动/农场体验</div>
                                    <div style="padding: 15px; font-size: 14px; line-height: 1.6;">
                                        <p><strong>优点:</strong> 有专人指导，安全性高，合法合规，兼具社交性。</p>
                                        <p><strong>缺点:</strong> 可能需要付费，时间地点固定。</p>
                                        <p><strong>途径:</strong> 关注本地生活/户外社群发布的信息。</p>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px; border-left: 4px solid red;">
                                <h3 style="color: red;">安全与责任</h3>
                                <ul>
                                    <li><strong>核实信息:</strong> 不要轻信网络攻略，务必查询官方信息或咨询当地管理部门。</li>
                                    <li><strong>结伴而行:</strong> 尤其是去偏远地区。</li>
                                    <li><strong>告知行程:</strong> 让家人或朋友知道你的去向和预计返回时间。</li>
                                    <li><strong>环境负责:</strong> 不留垃圾，不破坏植被，尊重当地生态和居民。</li>
                                    <li><strong>量力而行:</strong> 根据自身体力选择合适的地点和时长。</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `
            },
             // 第8页：上海常见野菜图鉴
            {
                title: "实践指南",
                subtitle: "上海常见野菜图鉴 (仅供参考)",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box" style="border-left: 5px solid red;">
                                <h3 style="color: red;">【极重要】安全警告！！！</h3>
                                <p><strong>野菜辨识关乎生命安全！</strong> 很多有毒植物与可食用野菜外观极为相似！</p>
                                <ul>
                                    <li><strong>不认识 = 不采 = 不吃！</strong> 这是铁律！</li>
                                    <li><strong>100% 确定原则：</strong> 只有在你完全确认无误的情况下才能采集。</li>
                                    <li><strong>App 仅供参考：</strong> 植物识别 App 可能出错，不能作为唯一依据。</li>
                                    <li><strong>请教专家：</strong> 向有经验的长辈或植物专家请教是最可靠的方式。</li>
                                    <li><strong>图鉴对比：</strong> 仔细核对可靠植物图鉴的形态特征（根、茎、叶、花、果实）。</li>
                                    <li><strong>误食风险：</strong> 误食有毒植物可能导致呕吐、腹泻、神经系统损伤甚至死亡！切勿冒险！</li>
                                </ul>
                                <p><strong>本图鉴仅为科普介绍，不能作为采食依据！一切后果需自行承担！</strong></p>
                            </div>

                            <div class="plant-gallery">
                                <div class="plant-card">
                                    <div class="plant-image" title="荠菜示意图">🌱</div>
                                    <div class="plant-info">
                                        <div class="plant-name">荠菜 (Jì Cài)</div>
                                        <div class="plant-description">
                                            <p><strong>季节:</strong> 早春 (2-4月)</p>
                                            <p><strong>特征:</strong> 基生叶莲座状，羽状分裂，边缘有不规则锯齿。茎生叶较小。开白色小花，花瓣4。果实是特征性的倒三角形（或心形）。常见于田野、路边。</p>
                                            <p><strong>易混淆:</strong> 某些堇菜、碎米荠等，需仔细看叶形和花果。</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="plant-card">
                                    <div class="plant-image" title="马兰头示意图">🌿</div>
                                    <div class="plant-info">
                                        <div class="plant-name">马兰头 (Mǎ Lán Tóu)</div>
                                         <div class="plant-description">
                                            <p><strong>季节:</strong> 春季 (3-5月)</p>
                                            <p><strong>特征:</strong> 多年生草本。根状茎有匍枝。基生叶匙形或倒披针形，边缘有粗齿或羽裂。茎生叶较小。头状花序，舌状花淡紫色。</p>
                                            <p><strong>易混淆:</strong> 需注意与其他菊科植物区分，嫩苗期尤其要小心。</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="plant-card">
                                    <div class="plant-image" title="蒲公英示意图">🌼</div>
                                     <div class="plant-info">
                                        <div class="plant-name">蒲公英 (Pú Gōng Yīng)</div>
                                         <div class="plant-description">
                                            <p><strong>季节:</strong> 春秋季，春季最佳</p>
                                            <p><strong>特征:</strong> 多年生草本。叶莲座状平铺，叶片倒披针形，边缘羽状深裂或波状齿。花葶单一，顶生黄色头状花序。果实有白色冠毛（降落伞）。</p>
                                            <p><strong>易混淆:</strong> 多种菊科植物形态相似，如苦苣菜属，需仔细看叶裂方式和花。</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="plant-card">
                                    <div class="plant-image" title="马齿苋示意图">🍀</div>
                                     <div class="plant-info">
                                        <div class="plant-name">马齿苋 (Mǎ Chǐ Xiàn)</div>
                                         <div class="plant-description">
                                            <p><strong>季节:</strong> 夏季 (5-9月)</p>
                                            <p><strong>特征:</strong> 一年生肉质草本。茎匍匐或斜生，常带紫色。叶互生或近对生，叶片肉质，倒卵形，顶端圆钝或微凹。花小，黄色，通常白天开放时间短。</p>
                                            <p><strong>易混淆:</strong> 其他匍匐生长的植物，注意其肉质特性。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>野菜辨识基本方法</h3>
                                <ul>
                                    <li><strong>整体观察:</strong> 生长环境、植株形态（直立/匍匐/缠绕）。</li>
                                    <li><strong>看叶:</strong> 叶序（互生/对生/轮生/基生莲座状）、叶形（卵形/心形/披针形/羽状分裂等）、叶缘（全缘/锯齿/波状）、叶面（光滑/有毛/有粉）、有无叶柄。</li>
                                    <li><strong>看茎:</strong> 圆形/方形/有棱、颜色、有无毛、中空/实心。</li>
                                    <li><strong>看花 (若有):</strong> 花色、花瓣数目、花形（单独/花序）。</li>
                                    <li><strong>看果实/种子 (若有):</strong> 形状、颜色。</li>
                                    <li><strong>闻气味:</strong> 有无特殊气味（需谨慎，气味可能误导）。</li>
                                    <li><strong>查资料:</strong> 对比可靠图鉴、植物志描述。</li>
                                     <li><strong><span style="color:red;">绝不品尝辨认！</span></strong></li>
                                </ul>
                            </div>
                        </div>

                        <div class="column">
                            <div class="plant-gallery">
                                <div class="plant-card">
                                    <div class="plant-image" title="鱼腥草示意图">🌱</div>
                                    <div class="plant-info">
                                        <div class="plant-name">鱼腥草 (Yú Xīng Cǎo) / 折耳根</div>
                                        <div class="plant-description">
                                            <p><strong>季节:</strong> 春夏季 (3-6月)</p>
                                            <p><strong>特征:</strong> 具特殊腥味。茎下部伏地生根。叶片心形或宽卵形，纸质。托叶膜质。花小，穗状花序，总苞片白色（像花瓣）。根状茎白色，节明显。</p>
                                            <p><strong>注意:</strong> 气味是重要辨识特征，但也有其他植物有异味。</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="plant-card">
                                    <div class="plant-image" title="苦苣菜示意图">🌿</div>
                                    <div class="plant-info">
                                        <div class="plant-name">苦苣菜 (Kǔ Jù Cài) / 苣荬菜</div>
                                         <div class="plant-description">
                                            <p><strong>季节:</strong> 春秋季</p>
                                            <p><strong>特征:</strong> 一或二年生草本。茎直立。叶披针形或长圆状披针形，通常羽状深裂，边缘有尖齿，基部常抱茎。头状花序黄色。味苦。</p>
                                            <p><strong>易混淆:</strong> 与蒲公英及其他菊科植物相似，需仔细辨别叶形和基部。</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="plant-card">
                                    <div class="plant-image" title="车前草示意图">☘️</div>
                                    <div class="plant-info">
                                        <div class="plant-name">车前草 (Chē Qián Cǎo)</div>
                                         <div class="plant-description">
                                            <p><strong>季节:</strong> 春夏季 (嫩叶)</p>
                                            <p><strong>特征:</strong> 多年生草本。基生叶莲座状。叶片卵形或椭圆形，具明显弧形脉（3-7条）。花葶数个，顶生细长穗状花序。常见于路边、田埂。</p>
                                            <p><strong>注意:</strong> 食用通常取嫩叶。</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="plant-card">
                                    <div class="plant-image" title="灰灰菜示意图">🍀</div>
                                    <div class="plant-info">
                                        <div class="plant-name">灰灰菜 (Huī Huī Cài) / 藜</div>
                                         <div class="plant-description">
                                            <p><strong>季节:</strong> 春夏季 (嫩苗)</p>
                                            <p><strong>特征:</strong> 一年生草本。茎直立，有棱。幼苗或嫩叶背面常有白色粉状物（毛状体）。叶片菱状卵形至披针形，边缘有不规则锯齿。花小，绿色，穗状圆锥花序。</p>
                                            <p><strong>注意:</strong> 可能含光敏物质，部分人食用后避免日晒。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px; border-left: 4px solid red;">
                                <h3>安全采集操作指南</h3>
                                <ul>
                                    <li><strong>再次确认地点安全无污染。</strong></li>
                                    <li><strong>再次确认植物种类100%无误！</strong></li>
                                    <li><strong>适量采集:</strong> 只取所需，够一餐即可，切勿贪多。</li>
                                    <li><strong>保护性采集:</strong> 尽量用剪刀剪取地上部分，保留根系，让植物可以再生。避免连根拔起。</li>
                                    <li><strong>留种留苗:</strong> 不要采光一个区域的同种植物，保留一部分让其继续繁殖。</li>
                                    <li><strong>分类放置:</strong> 不同种类野菜分开装袋或篮，避免混淆。</li>
                                    <li><strong>及时处理:</strong> 野菜不易保存，尽快带回处理。</li>
                                    <li><strong>带走垃圾:</strong> 随身携带垃圾袋，不留下任何痕迹。</li>
                                </ul>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>知识分享与学习</h3>
                                <p>挖野菜的过程是极好的学习机会：</p>
                                <ul>
                                   <li>和同伴一起查阅资料，讨论辨识要点。</li>
                                   <li>分享各自的经验和家族传统（如果有）。</li>
                                   <li>用相机记录下植物的细节，方便后续学习。</li>
                                   <li>关注植物生长的环境，了解生态联系。</li>
                                </ul>
                                <p>这不仅能增长知识，也能在互动中加深了解。</p>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第9页：挖野菜约会装备清单
            {
                title: "实践指南",
                subtitle: "挖野菜约会装备清单",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>基础必备装备 (双方可分工携带)</h3>
                                <p>工欲善其事，必先利其器。合适的装备能提升安全性和体验感：</p>
                            </div>

                            <div class="equipment-list">
                                <div class="equipment-item">
                                    <div class="equipment-icon">🧤</div>
                                    <div>
                                        <div class="equipment-name">手套 (必备)</div>
                                        <div style="font-size: 12px; color: #666;">防刺、防脏、防过敏 (推荐园艺/劳保手套)</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">✂️</div>
                                    <div>
                                        <div class="equipment-name">小剪刀 (推荐)</div>
                                        <div style="font-size: 12px; color: #666;">剪取地上部分，比铲子更环保方便</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">🧺</div>
                                    <div>
                                        <div class="equipment-name">采集容器</div>
                                        <div style="font-size: 12px; color: #666;">透气的篮子、布袋、网兜 (塑料袋不透气)</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">📱</div>
                                    <div>
                                        <div class="equipment-name">手机 & App</div>
                                        <div style="font-size: 12px; color: #666;">满电！地图导航、植物识别App (辅助)</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">💧</div>
                                    <div>
                                        <div class="equipment-name">充足饮用水 (必备)</div>
                                        <div style="font-size: 12px; color: #666;">户外活动易失水，及时补充</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">🧴</div>
                                    <div>
                                        <div class="equipment-name">防晒用品</div>
                                        <div style="font-size: 12px; color: #666;">防晒霜、遮阳帽、太阳镜、冰袖等</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">🦟</div>
                                    <div>
                                        <div class="equipment-name">防蚊虫喷雾</div>
                                        <div style="font-size: 12px; color: #666;">野外蚊虫多，尤其夏秋季</div>
                                    </div>
                                </div>

                                <div class="equipment-item">
                                    <div class="equipment-icon">🩹</div>
                                    <div>
                                        <div class="equipment-name">简易急救包</div>
                                        <div style="font-size: 12px; color: #666;">创可贴、消毒湿巾、止痒膏药等</div>
                                    </div>
                                </div>
                                 <div class="equipment-item">
                                    <div class="equipment-icon">🧻</div>
                                    <div>
                                        <div class="equipment-name">纸巾/湿巾</div>
                                        <div style="font-size: 12px; color: #666;">清洁、擦汗、应急</div>
                                    </div>
                                </div>
                                 <div class="equipment-item">
                                    <div class="equipment-icon">🗑️</div>
                                    <div>
                                        <div class="equipment-name">垃圾袋 (必备)</div>
                                        <div style="font-size: 12px; color: #666;">带走自己产生的所有垃圾，无痕山林</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>进阶与可选装备</h3>
                                <p>根据活动时长、地点和个人需求选择性携带：</p>
                                <ul style="font-size: 14px;">
                                    <li><strong>小铲子:</strong> 如需挖根茎类野菜 (请谨慎使用)。</li>
                                    <li><strong>野菜图鉴书籍:</strong> 可靠的纸质参考。</li>
                                    <li><strong>便携野餐垫:</strong> 休息、用餐时更舒适。</li>
                                    <li><strong>保温杯:</strong> 带热茶或冷饮。</li>
                                    <li><strong>小零食/午餐:</strong> 补充能量，增加约会乐趣。</li>
                                    <li><strong>充电宝:</strong> 保证手机电量。</li>
                                    <li><strong>相机:</strong> 记录美好瞬间。</li>
                                    <li><strong>备用袜子/小毛巾:</strong> 应对潮湿或出汗。</li>
                                    <li><strong>雨具:</strong> 轻便雨衣或雨伞。</li>
                                </ul>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>着装建议：舒适防护为主</h3>
                                <p>户外活动，着装要考虑实用性、防护性和一定的得体性：</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">着装要点与建议</div>
                                <div style="padding:0;">
                                <table class="comparison-table">
                                   <thead>
                                        <tr>
                                            <th>部位</th>
                                            <th>建议</th>
                                            <th>理由/注意</th>
                                        </tr>
                                   </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>上衣</strong></td>
                                            <td><strong>长袖</strong> (透气速干或棉质)</td>
                                            <td>防晒、防刮、防蚊虫叮咬。</td>
                                        </tr>
                                        <tr>
                                            <td><strong>裤子</strong></td>
                                            <td><strong>长裤</strong> (耐磨、宽松舒适)</td>
                                            <td>同上。避免短裤短裙，易受伤或叮咬。</td>
                                        </tr>
                                        <tr>
                                            <td><strong>鞋子</strong></td>
                                            <td><strong style="color:red;">防滑、防水/透气、舒适</strong></td>
                                            <td><strong>极其重要！</strong>推荐徒步鞋、防滑运动鞋、雨靴(湿地)。<strong>绝不能穿</strong>凉鞋、拖鞋、高跟鞋！</td>
                                        </tr>
                                        <tr>
                                            <td><strong>袜子</strong></td>
                                            <td>吸汗、舒适 (建议中长筒)</td>
                                            <td>保护脚踝，防止异物、小虫进入。</td>
                                        </tr>
                                        <tr>
                                            <td><strong>帽子</strong></td>
                                            <td>宽檐帽/鸭舌帽</td>
                                            <td>物理防晒，遮阳。</td>
                                        </tr>
                                         <tr>
                                            <td><strong>颜色</strong></td>
                                            <td>浅色系 (不易吸热/易发现蜱虫)，或耐脏色系</td>
                                            <td>避免过于鲜艳 (可能招蜂引蝶)。</td>
                                        </tr>
                                         <tr>
                                            <td><strong>其他</strong></td>
                                            <td>可备薄外套/皮肤衣应对温差</td>
                                            <td>根据季节和天气调整。</td>
                                        </tr>
                                    </tbody>
                                </table>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px; border-left: 4px solid red;">
                                <h3><span style="color: red;">再次强调：鞋子！鞋子！鞋子！</span></h3>
                                <p>一双不合适的鞋子足以毁掉整个约会体验，甚至带来安全风险。</p>
                                <ul>
                                    <li><strong>抓地力是关键:</strong> 野外地面复杂，防滑至关重要。</li>
                                    <li><strong>保护性要足够:</strong> 能保护脚踝和脚趾免受刮擦和撞击。</li>
                                    <li><strong>舒适度不可少:</strong> 长时间行走，不合脚的鞋是折磨。</li>
                                    <li><strong>防水/透气平衡:</strong> 根据天气和地形选择，潮湿环境防水优先，干燥炎热透气优先。</li>
                                </ul>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>准备装备的社交意义</h3>
                                <ul>
                                    <li><strong>体现细心与靠谱:</strong> 准备充分能给对方留下好印象。</li>
                                    <li><strong>创造互动机会:</strong> 分享多余的装备（如手套、水、零食）是自然的互动方式。</li>
                                    <li><strong>展现合作意愿:</strong> 事先沟通好谁带什么，体现团队精神。</li>
                                    <li><strong>避免尴尬:</strong> 避免因缺少必要装备导致活动不便或提前结束。</li>
                                </ul>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>出发前检查清单 (Checklist)</h3>
                                <div style="background-color: #F5F5F5; padding: 15px; border-radius: 5px; font-size: 14px;">
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 合适的鞋子和衣物 (穿戴好)</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 手套</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 剪刀/小工具</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 采集容器</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 手机 (满电) & 充电宝 (可选)</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 充足饮用水</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 防晒用品</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 防蚊虫喷雾</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 纸巾/湿巾</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 简易急救包</label>
                                     <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 垃圾袋</label>
                                    <label style="display: block; margin-bottom: 8px;"><input type="checkbox" style="margin-right: 10px;"> 身份证明/少量现金</label>
                                    <label style="display: block;"><input type="checkbox" style="margin-right: 10px;"> 食物/图鉴/野餐垫等 (可选)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第10页：挖野菜约会互动指南
            {
                title: "实践指南",
                subtitle: "挖野菜约会互动指南：自然地靠近",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>挖野菜约会的自然流程与互动点</h3>
                                <p>一次成功的挖野菜约会，互动是核心。以下流程仅供参考，关键在于自然、放松、享受过程：</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">流程节点 & 互动机会</div>
                                <div style="width: 100%; padding: 20px;">
                                    <div class="process-flow">
                                        <div class="process-step">
                                            <div class="step-number">1</div>
                                            <div class="step-content">
                                                <strong>碰头与出发</strong>
                                                <p>准时、微笑、轻松问候。确认装备，简单聊聊对今天的期待或了解。</p>
                                                <p style="font-size: 12px; color: #666;"><i>观察点: 守时、准备情况、初步沟通风格。</i></p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">2</div>
                                            <div class="step-content">
                                                <strong>边走边探索</strong>
                                                <p>前往目的地的路上或寻找野菜时，聊聊路上的风景、对环境的感受、过往的户外经历等。</p>
                                                <p style="font-size: 12px; color: #666;"><i>互动点: 打开话题，观察对方的兴趣点和对自然的态度。</i></p>
                                            </div>
                                        </div>
                                         <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">3</div>
                                            <div class="step-content">
                                                <strong>发现与辨识 (核心环节)</strong>
                                                <p>共同寻找，分享发现 ("快看这是什么!")。一起查App/图鉴，讨论特征，分享已知知识 (即使很少)。</p>
                                                 <p style="font-size: 12px; color: #666;"><i>互动点: 观察力、合作、求知欲、分享意愿、处理不确定性的方式 (认错/谨慎)。</i></p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">4</div>
                                            <div class="step-content">
                                                <strong>采集与协作</strong>
                                                <p>确认后一起采集。交流手法 ("这样剪比较好?")，帮忙拿东西，聊聊小时候的相似记忆或食物偏好。</p>
                                                 <p style="font-size: 12px; color: #666;"><i>互动点: 动手能力、细心度、环保意识、生活经验、价值观。</i></p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">5</div>
                                            <div class="step-content">
                                                <strong>休息与分享</strong>
                                                <p>找个舒适地方坐下休息。分享水和零食 (体现体贴)。聊聊刚才的发现，或更深入地聊工作、生活、兴趣爱好。</p>
                                                 <p style="font-size: 12px; color: #666;"><i>互动点: 体贴度、生活情趣、沟通深度、倾听能力。</i></p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">6</div>
                                            <div class="step-content">
                                                <strong>整理与结束</strong>
                                                <p>一起整理收获，清理现场垃圾。讨论野菜如何处理，为后续联系埋下伏笔。愉快、真诚地告别。</p>
                                                 <p style="font-size: 12px; color: #666;"><i>互动点: 责任心、计划性、是否为未来互动创造机会。</i></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>破冰与话题建议 (源于活动本身)</h3>
                                <p>不必尬聊，围绕活动本身就能找到很多话题：</p>
                                <ul>
                                    <li><strong>关于野菜:</strong> "你认识这个吗？" "这个App靠谱吗？" "你吃过XX野菜吗？什么味道？" "小时候挖过野菜吗？"</li>
                                    <li><strong>关于过程:</strong> "这里风景不错！" "刚才差点滑倒！" "找了半天终于找到了！" "你手套/剪刀挺好用。"</li>
                                    <li><strong>关于后续:</strong> "这么多荠菜可以包饺子了！你会包吗？" "马兰头凉拌最好吃，你觉得呢？" "回去怎么洗才干净？"</li>
                                    <li><strong>关于感受:</strong> "感觉空气真好。" "这样活动挺解压的。" "没想到上海还有这样的地方。"</li>
                                    <li><strong>延伸话题:</strong> "平时周末喜欢做什么？" "也喜欢户外吗？" "喜欢做饭吗？"</li>
                                </ul>
                                <p>关键是<strong>真诚的好奇和分享</strong>。</p>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>让互动更愉快的 Tips</h3>
                                <ul>
                                    <li><strong>保持微笑和眼神交流:</strong> 传递友好和专注。</li>
                                    <li><strong>积极倾听并回应:</strong> 对对方的话题表示兴趣，提出相关问题。</li>
                                    <li><strong>主动分享，但别变"讲座":</strong> 分享你的知识和经历，但留给对方说话空间。</li>
                                    <li><strong>展现好奇心和学习态度:</strong> 不懂就问，乐于学习新事物。</li>
                                    <li><strong>适度赞美:</strong> 真诚地欣赏对方的优点或发现 ("你眼神真好！" "这个你都知道！")。</li>
                                    <li><strong>提供小帮助:</strong> 递水、帮忙拿东西、提醒注意脚下。</li>
                                    <li><strong>接受帮助并表示感谢:</strong> 良好的互动是双向的。</li>
                                    <li><strong>共享"成就时刻":</strong> 一起为找到稀有野菜或克服小困难而开心。</li>
                                    <li><strong>拍照请求许可:</strong> 想拍合影或对方照片时先询问。</li>
                                    <li><strong>保持轻松幽默:</strong> 遇到小尴尬或困难时，轻松化解。</li>
                                </ul>
                            </div>

                             <div class="info-box" style="margin-top: 20px; border-left: 4px solid #D95D39;">
                                <h3>真实互动案例</h3>
                                <blockquote>
                                <p>"我们第一次约会去松江挖野菜...（略）...回家后，我用采集的荠菜做了饺子，发照片给她，她也分享了她做的蒲公英沙拉。这种围绕共同经历的后续互动，感觉特别自然，没有刻意的'汇报'感，关系也顺理成章地近了一步。"</p>
                                <footer>——张先生，30岁，教师 (续)</footer>
                                </blockquote>
                            </div>

                            <div class="info-box" style="margin-top: 20px; border-left: 4px solid red;">
                                <h3 style="color: red;">互动中的注意要点</h3>
                                <ul>
                                    <li><strong>安全永远优先于互动:</strong> 不要为了找话题或表现而忽略安全。</li>
                                    <li><strong>尊重界限:</strong> 注意身体距离和话题尺度，避免过度打探隐私。</li>
                                    <li><strong>避免抱怨和负能量:</strong> 保持积极乐观的态度。</li>
                                    <li><strong>不要过度表现或说教:</strong> 真诚最重要，不懂装懂很减分。</li>
                                    <li><strong>关注对方状态:</strong> 注意对方是否疲劳、口渴、不适，及时调整。</li>
                                    <li><strong>环保意识贯穿始终:</strong> 不乱丢垃圾，不破坏环境，体现素养。</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第11页：野菜烹饪与分享
            {
                title: "实践指南",
                subtitle: "野菜烹饪与分享：美味的延续",
                content: `
                    <div class="content-columns">
                        <div class="column">
                            <div class="info-box">
                                <h3>从田野到餐桌：体验的完整闭环</h3>
                                <p>挖野菜的乐趣，在亲手将收获变成美食并分享时达到高潮。这也是延续互动、深化了解的好机会。</p>
                                <p style="color: red; font-weight: bold;">安全第一：烹饪前务必再次确认野菜种类无误，并彻底清洗干净！不确定的坚决丢弃！</p>
                            </div>

                            <div class="chart">
                                <div class="chart-title">野菜处理关键步骤</div>
                                <div style="width: 100%; padding: 20px;">
                                    <div class="process-flow">
                                        <div class="process-step">
                                            <div class="step-number">1</div>
                                            <div class="step-content">
                                                <strong>复检与择洗</strong>
                                                <p>回家后仔细检查，去除混入的杂草、黄叶、老根、泥沙。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number" style="background-color: red;">2</div>
                                            <div class="step-content">
                                                <strong>彻底清洗 (最关键!)</strong>
                                                <p>用流水多次冲洗。对根部泥沙多的，可先浸泡松动泥土再冲洗。建议用淡盐水或小苏打水浸泡15-30分钟，进一步去除可能的虫卵和残留。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">3</div>
                                            <div class="step-content">
                                                <strong>焯水处理 (按需)</strong>
                                                <p><strong>目的:</strong> 去除草酸、涩味、部分农残(如有)、杀灭虫卵、使颜色更鲜艳。</p>
                                                <p><strong>建议焯水:</strong> 荠菜、马齿苋、灰灰菜、香椿等。沸水中加少许盐和油，焯烫几十秒到1分钟 (视菜量和种类)，捞出立即过凉水保持口感和颜色。</p>
                                                <p><strong>通常不焯水:</strong> 蒲公英嫩叶 (生食或直接炒)、马兰头 (也可焯)。</p>
                                            </div>
                                        </div>
                                         <div class="process-arrow"></div>
                                         <div class="process-step">
                                            <div class="step-number">4</div>
                                            <div class="step-content">
                                                <strong>挤干与切配</strong>
                                                <p>焯水后的野菜需挤干多余水分，否则影响口感和调味。然后根据菜式需要切段、切末。</p>
                                            </div>
                                        </div>
                                        <div class="process-arrow"></div>
                                        <div class="process-step">
                                            <div class="step-number">5</div>
                                            <div class="step-content">
                                                <strong>发挥创意烹饪</strong>
                                                <p>根据野菜风味和口感选择合适的烹饪方式：凉拌、清炒、煮汤、蒸制、做馅、泡茶等。</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>常见野菜基础烹饪法</h3>
                                <div style="padding:0;">
                                <table class="comparison-table">
                                    <thead>
                                        <tr>
                                            <th>野菜</th>
                                            <th>基础做法推荐</th>
                                            <th>风味特点</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>荠菜</td>
                                            <td><strong>做馅</strong> (馄饨/饺子/春卷)、<strong>煮羹</strong> (豆腐羹/鱼片羹)</td>
                                            <td>清香浓郁，略带甘甜</td>
                                        </tr>
                                        <tr>
                                            <td>马兰头</td>
                                            <td><strong>凉拌</strong> (配香干是绝配)、清炒</td>
                                            <td>清爽脆嫩，有独特香气</td>
                                        </tr>
                                        <tr>
                                            <td>蒲公英</td>
                                            <td>凉拌 (嫩叶)、蘸酱生食、炒鸡蛋、泡水</td>
                                            <td>微苦回甘，清热</td>
                                        </tr>
                                        <tr>
                                            <td>马齿苋</td>
                                            <td>凉拌 (蒜蓉/麻酱)、炒鸡蛋、做馅 (包子/饼)</td>
                                            <td>滑润微酸，爽口</td>
                                        </tr>
                                        <tr>
                                            <td>鱼腥草</td>
                                            <td>凉拌 (根/叶，西南常见)、炒腊肉、煲汤 (去腥味)</td>
                                            <td>特殊“鱼腥”味，争议较大</td>
                                        </tr>
                                        <tr>
                                            <td>车前草</td>
                                            <td>煮水代茶、嫩叶煮汤/煮粥</td>
                                            <td>味淡，药用价值更受关注</td>
                                        </tr>
                                         <tr>
                                            <td>灰灰菜</td>
                                            <td>凉拌、清炒 (需焯水处理好)</td>
                                            <td>口感较软糯，处理不好易涩</td>
                                        </tr>
                                    </tbody>
                                </table>
                                </div>
                                <p style="font-size: 12px; color: #666; margin-top: 10px;">* 口味和做法因人而异，以上仅为常见参考。</p>
                            </div>
                        </div>

                        <div class="column">
                            <div class="info-box">
                                <h3>分享美食，延续互动</h3>
                                <p>如何将烹饪变成关系催化剂？</p>
                                <ul>
                                    <li><strong>线上分享，开启话题:</strong>
                                        <ul>
                                            <li>拍下成品照片/小视频发给对方。</li>
                                            <li>描述一下味道如何，做法心得。</li>
                                            <li>可以问问对方采的野菜做了什么？味道如何？</li>
                                            <li>自然地提出："下次有机会可以一起做/尝尝我做的"。</li>
                                        </ul>
                                    </li>
                                    <li><strong>线下共厨 (理想模式):</strong>
                                        <ul>
                                            <li>如果感觉不错，可以提议找个时间一起做饭。</li>
                                            <li>共同处理食材、分工合作、交流厨艺。</li>
                                            <li>这是非常生活化、能快速增进了解的方式。</li>
                                            <li>可以从简单的菜式开始，降低压力。</li>
                                        </ul>
                                    </li>
                                    <li><strong>小型"野菜宴":</strong>
                                        <ul>
                                            <li>如果双方都收获颇丰，可以各自做几道拿手野菜菜，约在一起品尝分享。</li>
                                            <li>也可以邀请1-2位共同的朋友，营造轻松氛围。</li>
                                        </ul>
                                    </li>
                                     <li><strong>暖心赠送:</strong>
                                        <ul>
                                            <li>如果做的比较多，可以用干净的餐盒装一小份送给对方尝尝（确保卫生！）。</li>
                                            <li>体现你的用心和分享意愿。</li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>

                            <div class="recipe-card">
                                <div class="recipe-title">经典：荠菜鲜肉大馄饨</div>
                                <div class="recipe-content">
                                    <div class="recipe-section">
                                        <div class="recipe-section-title">馅料材料</div>
                                        <ul>
                                            <li>荠菜 (焯水挤干切末)</li>
                                            <li>猪肉糜 (肥瘦相间)</li>
                                            <li>鸡蛋 (可选，增加嫩滑)</li>
                                            <li>葱姜末</li>
                                            <li>盐、生抽、料酒、白胡椒粉、香油</li>
                                            <li>(可选) 少量笋丁、香菇丁增加口感</li>
                                        </ul>
                                    </div>
                                    <div class="recipe-section">
                                        <div class="recipe-section-title">做法</div>
                                        <ol>
                                            <li>肉糜中加入葱姜末、调味料和鸡蛋(如用)，顺一个方向搅拌上劲。</li>
                                            <li>分次加入少量水或高汤，继续搅拌至肉馅吸饱水分。</li>
                                            <li>加入荠菜末和可选配料，淋入香油拌匀。</li>
                                            <li>取馄饨皮包入馅料。</li>
                                            <li>下锅煮熟，碗中可放紫菜、虾皮、榨菜、葱花做汤底。</li>
                                        </ol>
                                    </div>
                                    <div class="recipe-section">
                                        <div class="recipe-section-title">互动点</div>
                                        <p>包馄饨/饺子本身就是很好的协作活动。可以比赛谁包得快/好看，交流家乡的不同包法。分享煮好的成品照片，诱惑力十足！</p>
                                    </div>
                                </div>
                            </div>

                            <div class="recipe-card" style="margin-top: 20px;">
                                <div class="recipe-title">清新：香干拌马兰头</div>
                                <div class="recipe-content">
                                    <div class="recipe-section">
                                        <div class="recipe-section-title">材料</div>
                                        <ul>
                                            <li>马兰头 (焯水/不焯水皆可，挤干切末)</li>
                                            <li>五香豆干 (切丁)</li>
                                            <li>盐</li>
                                            <li>糖 (少量提鲜)</li>
                                            <li>香油</li>
                                            <li>(可选) 蒜末、生抽</li>
                                        </ul>
                                    </div>
                                    <div class="recipe-section">
                                        <div class="recipe-section-title">做法</div>
                                        <ol>
                                            <li>将马兰头末和豆干丁混合。</li>
                                            <li>加入盐、糖、香油 (及可选调料) 拌匀。</li>
                                            <li>静置5-10分钟入味即可。</li>
                                        </ol>
                                    </div>
                                    <div class="recipe-section">
                                        <div class="recipe-section-title">互动点</div>
                                        <p>经典家常菜，制作简单。可以讨论是否焯水、调味偏好。作为线下共厨的快手凉菜也很合适。</p>
                                    </div>
                                </div>
                            </div>

                            <div class="info-box" style="margin-top: 20px;">
                                <h3>烹饪分享的意义</h3>
                                <p>这不仅仅是做饭吃饭，更是：</p>
                                <ul>
                                    <li><strong>共同劳动的延续:</strong> 将采集的成果转化为美味。</li>
                                    <li><strong>生活能力的展示:</strong> 体现对生活的热情和动手能力。</li>
                                    <li><strong>口味偏好的了解:</strong> 通过分享和品尝了解彼此的饮食习惯。</li>
                                    <li><strong>创造新的共同记忆:</strong> "我们一起做的/吃的这道菜"。</li>
                                    <li><strong>为下次约会铺垫:</strong> 自然过渡到更生活化的交往场景。</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `
            },
            // 第12页：结语与展望
             {
                title: "结语与展望",
                subtitle: "重新定义都市亲密关系",
                content: `
                    <div class="conclusion-page">
                        <div class="conclusion-icon">
                           <span style="transform: rotate(-15deg);">🌿</span>🤝<span style="transform: rotate(15deg);">🏙️</span>
                        </div>
                        <div class="conclusion-title">结语：都市觅“野”，寻回本真</div>
                        <div class="conclusion-text">
                            <p>在高度现代化、快节奏运行的上海，“挖野菜约会”如同一缕不期而遇的田野气息，悄然弥散在都市青年的社交圈层。这股看似“复古”的潮流，并非简单的猎奇或跟风，而是深刻折射出当代都市青年在社交焦虑、消费迷思和自然缺失等多重背景下，对<strong style="color:#D95D39;">真实连接、具身体验、传统智慧</strong>以及<strong style="color:#D95D39;">非功利性交往</strong>的内在呼唤与主动求索。</p>
                            <p>从田野间的并肩寻觅、知识共享，到厨房里的合作烹饪、成果分享，“挖野菜约会”构建了一个完整的“行动交互”闭环。它提供了一个低成本、低压力、高互动的场域，让参与者得以在相对自然放松的状态下，观察、了解、磨合，建立起基于共同实践而非符号消费的情感联结。这恰是对当下程式化、表演化、消费化的都市约会模式的一种温和而有力的反拨。</p>
                            <p>当年轻的都市灵魂暂时放下手机，弯腰亲近土地，用指尖触摸真实的植物脉络时，他们寻找的不仅仅是几棵野菜，更是在钢筋水泥的丛林中，试图寻回失落的自然节律、人际温情与生活本真。</p>
                        </div>

                         <div class="info-box">
                            <h3 style="color: #2E4057;">未来趋势与思考</h3>
                            <ul>
                                <li><strong>模式扩散与演变:</strong> 类似“挖野菜”的“行动交互型”约会/社交活动 (如城市农耕、手作体验、社区营造参与等) 可能持续涌现并多元化。</li>
                                <li><strong>社群化与组织化:</strong> 可能出现更多相关兴趣社群、体验活动组织方，甚至轻度商业化服务。</li>
                                <li><strong>面临的挑战:</strong>
                                    <ul>
                                        <li><strong>安全风险:</strong> 野外环境复杂，误采误食风险需警惕。</li>
                                        <li><strong>生态伦理:</strong> 如何平衡体验需求与环境保护？过度采集问题。</li>
                                        <li><strong>规范与引导:</strong> 如何避免其流于形式或被过度商业化绑架？</li>
                                    </ul>
                                </li>
                                <li><strong>深层启示:</strong> 促使我们反思城市公共空间的功能、现代人的自然连接、传统知识的当代价值以及更健康、本真的亲密关系模式。</li>
                            </ul>
                         </div>

                        <div class="cover-footer">
                            上海都市文化研究所 · 2023-2025 现象观察
                        </div>
                    </div>
                `
            }
        ];

        // --- JavaScript Logic ---
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Element References
            const bookContainer = document.getElementById('book-container');
            const pageIndicator = document.getElementById('page-indicator');
            const prevButton = document.getElementById('prev-button');
            const nextButton = document.getElementById('next-button');
            const tocButton = document.getElementById('toc-btn');
            const tocPanel = document.getElementById('toc-panel');
            const tocContent = document.getElementById('toc-content');

            // State
            let currentPage = 0;
            const totalPages = pages.length;

            /**
             * Renders the specified page content into the book container.
             * @param {number} pageIndex - The index of the page to render.
             */
            function renderPage(pageIndex) {
                const pageData = pages[pageIndex];
                while (bookContainer.firstChild) {
                    bookContainer.removeChild(bookContainer.firstChild);
                }

                const pageElement = document.createElement('div');
                pageElement.classList.add('page');
                pageElement.id = `page-${pageIndex + 1}`;

                let pageHTML = '';

                 if (pageIndex === 0) {
                    pageHTML = pageData.content;
                } else if (pageIndex === totalPages - 1) {
                     pageHTML = pageData.content;
                } else {
                    pageHTML = `
                        <div class="page-header">
                            <span class="page-number">${pageIndex + 1} / ${totalPages}</span>
                            <span class="page-title">${pageData.title || ''}</span>
                        </div>
                        <div class="page-content">
                            ${pageData.subtitle ? `<h2 class="page-subtitle">${pageData.subtitle}</h2>` : ''}
                            ${pageData.content}
                        </div>
                        <div class="page-footer">上海挖野菜约会现象研究 · 第 ${pageIndex + 1} 页</div>
                    `;
                }


                pageElement.innerHTML = pageHTML;
                bookContainer.appendChild(pageElement);

                // Find the scrollable element
                let scrollableElement = null;
                if (pageIndex > 0 && pageIndex < totalPages - 1) {
                    scrollableElement = pageElement.querySelector('.page-content');
                } else {
                    scrollableElement = pageElement; // Cover and conclusion scroll .page
                }


                requestAnimationFrame(() => {
                    pageElement.classList.add('active');
                     if (scrollableElement) {
                         scrollableElement.scrollTop = 0;
                     }
                });

                pageIndicator.innerText = `${pageIndex + 1} / ${totalPages}`;
                prevButton.disabled = (pageIndex === 0);
                nextButton.disabled = (pageIndex === totalPages - 1);
                updateTocHighlight(pageIndex);
            }

             /**
             * Generates the Table of Contents list items.
             */
            function generateToc() {
                tocContent.innerHTML = '';
                pages.forEach((page, index) => {
                    const tocItem = document.createElement('div');
                    tocItem.className = 'toc-item';
                    let tocText = `${index + 1}. ${page.title}`;
                    if (page.subtitle && index !== 0 && index !== totalPages -1 ) {
                         let shortSubtitle = page.subtitle.length > 20 ? page.subtitle.substring(0, 20) + '...' : page.subtitle;
                        tocItem.innerHTML = `<div>${index + 1}. ${page.title}</div><div style="font-size: 0.85em; opacity: 0.8; padding-left: 1.2em;">${shortSubtitle}</div>`;
                    } else {
                         tocItem.innerText = tocText;
                    }

                    tocItem.title = `${index + 1}. ${page.title}${page.subtitle ? ': ' + page.subtitle : ''}`;
                    tocItem.addEventListener('click', (e) => {
                         e.stopPropagation();
                         goToPage(index);
                    });
                    tocContent.appendChild(tocItem);
                });
            }

            /**
             * Updates the highlighting of the active item in the TOC.
             * @param {number} activeIndex - The index of the currently active page.
             */
            function updateTocHighlight(activeIndex) {
                const tocItems = tocContent.querySelectorAll('.toc-item');
                tocItems.forEach((item, index) => {
                    if (index === activeIndex) {
                        item.classList.add('active');
                         if (tocPanel.classList.contains('open')) {
                            setTimeout(() => item.scrollIntoView({ behavior: 'smooth', block: 'nearest' }), 50);
                        }
                    } else {
                        item.classList.remove('active');
                    }
                });
            }

            /** Navigates to the next page. */
            function nextPage() {
                if (currentPage < totalPages - 1) {
                    currentPage++;
                    renderPage(currentPage);
                }
            }

            /** Navigates to the previous page. */
            function prevPage() {
                if (currentPage > 0) {
                    currentPage--;
                    renderPage(currentPage);
                }
            }

            /**
             * Navigates directly to a specific page index.
             * @param {number} pageIndex - The index of the page to go to.
             */
            function goToPage(pageIndex) {
                if (pageIndex >= 0 && pageIndex < totalPages && pageIndex !== currentPage) {
                    currentPage = pageIndex;
                    renderPage(currentPage);
                }
                 tocPanel.classList.remove('open');
            }

            /** Toggles the visibility of the TOC panel. */
            function toggleToc() {
                tocPanel.classList.toggle('open');
                 if (tocPanel.classList.contains('open')) {
                     updateTocHighlight(currentPage);
                 }
            }


            // --- Initial Setup ---
            generateToc();
            renderPage(currentPage); // Render the initial page

            // --- Event Listeners ---
            nextButton.addEventListener('click', nextPage);
            prevButton.addEventListener('click', prevPage);
            tocButton.addEventListener('click', toggleToc);

            // Keyboard Navigation
            document.addEventListener('keydown', function(e) {
                 if (document.activeElement.tagName === 'INPUT' ||
                     document.activeElement.tagName === 'TEXTAREA' ||
                     e.metaKey || e.ctrlKey || e.altKey) {
                     return;
                 }

                if (e.key === 'Escape' && tocPanel.classList.contains('open')) {
                     toggleToc();
                     return;
                }

                 if (tocPanel.classList.contains('open')) {
                    return;
                 }

                if (e.key === 'ArrowRight' || e.key === 'PageDown' || e.key === ' ') {
                    e.preventDefault();
                    nextPage();
                } else if (e.key === 'ArrowLeft' || e.key === 'PageUp') {
                    e.preventDefault();
                    prevPage();
                }
            });

            // Close TOC panel when clicking outside
            document.addEventListener('click', function(event) {
                if (tocPanel.classList.contains('open') &&
                    !tocPanel.contains(event.target) &&
                    !tocButton.contains(event.target)) {
                    tocPanel.classList.remove('open');
                }
            });

        }); // End DOMContentLoaded
    </script>

</body>
</html>