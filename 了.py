from xml.etree.ElementTree import Element, SubElement, tostring
from IPython.display import SVG, display
import xml.dom.minidom

# 定义节点
nodes = {
    "P1": {"label": "金融政策放松", "layer": "政策端", "x": 100, "y": 50, "data": "LPR 3.0%, M2增长 8.3%"},
    "F1": {"label": "资金错配：城投、理财", "layer": "金融端", "x": 300, "y": 50, "data": "再贷款1万亿"},
    "E1": {"label": "实体经济融资困难", "layer": "企业端", "x": 500, "y": 50, "data": "民企融资成本 > 5%"},
    "E2": {"label": "创新企业受限", "layer": "企业端", "x": 500, "y": 150, "data": "中小企业贷款占比 < 27%"},
    "C1": {"label": "消费信心下降", "layer": "消费端", "x": 300, "y": 250, "data": "CPI同比 -0.3%, 储蓄率 42%"},
    "C2": {"label": "内需持续低迷", "layer": "消费端", "x": 100, "y": 250, "data": "社零总额增速 2.3%"},
    "F2": {"label": "贫富差距扩大", "layer": "金融端", "x": 100, "y": 150, "data": "跨境投资同比 +40%"},
    "P2": {"label": "政策再放松", "layer": "政策端", "x": 100, "y": 0, "data": "准备金率 7.0%"},
}

# 定义连接
edges = [
    ("P1", "F1"),
    ("F1", "E1"),
    ("E1", "E2"),
    ("E1", "C1"),
    ("C1", "C2"),
    ("C2", "P2"),
    ("P2", "P1"),
    ("F1", "F2"),
    ("F2", "C1"),
]

# 创建SVG元素
svg = Element('svg', width='800', height='400', xmlns="http://www.w3.org/2000/svg")

# 定义颜色
layer_colors = {
    "政策端": "#ffd700",
    "金融端": "#87ceeb",
    "企业端": "#90ee90",
    "消费端": "#ffcccb"
}

# 画节点
for node_id, props in nodes.items():
    x, y = props["x"], props["y"]
    color = layer_colors[props["layer"]]
    group = SubElement(svg, 'g')
    SubElement(group, 'rect', x=str(x), y=str(y), width="180", height="50", rx="10", ry="10",
               fill=color, stroke="black")
    SubElement(group, 'text', x=str(x+90), y=str(y+20), text_anchor="middle", font_size="12").text = props["label"]
    SubElement(group, 'text', x=str(x+90), y=str(y+40), text_anchor="middle", font_size="10", fill="gray").text = props["data"]

# 画边（箭头线）
for from_id, to_id in edges:
    x1, y1 = nodes[from_id]["x"] + 90, nodes[from_id]["y"] + 25
    x2, y2 = nodes[to_id]["x"] + 90, nodes[to_id]["y"] + 25
    SubElement(svg, 'line', x1=str(x1), y1=str(y1), x2=str(x2), y2=str(y2),
               stroke="black", marker_end="url(#arrow)", stroke_width="2")

# 添加箭头定义
defs = SubElement(svg, 'defs')
marker = SubElement(defs, 'marker', id='arrow', markerWidth='10', markerHeight='10',
                    refX='5', refY='3', orient='auto', markerUnits='strokeWidth')
SubElement(marker, 'path', d='M0,0 L0,6 L9,3 z', fill='black')

# 美化XML
dom = xml.dom.minidom.parseString(tostring(svg).decode())
svg_code = dom.toprettyxml()

# 显示SVG
display(SVG(svg_code))
