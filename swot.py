import matplotlib.pyplot as plt

plt.rcParams['font.family'] = 'SimHei'  # 设置为支持中文的字体

# 模拟的数据
dimensions = ['A', 'B', 'C', 'D', 'E']
importance = [0.8, 0.6, 0.9, 0.4, 0.7]  # 各维度的重要性 (0-1 范围)
health_score = [0.2, -0.1, 0.3, -0.5, 0.1]  # 各维度的健康度 (-1到1)

# 设置象限图
plt.figure(figsize=(10, 6))

# 绘制各个维度
for i, dimension in enumerate(dimensions):
    plt.scatter(importance[i], health_score[i], s=100, label=f'{dimension}', alpha=0.6)

# 添加象限的轴线
plt.axhline(0, color='gray', lw=1, linestyle='--')  # 横轴
plt.axvline(0.5, color='gray', lw=1, linestyle='--')  # 纵轴，假定重要性超过0.5是高重要性

# 标注每个维度的点
for i, dimension in enumerate(dimensions):
    plt.text(importance[i] + 0.02, health_score[i] + 0.02, dimension, fontsize=10, ha='center', va='center')

# 给每个象限加上说明（包含不同的坐标点）
plt.text(0.75, 0.75, '高重要性 & 健康\n(保持优势)', fontsize=12, color='green', ha='center')  # 第一象限
plt.text(0.75, -0.75, '高重要性 & 不健康\n(重点改进)', fontsize=12, color='red', ha='center')  # 第四象限
plt.text(0.25, 0.75, '低重要性 & 健康\n(可降低优先级)', fontsize=12, color='blue', ha='center')  # 第二象限
plt.text(0.25, -0.75, '低重要性 & 不健康\n(低优先级改进)', fontsize=12, color='orange', ha='center')  # 第三象限

# 在每个象限内增加不同的坐标点，并说明其对应的情况
# 第一象限 (0.6, 0.5)
plt.scatter(0.6, 0.5, s=80, color='green', alpha=0.6)
plt.text(0.6 + 0.02, 0.5 + 0.02, '(0.6, 0.5)', fontsize=10, ha='center', va='center')

# 第二象限 (0.3, 0.6)
plt.scatter(0.3, 0.6, s=80, color='blue', alpha=0.6)
plt.text(0.3 + 0.02, 0.6 + 0.02, '(0.3, 0.6)', fontsize=10, ha='center', va='center')

# 第三象限 (0.3, -0.6)
plt.scatter(0.3, -0.6, s=80, color='orange', alpha=0.6)
plt.text(0.3 + 0.02, -0.6 + 0.02, '(0.3, -0.6)', fontsize=10, ha='center', va='center')

# 第四象限 (0.7, -0.4)
plt.scatter(0.7, -0.4, s=80, color='red', alpha=0.6)
plt.text(0.7 + 0.02, -0.4 + 0.02, '(0.7, -0.4)', fontsize=10, ha='center', va='center')

# 设置图的属性
plt.title('象限图：各维度健康度与重要性', fontsize=16)
plt.xlabel('重要性', fontsize=14)
plt.ylabel('健康度', fontsize=14)
plt.grid(True, linestyle='--', alpha=0.5)
plt.xlim(0, 1)
plt.ylim(-1, 1)

# 图例
plt.legend(title='维度', loc='upper left')

# 显示图形
plt.show()
