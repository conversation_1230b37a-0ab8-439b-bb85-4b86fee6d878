{"hub_name": "GPT架构中枢", "core_functions": [{"name": "GPT注册管理", "description": "统一管理所有GPT实例的身份配置、提示结构、记忆结构、可继承技能与授权范围"}, {"name": "任务意图解析器", "description": "接收用户自然语言输入并自动分类为：查询型、分析型、操作型、流程型等任务类型"}, {"name": "GPT分派调度器", "description": "根据任务类型、领域标签与当前状态，调用最合适的GPT实例或组合多个GPT联合执行"}, {"name": "技能继承与激活中心", "description": "动态检查当前GPT是否拥有所需技能，若无则从注册表中继承、激活技能并组合到调用链中"}, {"name": "执行追踪与反馈修正模块", "description": "记录每次交互的执行路径、结果与失败原因，作为后续优化GPT配置与技能升级的依据"}], "orchestration_flow": {"step_1": "用户发起任务请求", "step_2": "意图解析器判定任务类型", "step_3": "匹配最优GPT或组合多个GPT", "step_4": "加载相关身份配置 + 知识库 + 技能", "step_5": "执行任务，必要时调度Action或RAG", "step_6": "返回结果并记录全过程日志"}, "infrastructure_suggestion": {"部署形式": "可选本地化 / API 网关托管", "推荐技术": ["LangChain 或 Semantic Kernel 构建调度逻辑", "向量数据库（如Weaviate/FAISS）管理知识共享", "FastAPI + JSON-RPC 实现技能注册与继承分发", "SQLite/Notion/FireStore 记录运行轨迹与GPT配置状态"]}}