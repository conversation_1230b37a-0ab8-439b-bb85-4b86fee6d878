import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint

plt.rcParams['font.family'] = 'SimHei'  # 设置为支持中文的字体

# 定义模型中的变量索引
TNI, ECP, NTP, FR, ESI, IEC = range(6)

# 微分方程组
def model(y, t):
    dy = np.zeros(6)
    
    # 当前变量
    tni, ecp, ntp, fr, esi, iec = y
    
    # 回路影响关系 (线性简化建模 + 非线性抑制项)
    
    # R1: 技术优势提升升级优势，再触发核门槛跃升
    dy[TNI] = -0.1 * tni + 0.05 * fr + 0.02 * iec
    dy[ECP] = 0.3 * tni - 0.1 * ecp
    dy[NTP] = 0.4 * ecp - 0.2 * ntp
    
    # R2: 核门槛上升反向提升 IEC，IEC 促使 TNI 提升
    dy[IEC] = 0.3 * ntp - 0.05 * iec
    dy[TNI] += 0.2 * iec
    
    # R3: 战线动能由 TNI 驱动，也受 ESI 能源瓶颈压制
    dy[FR] = 0.2 * tni - 0.3 * (1 - esi) - 0.1 * fr
    
    # R4: 能源安全受 NTP 影响，高核风险带来油价上升 (间接削弱 ESI)
    dy[ESI] = -0.2 * ntp - 0.1 * esi
    
    return dy

# 初始状态：[TNI, ECP, NTP, FR, ESI, IEC]
y0 = [0.3, 0.2, 0.1, 0.1, 0.8, 0.2]

# 时间区间（120 天）
t = np.linspace(0, 120, 300)

# 积分求解
sol = odeint(model, y0, t)

# 绘图
plt.figure(figsize=(12, 6))
labels = ["技术非对称 TNI", "升级优势感知 ECP", "核门槛邻近 NTP", 
          "战线动能 FR", "能源安全 ESI", "信息环境控制 IEC"]
for i in range(6):
    plt.plot(t, sol[:, i], label=labels[i])

plt.title("美伊冲突主导反馈系统演化（120天）")
plt.xlabel("时间（天）")
plt.ylabel("状态变量（归一化值）")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
