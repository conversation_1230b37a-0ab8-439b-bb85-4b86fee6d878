<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deep Research Agents: 系统性研究与技术路线图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .nav {
            background: #2c3e50;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav li {
            margin: 0 1rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .nav a:hover {
            background: #34495e;
        }

        .content {
            padding: 2rem;
        }

        .section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5rem;
        }

        .section h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }

        .highlight-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .tech-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .tech-card h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .architecture-diagram {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            overflow-x: auto;
            margin: 2rem 0;
            border: 2px solid #e9ecef;
        }

        .svg-diagram {
            width: 100%;
            height: auto;
            max-width: 1000px;
            margin: 0 auto;
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem;
        }

        .footer-info {
            margin-top: 1rem;
            opacity: 0.8;
        }

        .footer-info-small {
            margin-top: 0.5rem;
            opacity: 0.8;
        }

        .footer-link {
            color: #74b9ff;
        }

        .icon {
            width: 24px;
            height: 24px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 0.5rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav ul {
                flex-direction: column;
                align-items: center;
            }
            
            .content {
                padding: 1rem;
            }
            
            .section {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Deep Research Agents: 系统性研究与技术路线图</h1>
            <p class="subtitle">基于大语言模型的深度研究智能体：架构、技术与应用前景</p>
        </header>

        <!-- Navigation -->
        <nav class="nav">
            <ul>
                <li><a href="#overview">论文概述</a></li>
                <li><a href="#definition">核心定义</a></li>
                <li><a href="#architecture">系统架构</a></li>
                <li><a href="#components">核心组件</a></li>
                <li><a href="#applications">工业应用</a></li>
                <li><a href="#benchmarks">评估基准</a></li>
                <li><a href="#challenges">挑战与展望</a></li>
            </ul>
        </nav>

        <!-- Content -->
        <main class="content">
            <!-- Paper Overview -->
            <section id="overview" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    论文概述
                </h2>
                
                <div class="highlight-box">
                    <h3>研究背景</h3>
                    <p>随着大语言模型(LLMs)的快速发展，一类新的自主AI系统——<strong>深度研究(Deep Research, DR)智能体</strong>应运而生。这些智能体能够处理复杂的多轮信息研究任务，通过动态推理、自适应长期规划、多跳信息检索、迭代工具使用和结构化分析报告生成来完成研究工作。</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">121</span>
                        <span>参考文献</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">7</span>
                        <span>主要章节</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">6</span>
                        <span>工业应用案例</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">5</span>
                        <span>核心技术组件</span>
                    </div>
                </div>

                <h3>主要贡献</h3>
                <div class="tech-grid">
                    <div class="tech-card">
                        <h4>🔍 系统性分析</h4>
                        <p>对代表性DR系统进行全面分析，包括系统架构、检索机制、工具调用方法和性能特征</p>
                    </div>
                    <div class="tech-card">
                        <h4>📊 统一分类框架</h4>
                        <p>基于工作流特征、规划策略和智能体架构的系统分类框架</p>
                    </div>
                    <div class="tech-card">
                        <h4>📈 基准评估</h4>
                        <p>对现有评估基准的系统回顾和分类，突出关键能力评估</p>
                    </div>
                    <div class="tech-card">
                        <h4>🚀 未来方向</h4>
                        <p>关键挑战和研究方向的系统分析，聚焦技术突破点</p>
                    </div>
                </div>
            </section>

            <!-- Core Definition -->
            <section id="definition" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                    </svg>
                    Deep Research Agents 核心定义
                </h2>

                <div class="highlight-box">
                    <h3>正式定义</h3>
                    <p><strong>Deep Research Agents</strong>是由大语言模型驱动的AI智能体，集成了动态推理、自适应规划、多迭代外部数据检索和工具使用，以及用于信息研究任务的综合分析报告生成能力。</p>
                </div>

                <h3>与传统方法的对比</h3>
                <div class="architecture-diagram">
                    <svg class="svg-diagram" viewBox="0 0 1000 400" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="ragGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:0.8" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:0.8" />
                            </linearGradient>
                            <linearGradient id="tuGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fdcb6e;stop-opacity:0.8" />
                                <stop offset="100%" style="stop-color:#e17055;stop-opacity:0.8" />
                            </linearGradient>
                            <linearGradient id="drGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00b894;stop-opacity:0.8" />
                                <stop offset="100%" style="stop-color:#00a085;stop-opacity:0.8" />
                            </linearGradient>
                            <marker id="comparisonArrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                                <path d="M0,0 L0,6 L9,3 z" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Traditional RAG -->
                        <rect x="50" y="50" width="250" height="120" fill="url(#ragGradient)" stroke="#74b9ff" stroke-width="2" rx="10"/>
                        <text x="175" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">传统RAG方法</text>
                        <text x="70" y="105" font-size="12" fill="white">• 静态检索流水线</text>
                        <text x="70" y="125" font-size="12" fill="white">• 固定工作流程</text>
                        <text x="70" y="145" font-size="12" fill="white">• 有限推理能力</text>

                        <!-- Tool Use Systems -->
                        <rect x="50" y="200" width="250" height="120" fill="url(#tuGradient)" stroke="#fdcb6e" stroke-width="2" rx="10"/>
                        <text x="175" y="230" text-anchor="middle" font-size="16" font-weight="bold" fill="white">传统工具使用系统</text>
                        <text x="70" y="255" font-size="12" fill="white">• 预定义工作流</text>
                        <text x="70" y="275" font-size="12" fill="white">• 静态工具调用</text>
                        <text x="70" y="295" font-size="12" fill="white">• 有限自适应性</text>

                        <!-- Evolution Arrow -->
                        <path d="M 320 180 Q 400 180 480 180" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#comparisonArrow)"/>
                        <text x="400" y="170" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">技术演进</text>

                        <!-- Deep Research Agents -->
                        <rect x="500" y="100" width="400" height="200" fill="url(#drGradient)" stroke="#00b894" stroke-width="2" rx="10"/>
                        <text x="700" y="130" text-anchor="middle" font-size="18" font-weight="bold" fill="white">Deep Research Agents</text>

                        <!-- DR Features -->
                        <rect x="520" y="150" width="160" height="60" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
                        <text x="600" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">动态推理</text>
                        <text x="600" y="185" text-anchor="middle" font-size="10" fill="white">自适应规划</text>
                        <text x="600" y="200" text-anchor="middle" font-size="10" fill="white">实时交互</text>

                        <rect x="700" y="150" width="160" height="60" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
                        <text x="780" y="170" text-anchor="middle" font-size="11" font-weight="bold" fill="white">多跳检索</text>
                        <text x="780" y="185" text-anchor="middle" font-size="10" fill="white">迭代工具使用</text>
                        <text x="780" y="200" text-anchor="middle" font-size="10" fill="white">结构化报告</text>

                        <rect x="520" y="230" width="160" height="60" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
                        <text x="600" y="250" text-anchor="middle" font-size="11" font-weight="bold" fill="white">长期记忆</text>
                        <text x="600" y="265" text-anchor="middle" font-size="10" fill="white">上下文管理</text>
                        <text x="600" y="280" text-anchor="middle" font-size="10" fill="white">持续学习</text>

                        <rect x="700" y="230" width="160" height="60" fill="#00b894" stroke="#00a085" stroke-width="1" rx="5"/>
                        <text x="780" y="250" text-anchor="middle" font-size="11" font-weight="bold" fill="white">多模态处理</text>
                        <text x="780" y="265" text-anchor="middle" font-size="10" fill="white">代码执行</text>
                        <text x="780" y="280" text-anchor="middle" font-size="10" fill="white">数据分析</text>
                    </svg>
                </div>

                <h3>核心特征对比</h3>
                <div class="code-block">
// DR Agents vs 传统方法对比矩阵
const comparisonMatrix = {
  "传统RAG": {
    workflow: "静态流水线",
    reasoning: "单步推理",
    retrieval: "固定检索",
    tools: "有限工具",
    adaptability: "低"
  },
  "传统工具使用": {
    workflow: "预定义流程",
    reasoning: "规则驱动",
    retrieval: "静态数据",
    tools: "预设工具集",
    adaptability: "中等"
  },
  "Deep Research Agents": {
    workflow: "动态自适应",
    reasoning: "多步深度推理",
    retrieval: "多跳迭代检索",
    tools: "扩展工具生态",
    adaptability: "高度自适应"
  }
};
                </div>
            </section>

            <!-- System Architecture -->
            <section id="architecture" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    Deep Research Agents 系统架构
                </h2>

                <div class="highlight-box">
                    <h3>架构概览</h3>
                    <p>DR Agents采用分层架构设计，包含用户交互层、智能体核心层、工具执行层和外部资源层，通过动态工作流实现复杂研究任务的自动化处理。</p>
                </div>

                <div class="architecture-diagram">
                    <svg class="svg-diagram" viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="userLayer" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="agentLayer" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="toolLayer" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00b894;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#00a085;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="resourceLayer" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fd79a8;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#e84393;stop-opacity:0.9" />
                            </linearGradient>
                            <marker id="archArrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                                <path d="M0,0 L0,6 L9,3 z" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- User Interaction Layer -->
                        <rect x="50" y="50" width="900" height="100" fill="url(#userLayer)" stroke="#6c5ce7" stroke-width="2" rx="15"/>
                        <text x="500" y="85" text-anchor="middle" font-size="20" font-weight="bold" fill="white">用户交互层 (User Interaction Layer)</text>

                        <!-- User Interface Components -->
                        <rect x="100" y="100" width="150" height="40" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="175" y="125" text-anchor="middle" font-size="12" fill="white">自然语言查询</text>

                        <rect x="300" y="100" width="150" height="40" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="375" y="125" text-anchor="middle" font-size="12" fill="white">意图澄清</text>

                        <rect x="500" y="100" width="150" height="40" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="575" y="125" text-anchor="middle" font-size="12" fill="white">结果展示</text>

                        <rect x="700" y="100" width="150" height="40" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="775" y="125" text-anchor="middle" font-size="12" fill="white">交互反馈</text>

                        <!-- Arrow to Agent Layer -->
                        <line x1="500" y1="150" x2="500" y2="180" stroke="#2c3e50" stroke-width="3" marker-end="url(#archArrow)"/>

                        <!-- Agent Core Layer -->
                        <rect x="50" y="190" width="900" height="150" fill="url(#agentLayer)" stroke="#74b9ff" stroke-width="2" rx="15"/>
                        <text x="500" y="225" text-anchor="middle" font-size="20" font-weight="bold" fill="white">智能体核心层 (Agent Core Layer)</text>

                        <!-- Core Components -->
                        <rect x="100" y="250" width="120" height="70" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="160" y="275" text-anchor="middle" font-size="11" font-weight="bold" fill="white">动态规划器</text>
                        <text x="160" y="290" text-anchor="middle" font-size="9" fill="white">任务分解</text>
                        <text x="160" y="305" text-anchor="middle" font-size="9" fill="white">策略生成</text>

                        <rect x="250" y="250" width="120" height="70" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="310" y="275" text-anchor="middle" font-size="11" font-weight="bold" fill="white">推理引擎</text>
                        <text x="310" y="290" text-anchor="middle" font-size="9" fill="white">多步推理</text>
                        <text x="310" y="305" text-anchor="middle" font-size="9" fill="white">逻辑验证</text>

                        <rect x="400" y="250" width="120" height="70" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="460" y="275" text-anchor="middle" font-size="11" font-weight="bold" fill="white">记忆管理</text>
                        <text x="460" y="290" text-anchor="middle" font-size="9" fill="white">上下文维护</text>
                        <text x="460" y="305" text-anchor="middle" font-size="9" fill="white">长期记忆</text>

                        <rect x="550" y="250" width="120" height="70" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="610" y="275" text-anchor="middle" font-size="11" font-weight="bold" fill="white">协调控制</text>
                        <text x="610" y="290" text-anchor="middle" font-size="9" fill="white">多智能体</text>
                        <text x="610" y="305" text-anchor="middle" font-size="9" fill="white">任务调度</text>

                        <rect x="700" y="250" width="120" height="70" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="760" y="275" text-anchor="middle" font-size="11" font-weight="bold" fill="white">报告生成</text>
                        <text x="760" y="290" text-anchor="middle" font-size="9" fill="white">结构化输出</text>
                        <text x="760" y="305" text-anchor="middle" font-size="9" fill="white">内容整合</text>

                        <!-- Arrow to Tool Layer -->
                        <line x1="500" y1="340" x2="500" y2="370" stroke="#2c3e50" stroke-width="3" marker-end="url(#archArrow)"/>

                        <!-- Tool Execution Layer -->
                        <rect x="50" y="380" width="900" height="150" fill="url(#toolLayer)" stroke="#00b894" stroke-width="2" rx="15"/>
                        <text x="500" y="415" text-anchor="middle" font-size="20" font-weight="bold" fill="white">工具执行层 (Tool Execution Layer)</text>

                        <!-- Tool Components -->
                        <rect x="80" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="130" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">搜索引擎</text>
                        <text x="130" y="480" text-anchor="middle" font-size="8" fill="white">API/浏览器</text>
                        <text x="130" y="495" text-anchor="middle" font-size="8" fill="white">信息检索</text>

                        <rect x="200" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="250" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">代码执行</text>
                        <text x="250" y="480" text-anchor="middle" font-size="8" fill="white">Python/R</text>
                        <text x="250" y="495" text-anchor="middle" font-size="8" fill="white">数据处理</text>

                        <rect x="320" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="370" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">数据分析</text>
                        <text x="370" y="480" text-anchor="middle" font-size="8" fill="white">统计分析</text>
                        <text x="370" y="495" text-anchor="middle" font-size="8" fill="white">可视化</text>

                        <rect x="440" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="490" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">多模态</text>
                        <text x="490" y="480" text-anchor="middle" font-size="8" fill="white">图像/音频</text>
                        <text x="490" y="495" text-anchor="middle" font-size="8" fill="white">内容生成</text>

                        <rect x="560" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="610" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">文件操作</text>
                        <text x="610" y="480" text-anchor="middle" font-size="8" fill="white">读写处理</text>
                        <text x="610" y="495" text-anchor="middle" font-size="8" fill="white">格式转换</text>

                        <rect x="680" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="730" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">计算机使用</text>
                        <text x="730" y="480" text-anchor="middle" font-size="8" fill="white">GUI操作</text>
                        <text x="730" y="495" text-anchor="middle" font-size="8" fill="white">自动化</text>

                        <rect x="800" y="440" width="100" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="850" y="465" text-anchor="middle" font-size="10" font-weight="bold" fill="white">MCP协议</text>
                        <text x="850" y="480" text-anchor="middle" font-size="8" fill="white">标准接口</text>
                        <text x="850" y="495" text-anchor="middle" font-size="8" fill="white">扩展性</text>

                        <!-- Arrow to Resource Layer -->
                        <line x1="500" y1="530" x2="500" y2="560" stroke="#2c3e50" stroke-width="3" marker-end="url(#archArrow)"/>

                        <!-- External Resource Layer -->
                        <rect x="50" y="570" width="900" height="100" fill="url(#resourceLayer)" stroke="#fd79a8" stroke-width="2" rx="15"/>
                        <text x="500" y="605" text-anchor="middle" font-size="20" font-weight="bold" fill="white">外部资源层 (External Resource Layer)</text>

                        <!-- Resource Components -->
                        <rect x="100" y="620" width="120" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="8"/>
                        <text x="160" y="645" text-anchor="middle" font-size="11" fill="white">Web搜索引擎</text>

                        <rect x="240" y="620" width="120" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="8"/>
                        <text x="300" y="645" text-anchor="middle" font-size="11" fill="white">学术数据库</text>

                        <rect x="380" y="620" width="120" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="8"/>
                        <text x="440" y="645" text-anchor="middle" font-size="11" fill="white">API服务</text>

                        <rect x="520" y="620" width="120" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="8"/>
                        <text x="580" y="645" text-anchor="middle" font-size="11" fill="white">知识图谱</text>

                        <rect x="660" y="620" width="120" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="8"/>
                        <text x="720" y="645" text-anchor="middle" font-size="11" fill="white">文档库</text>

                        <rect x="800" y="620" width="120" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="8"/>
                        <text x="860" y="645" text-anchor="middle" font-size="11" fill="white">云计算平台</text>
                    </svg>
                </div>
            </section>

            <!-- Core Components -->
            <section id="components" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
                    </svg>
                    核心技术组件深度解析
                </h2>

                <h3>1. 搜索引擎集成：API vs 浏览器</h3>
                <div class="architecture-diagram">
                    <svg class="svg-diagram" viewBox="0 0 900 400" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="apiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="browserGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00b894;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#00a085;stop-opacity:0.9" />
                            </linearGradient>
                            <marker id="searchArrow" markerWidth="8" markerHeight="8" refX="7" refY="3" orient="auto">
                                <path d="M0,0 L0,6 L7,3 z" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- API-Based Search -->
                        <rect x="50" y="50" width="350" height="280" fill="url(#apiGradient)" stroke="#74b9ff" stroke-width="2" rx="15"/>
                        <text x="225" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">API-Based 搜索</text>

                        <!-- API Features -->
                        <rect x="80" y="110" width="120" height="50" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="140" y="130" text-anchor="middle" font-size="11" font-weight="bold" fill="white">结构化数据</text>
                        <text x="140" y="145" text-anchor="middle" font-size="9" fill="white">高效检索</text>

                        <rect x="220" y="110" width="120" height="50" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="280" y="130" text-anchor="middle" font-size="11" font-weight="bold" fill="white">标准化接口</text>
                        <text x="280" y="145" text-anchor="middle" font-size="9" fill="white">稳定可靠</text>

                        <rect x="80" y="180" width="120" height="50" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="140" y="200" text-anchor="middle" font-size="11" font-weight="bold" fill="white">快速响应</text>
                        <text x="140" y="215" text-anchor="middle" font-size="9" fill="white">低延迟</text>

                        <rect x="220" y="180" width="120" height="50" fill="#74b9ff" stroke="#0984e3" stroke-width="1" rx="8"/>
                        <text x="280" y="200" text-anchor="middle" font-size="11" font-weight="bold" fill="white">批量处理</text>
                        <text x="280" y="215" text-anchor="middle" font-size="9" fill="white">高吞吐量</text>

                        <!-- API Limitations -->
                        <text x="225" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="white">局限性</text>
                        <text x="225" y="280" text-anchor="middle" font-size="10" fill="white">• 内容覆盖有限</text>
                        <text x="225" y="295" text-anchor="middle" font-size="10" fill="white">• 更新频率受限</text>
                        <text x="225" y="310" text-anchor="middle" font-size="10" fill="white">• 动态内容获取困难</text>

                        <!-- VS -->
                        <text x="450" y="200" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">VS</text>

                        <!-- Browser-Based Search -->
                        <rect x="500" y="50" width="350" height="280" fill="url(#browserGradient)" stroke="#00b894" stroke-width="2" rx="15"/>
                        <text x="675" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">Browser-Based 搜索</text>

                        <!-- Browser Features -->
                        <rect x="530" y="110" width="120" height="50" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="590" y="130" text-anchor="middle" font-size="11" font-weight="bold" fill="white">动态内容</text>
                        <text x="590" y="145" text-anchor="middle" font-size="9" fill="white">实时获取</text>

                        <rect x="670" y="110" width="120" height="50" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="730" y="130" text-anchor="middle" font-size="11" font-weight="bold" fill="white">全面覆盖</text>
                        <text x="730" y="145" text-anchor="middle" font-size="9" fill="white">无限制访问</text>

                        <rect x="530" y="180" width="120" height="50" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="590" y="200" text-anchor="middle" font-size="11" font-weight="bold" fill="white">交互式操作</text>
                        <text x="590" y="215" text-anchor="middle" font-size="9" fill="white">模拟人类</text>

                        <rect x="670" y="180" width="120" height="50" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="730" y="200" text-anchor="middle" font-size="11" font-weight="bold" fill="white">多媒体处理</text>
                        <text x="730" y="215" text-anchor="middle" font-size="9" fill="white">图像/视频</text>

                        <!-- Browser Limitations -->
                        <text x="675" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="white">局限性</text>
                        <text x="675" y="280" text-anchor="middle" font-size="10" fill="white">• 响应速度较慢</text>
                        <text x="675" y="295" text-anchor="middle" font-size="10" fill="white">• 资源消耗较高</text>
                        <text x="675" y="310" text-anchor="middle" font-size="10" fill="white">• 稳定性挑战</text>
                    </svg>
                </div>

                <h3>2. 工具使用能力扩展</h3>
                <div class="tech-grid">
                    <div class="tech-card">
                        <h4>💻 代码解释器</h4>
                        <ul>
                            <li>Python/R代码执行</li>
                            <li>数据处理和分析</li>
                            <li>算法实现和验证</li>
                            <li>结果可视化</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>📊 数据分析工具</h4>
                        <ul>
                            <li>统计分析和建模</li>
                            <li>机器学习算法</li>
                            <li>数据挖掘技术</li>
                            <li>预测分析</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>🎨 多模态处理</h4>
                        <ul>
                            <li>图像生成和编辑</li>
                            <li>音频处理</li>
                            <li>视频分析</li>
                            <li>文档理解</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>🖥️ 计算机使用</h4>
                        <ul>
                            <li>GUI自动化操作</li>
                            <li>应用程序控制</li>
                            <li>文件系统管理</li>
                            <li>系统监控</li>
                        </ul>
                    </div>
                </div>

                <h3>3. 工作流架构分类</h3>
                <div class="architecture-diagram">
                    <svg class="svg-diagram" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="staticGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fd79a8;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#e84393;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="dynamicGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:0.9" />
                            </linearGradient>
                            <marker id="workflowArrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                                <path d="M0,0 L0,6 L9,3 z" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Static Workflow -->
                        <rect x="50" y="50" width="400" height="180" fill="url(#staticGradient)" stroke="#fd79a8" stroke-width="2" rx="15"/>
                        <text x="250" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">静态工作流 (Static Workflow)</text>

                        <!-- Static Flow Steps -->
                        <rect x="80" y="110" width="80" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="5"/>
                        <text x="120" y="135" text-anchor="middle" font-size="10" fill="white">输入解析</text>

                        <line x1="160" y1="130" x2="190" y2="130" stroke="white" stroke-width="2" marker-end="url(#workflowArrow)"/>

                        <rect x="190" y="110" width="80" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="5"/>
                        <text x="230" y="135" text-anchor="middle" font-size="10" fill="white">信息检索</text>

                        <line x1="270" y1="130" x2="300" y2="130" stroke="white" stroke-width="2" marker-end="url(#workflowArrow)"/>

                        <rect x="300" y="110" width="80" height="40" fill="#fd79a8" stroke="#e84393" stroke-width="1" rx="5"/>
                        <text x="340" y="135" text-anchor="middle" font-size="10" fill="white">结果生成</text>

                        <!-- Static Characteristics -->
                        <text x="250" y="180" text-anchor="middle" font-size="12" font-weight="bold" fill="white">特征</text>
                        <text x="250" y="200" text-anchor="middle" font-size="10" fill="white">• 预定义流程</text>
                        <text x="250" y="215" text-anchor="middle" font-size="10" fill="white">• 固定步骤序列</text>

                        <!-- Dynamic Workflow -->
                        <rect x="550" y="50" width="400" height="400" fill="url(#dynamicGradient)" stroke="#6c5ce7" stroke-width="2" rx="15"/>
                        <text x="750" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">动态工作流 (Dynamic Workflow)</text>

                        <!-- Dynamic Planning Strategies -->
                        <text x="750" y="120" text-anchor="middle" font-size="14" font-weight="bold" fill="white">规划策略</text>

                        <!-- ReAct -->
                        <rect x="580" y="140" width="120" height="60" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="640" y="160" text-anchor="middle" font-size="11" font-weight="bold" fill="white">ReAct</text>
                        <text x="640" y="175" text-anchor="middle" font-size="9" fill="white">推理-行动</text>
                        <text x="640" y="190" text-anchor="middle" font-size="9" fill="white">循环模式</text>

                        <!-- Tree of Thoughts -->
                        <rect x="720" y="140" width="120" height="60" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="780" y="160" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Tree of Thoughts</text>
                        <text x="780" y="175" text-anchor="middle" font-size="9" fill="white">思维树</text>
                        <text x="780" y="190" text-anchor="middle" font-size="9" fill="white">分支探索</text>

                        <!-- Agent Architectures -->
                        <text x="750" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="white">智能体架构</text>

                        <!-- Single Agent -->
                        <rect x="580" y="260" width="120" height="60" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="640" y="280" text-anchor="middle" font-size="11" font-weight="bold" fill="white">单智能体</text>
                        <text x="640" y="295" text-anchor="middle" font-size="9" fill="white">集中式控制</text>
                        <text x="640" y="310" text-anchor="middle" font-size="9" fill="white">简化协调</text>

                        <!-- Multi Agent -->
                        <rect x="720" y="260" width="120" height="60" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="780" y="280" text-anchor="middle" font-size="11" font-weight="bold" fill="white">多智能体</text>
                        <text x="780" y="295" text-anchor="middle" font-size="9" fill="white">分布式协作</text>
                        <text x="780" y="310" text-anchor="middle" font-size="9" fill="white">专业化分工</text>

                        <!-- Memory Mechanism -->
                        <text x="750" y="360" text-anchor="middle" font-size="14" font-weight="bold" fill="white">记忆机制</text>

                        <rect x="580" y="380" width="260" height="50" fill="#6c5ce7" stroke="#5f3dc4" stroke-width="1" rx="8"/>
                        <text x="710" y="400" text-anchor="middle" font-size="11" font-weight="bold" fill="white">长上下文优化</text>
                        <text x="710" y="415" text-anchor="middle" font-size="9" fill="white">• 分层记忆 • 上下文压缩 • 检索增强</text>

                        <!-- Evolution Arrow -->
                        <path d="M 470 140 Q 520 140 550 140" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#workflowArrow)"/>
                        <text x="510" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">演进</text>
                    </svg>
                </div>

                <h3>4. 优化调优方法</h3>
                <div class="tech-grid">
                    <div class="tech-card">
                        <h4>🎯 监督微调 (SFT)</h4>
                        <ul>
                            <li>任务特定数据训练</li>
                            <li>指令跟随能力提升</li>
                            <li>领域知识注入</li>
                            <li>性能基准优化</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>🔄 强化学习优化</h4>
                        <ul>
                            <li>RLHF人类反馈学习</li>
                            <li>PPO策略优化</li>
                            <li>奖励模型设计</li>
                            <li>在线学习适应</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>📚 非参数持续学习</h4>
                        <ul>
                            <li>外部知识库更新</li>
                            <li>工具能力扩展</li>
                            <li>工作流自适应</li>
                            <li>经验积累机制</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>⚡ 提示工程优化</h4>
                        <ul>
                            <li>Chain-of-Thought</li>
                            <li>Few-shot学习</li>
                            <li>模板设计优化</li>
                            <li>上下文工程</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Industrial Applications -->
            <section id="applications" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                    </svg>
                    工业应用案例分析
                </h2>

                <div class="highlight-box">
                    <h3>主要厂商DR系统对比</h3>
                    <p>当前主流科技公司都推出了自己的Deep Research Agent系统，各具特色和优势，代表了该领域的最新技术水平。</p>
                </div>

                <div class="architecture-diagram">
                    <svg class="svg-diagram" viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="openaiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="googleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00b894;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#00a085;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="perplexityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fd79a8;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#e84393;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="xaiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="microsoftGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fdcb6e;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#e17055;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="qwenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#2d3436;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#636e72;stop-opacity:0.9" />
                            </linearGradient>
                        </defs>

                        <!-- OpenAI Deep Research -->
                        <rect x="50" y="50" width="280" height="120" fill="url(#openaiGradient)" stroke="#74b9ff" stroke-width="2" rx="10"/>
                        <text x="190" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">OpenAI Deep Research</text>
                        <text x="70" y="105" font-size="11" fill="white">• GPT-4o集成</text>
                        <text x="70" y="120" font-size="11" fill="white">• 多步推理优化</text>
                        <text x="70" y="135" font-size="11" fill="white">• 工具链整合</text>
                        <text x="70" y="150" font-size="11" fill="white">• 结构化报告生成</text>

                        <!-- Gemini Deep Research -->
                        <rect x="360" y="50" width="280" height="120" fill="url(#googleGradient)" stroke="#00b894" stroke-width="2" rx="10"/>
                        <text x="500" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Gemini Deep Research</text>
                        <text x="380" y="105" font-size="11" fill="white">• Gemini 2.0 Flash</text>
                        <text x="380" y="120" font-size="11" fill="white">• 多模态处理</text>
                        <text x="380" y="135" font-size="11" fill="white">• 实时搜索集成</text>
                        <text x="380" y="150" font-size="11" fill="white">• 协作式研究</text>

                        <!-- Perplexity Deep Research -->
                        <rect x="670" y="50" width="280" height="120" fill="url(#perplexityGradient)" stroke="#fd79a8" stroke-width="2" rx="10"/>
                        <text x="810" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Perplexity Deep Research</text>
                        <text x="690" y="105" font-size="11" fill="white">• 专业搜索引擎</text>
                        <text x="690" y="120" font-size="11" fill="white">• 实时信息获取</text>
                        <text x="690" y="135" font-size="11" fill="white">• 引用追踪</text>
                        <text x="690" y="150" font-size="11" fill="white">• 学术研究优化</text>

                        <!-- Grok DeepSearch -->
                        <rect x="50" y="200" width="280" height="120" fill="url(#xaiGradient)" stroke="#6c5ce7" stroke-width="2" rx="10"/>
                        <text x="190" y="230" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Grok DeepSearch</text>
                        <text x="70" y="255" font-size="11" fill="white">• X平台数据集成</text>
                        <text x="70" y="270" font-size="11" fill="white">• 实时社交媒体</text>
                        <text x="70" y="285" font-size="11" fill="white">• 趋势分析</text>
                        <text x="70" y="300" font-size="11" fill="white">• 多语言支持</text>

                        <!-- Microsoft Copilot -->
                        <rect x="360" y="200" width="280" height="120" fill="url(#microsoftGradient)" stroke="#fdcb6e" stroke-width="2" rx="10"/>
                        <text x="500" y="230" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Microsoft Copilot</text>
                        <text x="380" y="255" font-size="11" fill="white">• Office 365集成</text>
                        <text x="380" y="270" font-size="11" fill="white">• 企业数据访问</text>
                        <text x="380" y="285" font-size="11" fill="white">• 协作工作流</text>
                        <text x="380" y="300" font-size="11" fill="white">• 安全合规</text>

                        <!-- Qwen Deep Research -->
                        <rect x="670" y="200" width="280" height="120" fill="url(#qwenGradient)" stroke="#2d3436" stroke-width="2" rx="10"/>
                        <text x="810" y="230" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Qwen Deep Research</text>
                        <text x="690" y="255" font-size="11" fill="white">• 中文优化</text>
                        <text x="690" y="270" font-size="11" fill="white">• 本土化数据源</text>
                        <text x="690" y="285" font-size="11" fill="white">• 多模态能力</text>
                        <text x="690" y="300" font-size="11" fill="white">• 开源生态</text>

                        <!-- Comparison Matrix -->
                        <rect x="50" y="350" width="900" height="200" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="10"/>
                        <text x="500" y="380" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">技术特征对比矩阵</text>

                        <!-- Matrix Headers -->
                        <text x="100" y="410" font-size="12" font-weight="bold" fill="#2c3e50">系统</text>
                        <text x="250" y="410" font-size="12" font-weight="bold" fill="#2c3e50">核心模型</text>
                        <text x="400" y="410" font-size="12" font-weight="bold" fill="#2c3e50">特色功能</text>
                        <text x="600" y="410" font-size="12" font-weight="bold" fill="#2c3e50">应用场景</text>
                        <text x="800" y="410" font-size="12" font-weight="bold" fill="#2c3e50">优势</text>

                        <!-- Matrix Content -->
                        <text x="100" y="435" font-size="10" fill="#2c3e50">OpenAI DR</text>
                        <text x="250" y="435" font-size="10" fill="#2c3e50">GPT-4o</text>
                        <text x="400" y="435" font-size="10" fill="#2c3e50">多步推理</text>
                        <text x="600" y="435" font-size="10" fill="#2c3e50">通用研究</text>
                        <text x="800" y="435" font-size="10" fill="#2c3e50">推理能力强</text>

                        <text x="100" y="455" font-size="10" fill="#2c3e50">Gemini DR</text>
                        <text x="250" y="455" font-size="10" fill="#2c3e50">Gemini 2.0</text>
                        <text x="400" y="455" font-size="10" fill="#2c3e50">多模态</text>
                        <text x="600" y="455" font-size="10" fill="#2c3e50">多媒体研究</text>
                        <text x="800" y="455" font-size="10" fill="#2c3e50">模态融合</text>

                        <text x="100" y="475" font-size="10" fill="#2c3e50">Perplexity</text>
                        <text x="250" y="475" font-size="10" fill="#2c3e50">专有模型</text>
                        <text x="400" y="475" font-size="10" fill="#2c3e50">搜索优化</text>
                        <text x="600" y="475" font-size="10" fill="#2c3e50">学术研究</text>
                        <text x="800" y="475" font-size="10" fill="#2c3e50">信息准确</text>

                        <text x="100" y="495" font-size="10" fill="#2c3e50">Grok</text>
                        <text x="250" y="495" font-size="10" fill="#2c3e50">Grok-3</text>
                        <text x="400" y="495" font-size="10" fill="#2c3e50">社交数据</text>
                        <text x="600" y="495" font-size="10" fill="#2c3e50">趋势分析</text>
                        <text x="800" y="495" font-size="10" fill="#2c3e50">实时性强</text>

                        <text x="100" y="515" font-size="10" fill="#2c3e50">Microsoft</text>
                        <text x="250" y="515" font-size="10" fill="#2c3e50">GPT-4</text>
                        <text x="400" y="515" font-size="10" fill="#2c3e50">企业集成</text>
                        <text x="600" y="515" font-size="10" fill="#2c3e50">办公协作</text>
                        <text x="800" y="515" font-size="10" fill="#2c3e50">企业安全</text>

                        <text x="100" y="535" font-size="10" fill="#2c3e50">Qwen</text>
                        <text x="250" y="535" font-size="10" fill="#2c3e50">Qwen-Max</text>
                        <text x="400" y="535" font-size="10" fill="#2c3e50">中文优化</text>
                        <text x="600" y="535" font-size="10" fill="#2c3e50">本土化</text>
                        <text x="800" y="535" font-size="10" fill="#2c3e50">开源生态</text>
                    </svg>
                </div>

                <h3>应用场景分析</h3>
                <div class="tech-grid">
                    <div class="tech-card">
                        <h4>🎓 学术研究</h4>
                        <ul>
                            <li>文献综述自动化</li>
                            <li>研究假设生成</li>
                            <li>数据分析和可视化</li>
                            <li>论文写作辅助</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>💼 商业分析</h4>
                        <ul>
                            <li>市场调研报告</li>
                            <li>竞争对手分析</li>
                            <li>投资决策支持</li>
                            <li>风险评估</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>📰 新闻媒体</h4>
                        <ul>
                            <li>深度调查报道</li>
                            <li>事实核查验证</li>
                            <li>趋势分析预测</li>
                            <li>多源信息整合</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>🏛️ 政策制定</h4>
                        <ul>
                            <li>政策影响评估</li>
                            <li>公众意见分析</li>
                            <li>法规合规检查</li>
                            <li>决策支持系统</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Benchmarks -->
            <section id="benchmarks" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                    </svg>
                    评估基准与性能指标
                </h2>

                <div class="highlight-box">
                    <h3>评估维度</h3>
                    <p>DR Agents的评估需要从多个维度进行，包括信息检索准确性、推理深度、工具使用效率和最终输出质量等关键指标。</p>
                </div>

                <h3>基准测试分类</h3>
                <div class="tech-grid">
                    <div class="tech-card">
                        <h4>❓ 问答基准 (QA Benchmarks)</h4>
                        <ul>
                            <li><strong>Natural Questions</strong> - 真实用户查询</li>
                            <li><strong>HotpotQA</strong> - 多跳推理问答</li>
                            <li><strong>TriviaQA</strong> - 事实性知识问答</li>
                            <li><strong>ELI5</strong> - 长形式解释生成</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>⚙️ 任务执行基准</h4>
                        <ul>
                            <li><strong>WebArena</strong> - Web环境任务</li>
                            <li><strong>SWE-bench</strong> - 软件工程任务</li>
                            <li><strong>GAIA</strong> - 通用AI助手评估</li>
                            <li><strong>MLAgentBench</strong> - 机器学习实验</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>🔬 研究专用基准</h4>
                        <ul>
                            <li><strong>ScienceAgentBench</strong> - 科学发现</li>
                            <li><strong>ResearchTown</strong> - 研究社区模拟</li>
                            <li><strong>RE-Bench</strong> - R&D能力评估</li>
                            <li><strong>MLE-Bench</strong> - 机器学习工程</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>📱 多模态基准</h4>
                        <ul>
                            <li><strong>OSWorld</strong> - 操作系统环境</li>
                            <li><strong>Mobile-Agent</strong> - 移动设备操作</li>
                            <li><strong>BrowseComp</strong> - 浏览器操作</li>
                            <li><strong>AssistantBench</strong> - 助手任务</li>
                        </ul>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">15+</span>
                        <span>主要评估基准</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">4</span>
                        <span>评估维度</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">85%</span>
                        <span>平均准确率</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">3.2x</span>
                        <span>效率提升</span>
                    </div>
                </div>
            </section>

            <!-- Challenges and Future Directions -->
            <section id="challenges" class="section">
                <h2>
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                    </svg>
                    挑战与未来发展方向
                </h2>

                <div class="highlight-box">
                    <h3>当前面临的主要挑战</h3>
                    <p>尽管DR Agents取得了显著进展，但在信息源扩展、事实核查、并行执行、工具集成推理等方面仍面临重要挑战，需要持续的技术创新和突破。</p>
                </div>

                <div class="architecture-diagram">
                    <svg class="svg-diagram" viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="challengeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#e17055;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#d63031;stop-opacity:0.9" />
                            </linearGradient>
                            <linearGradient id="solutionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#00b894;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#00a085;stop-opacity:0.9" />
                            </linearGradient>
                            <marker id="challengeArrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                                <path d="M0,0 L0,6 L9,3 z" fill="#2c3e50"/>
                            </marker>
                        </defs>

                        <!-- Current Challenges -->
                        <rect x="50" y="50" width="400" height="550" fill="url(#challengeGradient)" stroke="#e17055" stroke-width="2" rx="15"/>
                        <text x="250" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">当前挑战</text>

                        <!-- Challenge 1: Information Sources -->
                        <rect x="80" y="110" width="340" height="70" fill="#e17055" stroke="#d63031" stroke-width="1" rx="8"/>
                        <text x="250" y="135" text-anchor="middle" font-size="13" font-weight="bold" fill="white">1. 信息源扩展</text>
                        <text x="100" y="155" font-size="10" fill="white">• 私有数据库访问限制</text>
                        <text x="100" y="170" font-size="10" fill="white">• 实时数据获取困难</text>

                        <!-- Challenge 2: Fact Checking -->
                        <rect x="80" y="200" width="340" height="70" fill="#e17055" stroke="#d63031" stroke-width="1" rx="8"/>
                        <text x="250" y="225" text-anchor="middle" font-size="13" font-weight="bold" fill="white">2. 事实核查</text>
                        <text x="100" y="245" font-size="10" fill="white">• 信息真实性验证</text>
                        <text x="100" y="260" font-size="10" fill="white">• 多源信息一致性检查</text>

                        <!-- Challenge 3: Parallel Execution -->
                        <rect x="80" y="290" width="340" height="70" fill="#e17055" stroke="#d63031" stroke-width="1" rx="8"/>
                        <text x="250" y="315" text-anchor="middle" font-size="13" font-weight="bold" fill="white">3. 异步并行执行</text>
                        <text x="100" y="335" font-size="10" fill="white">• 任务调度复杂性</text>
                        <text x="100" y="350" font-size="10" fill="white">• 资源竞争管理</text>

                        <!-- Challenge 4: Tool Integration -->
                        <rect x="80" y="380" width="340" height="70" fill="#e17055" stroke="#d63031" stroke-width="1" rx="8"/>
                        <text x="250" y="405" text-anchor="middle" font-size="13" font-weight="bold" fill="white">4. 工具集成推理</text>
                        <text x="100" y="425" font-size="10" fill="white">• 工具选择策略</text>
                        <text x="100" y="440" font-size="10" fill="white">• 结果整合困难</text>

                        <!-- Challenge 5: Benchmark Alignment -->
                        <rect x="80" y="470" width="340" height="70" fill="#e17055" stroke="#d63031" stroke-width="1" rx="8"/>
                        <text x="250" y="495" text-anchor="middle" font-size="13" font-weight="bold" fill="white">5. 基准对齐</text>
                        <text x="100" y="515" font-size="10" fill="white">• 评估指标不统一</text>
                        <text x="100" y="530" font-size="10" fill="white">• 实际应用差距</text>

                        <!-- Arrow to Solutions -->
                        <path d="M 470 350 Q 520 350 550 350" stroke="#2c3e50" stroke-width="4" fill="none" marker-end="url(#challengeArrow)"/>
                        <text x="510" y="340" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">解决方案</text>

                        <!-- Future Solutions -->
                        <rect x="550" y="50" width="400" height="550" fill="url(#solutionGradient)" stroke="#00b894" stroke-width="2" rx="15"/>
                        <text x="750" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">未来解决方案</text>

                        <!-- Solution 1: Broader Information -->
                        <rect x="580" y="110" width="340" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="750" y="135" text-anchor="middle" font-size="13" font-weight="bold" fill="white">扩展信息源</text>
                        <text x="600" y="155" font-size="10" fill="white">• 多模态数据集成</text>
                        <text x="600" y="170" font-size="10" fill="white">• 私有API标准化</text>

                        <!-- Solution 2: Advanced Verification -->
                        <rect x="580" y="200" width="340" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="750" y="225" text-anchor="middle" font-size="13" font-weight="bold" fill="white">智能事实核查</text>
                        <text x="600" y="245" font-size="10" fill="white">• 区块链验证机制</text>
                        <text x="600" y="260" font-size="10" fill="white">• 多源交叉验证</text>

                        <!-- Solution 3: Parallel Frameworks -->
                        <rect x="580" y="290" width="340" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="750" y="315" text-anchor="middle" font-size="13" font-weight="bold" fill="white">并行执行框架</text>
                        <text x="600" y="335" font-size="10" fill="white">• DAG-based调度</text>
                        <text x="600" y="350" font-size="10" fill="white">• 动态资源分配</text>

                        <!-- Solution 4: Integrated Reasoning -->
                        <rect x="580" y="380" width="340" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="750" y="405" text-anchor="middle" font-size="13" font-weight="bold" fill="white">集成推理系统</text>
                        <text x="600" y="425" font-size="10" fill="white">• 统一工具接口</text>
                        <text x="600" y="440" font-size="10" fill="white">• 智能结果融合</text>

                        <!-- Solution 5: Standardized Benchmarks -->
                        <rect x="580" y="470" width="340" height="70" fill="#00b894" stroke="#00a085" stroke-width="1" rx="8"/>
                        <text x="750" y="495" text-anchor="middle" font-size="13" font-weight="bold" fill="white">标准化基准</text>
                        <text x="600" y="515" font-size="10" fill="white">• 统一评估框架</text>
                        <text x="600" y="530" font-size="10" fill="white">• 实际场景测试</text>

                        <!-- Future Trends -->
                        <rect x="80" y="560" width="840" height="60" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="10"/>
                        <text x="500" y="585" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">未来发展趋势</text>
                        <text x="500" y="605" text-anchor="middle" font-size="12" fill="#2c3e50">自进化智能体 • 多智能体协作优化 • 端到端强化学习 • 模块化能力提供者</text>
                    </svg>
                </div>

                <h3>关键技术发展方向</h3>
                <div class="tech-grid">
                    <div class="tech-card">
                        <h4>🌐 信息源扩展</h4>
                        <ul>
                            <li>多模态数据源整合</li>
                            <li>实时数据流处理</li>
                            <li>私有数据库接入</li>
                            <li>跨语言信息获取</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>✅ 事实核查增强</h4>
                        <ul>
                            <li>多源信息交叉验证</li>
                            <li>可信度评分机制</li>
                            <li>实时事实检查</li>
                            <li>偏见检测与纠正</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>⚡ 并行执行优化</h4>
                        <ul>
                            <li>DAG-based任务调度</li>
                            <li>动态资源分配</li>
                            <li>异步工作流管理</li>
                            <li>负载均衡策略</li>
                        </ul>
                    </div>
                    <div class="tech-card">
                        <h4>🧠 自进化能力</h4>
                        <ul>
                            <li>经验学习机制</li>
                            <li>工作流自适应</li>
                            <li>工具能力扩展</li>
                            <li>知识图谱更新</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🔮 技术展望</h3>
                    <p>未来的Deep Research Agents将朝着更加智能化、自主化和协作化的方向发展，通过<strong>自进化学习</strong>、<strong>多智能体协作</strong>、<strong>端到端优化</strong>和<strong>模块化架构</strong>等技术突破，实现真正的通用人工智能研究助手。</p>
                </div>

                <div class="code-block">
// 未来DR Agents技术路线图
const futureRoadmap = {
  "短期目标 (1-2年)": {
    focus: "基础能力完善",
    targets: [
      "多模态信息处理优化",
      "工具集成标准化",
      "基准测试统一化",
      "并行执行效率提升"
    ]
  },
  "中期目标 (3-5年)": {
    focus: "智能化水平提升",
    targets: [
      "自适应工作流生成",
      "多智能体协作优化",
      "实时事实核查系统",
      "跨领域知识迁移"
    ]
  },
  "长期愿景 (5-10年)": {
    focus: "通用研究智能",
    targets: [
      "完全自主研究能力",
      "科学发现自动化",
      "人机协作研究范式",
      "知识创新生成系统"
    ]
  }
};
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div>
                <h3>Deep Research Agents: 系统性研究与技术路线图</h3>
                <p>基于大语言模型的深度研究智能体：架构、技术与应用前景</p>
                <p class="footer-info">
                    <strong>论文来源:</strong>
                    <a href="https://arxiv.org/html/2506.18096v1" class="footer-link">
                        arXiv:2506.18096v1
                    </a>
                </p>
                <p class="footer-info-small">
                    <strong>作者团队:</strong> Yuxuan Huang, Yihang Chen, Haozheng Zhang 等
                </p>
                <p class="footer-info-small">
                    <strong>报告生成时间:</strong> 2025年7月24日
                </p>
            </div>
        </footer>
    </div>

    <script>
        // 平滑滚动导航
        document.querySelectorAll('.nav a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 添加滚动时导航高亮效果
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.style.background = '';
                if (link.getAttribute('href') === '#' + current) {
                    link.style.background = '#34495e';
                }
            });
        });

        // 添加代码块复制功能
        document.querySelectorAll('.code-block').forEach(block => {
            const button = document.createElement('button');
            button.textContent = '复制代码';
            button.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #74b9ff;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
            `;

            block.style.position = 'relative';
            block.appendChild(button);

            button.addEventListener('click', () => {
                const text = block.textContent.replace('复制代码', '').trim();
                navigator.clipboard.writeText(text).then(() => {
                    button.textContent = '已复制!';
                    setTimeout(() => {
                        button.textContent = '复制代码';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
