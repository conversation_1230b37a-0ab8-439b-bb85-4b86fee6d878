{"registry_location": "/mnt/data/技能注册表.json", "core_flow": {"step_1_register": {"description": "技能注册：开发者在注册表中添加技能元数据", "input": ["技能ID", "技能名称", "类型", "来源GPT", "适用场景", "接口路径/知识来源/提示词模块"], "output": "技能注册成功"}, "step_2_inherit": {"description": "技能继承：新GPT通过配置声明引用已有技能", "input": ["目标GPT ID", "引用技能ID"], "output": "GPT能力拓展完成"}, "step_3_activate": {"description": "运行时激活：GPT自动判断任务是否需要调用技能", "trigger": "用户输入满足技能触发条件", "output": "执行技能模块"}, "step_4_invoke": {"description": "技能调用：根据注册信息完成实际调用", "mode": ["调用提示词模块", "调用向量知识库检索", "调用外部API"], "output": "技能执行结果返回给主模型整合处理"}}, "example_usage": {"GPT名称": "张五常GPT", "继承技能": ["zhangwu_analyzer"], "任务输入": "请分析这个制度安排中的产权设计问题", "触发行为": "激活张五常知识库 + 分析提示词模块", "结果输出": "以五常理论为框架，输出制度对照与改进建议"}}